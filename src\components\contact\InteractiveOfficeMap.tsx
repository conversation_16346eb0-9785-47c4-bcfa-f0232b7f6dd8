/**
 * Interactive Office Map Component
 * Bali Property Scout Website
 * 
 * Interactive map for office location in Canggu with static fallback.
 * Features dynamic import of map library for client-side interactivity.
 */

'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { cn } from '@/lib/utils';

// Office location data
const officeLocation = {
  name: 'Bali Property Scout Office',
  address: 'Jl. Pantai Berawa No. 123, Canggu, Badung Regency, Bali 80361, Indonesia',
  coordinates: {
    lat: -8.6481,
    lng: 115.1370
  },
  phone: '+62 ************',
  email: '<EMAIL>',
  hours: {
    weekdays: '9:00 AM - 6:00 PM',
    saturday: '9:00 AM - 4:00 PM',
    sunday: 'Closed'
  },
  description: 'Located in the vibrant heart of Canggu, close to popular cafes, coworking spaces, and just 5 minutes from the beach.'
};

// Static map fallback component
const StaticMapFallback: React.FC = () => {
  return (
    <div className="relative w-full h-96 bg-gray-200 rounded-lg overflow-hidden">
      <iframe
        src={`https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3944.2857142857144!2d${officeLocation.coordinates.lng}!3d${officeLocation.coordinates.lat}!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zOMKwMzgnNTMuMiJTIDExNcKwMDgnMTMuMiJF!5e0!3m2!1sen!2sus!4v1234567890123!5m2!1sen!2sus`}
        width="100%"
        height="100%"
        style={{ border: 0 }}
        allowFullScreen
        loading="lazy"
        referrerPolicy="no-referrer-when-downgrade"
        title="Bali Property Scout Office Location in Canggu"
      />
      
      {/* Overlay with office info */}
      <div className="absolute top-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg p-4 shadow-lg max-w-xs">
        <h4 className="font-semibold text-gray-900 mb-2">{officeLocation.name}</h4>
        <p className="text-sm text-gray-600 mb-2">{officeLocation.address}</p>
        <div className="flex items-center text-sm text-emerald-600">
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          Get Directions
        </div>
      </div>
    </div>
  );
};

// Loading component
const MapLoading: React.FC = () => {
  return (
    <div className="w-full h-96 bg-gray-100 rounded-lg flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading interactive map...</p>
      </div>
    </div>
  );
};

export interface InteractiveOfficeMapProps {
  className?: string;
}

export const InteractiveOfficeMap: React.FC<InteractiveOfficeMapProps> = ({ className }) => {
  const [isClient, setIsClient] = useState(false);
  const [mapError, setMapError] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // For now, we'll use the static map fallback
  // In a real implementation, you would dynamically import a map library like react-leaflet
  const handleGetDirections = () => {
    const googleMapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${officeLocation.coordinates.lat},${officeLocation.coordinates.lng}`;
    window.open(googleMapsUrl, '_blank');
  };

  const handleCallOffice = () => {
    window.open(`tel:${officeLocation.phone}`, '_self');
  };

  const handleEmailOffice = () => {
    window.open(`mailto:${officeLocation.email}`, '_self');
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Map Section */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="p-6 border-b border-gray-100">
          <h3 className="text-xl font-bold text-gray-900 mb-2">
            Visit Our Canggu Office
          </h3>
          <p className="text-gray-600">
            {officeLocation.description}
          </p>
        </div>

        <div className="relative">
          {isClient && !mapError ? (
            <Suspense fallback={<MapLoading />}>
              <StaticMapFallback />
            </Suspense>
          ) : (
            <StaticMapFallback />
          )}
        </div>
      </div>

      {/* Office Information Cards */}
      <div className="grid md:grid-cols-2 gap-6">
        {/* Contact Information */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
          <h4 className="text-lg font-bold text-gray-900 mb-4">Contact Information</h4>
          
          <div className="space-y-4">
            <div className="flex items-start">
              <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                <svg className="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <div>
                <h5 className="font-medium text-gray-900">Address</h5>
                <p className="text-sm text-gray-600 leading-relaxed">{officeLocation.address}</p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
              <div>
                <h5 className="font-medium text-gray-900">Phone</h5>
                <button
                  onClick={handleCallOffice}
                  className="text-sm text-blue-600 hover:text-blue-700 transition-colors"
                >
                  {officeLocation.phone}
                </button>
              </div>
            </div>

            <div className="flex items-start">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h5 className="font-medium text-gray-900">Email</h5>
                <button
                  onClick={handleEmailOffice}
                  className="text-sm text-purple-600 hover:text-purple-700 transition-colors"
                >
                  {officeLocation.email}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Office Hours */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
          <h4 className="text-lg font-bold text-gray-900 mb-4">Office Hours</h4>
          
          <div className="space-y-3">
            <div className="flex justify-between items-center py-2 border-b border-gray-100">
              <span className="text-gray-700 font-medium">Monday - Friday</span>
              <span className="text-gray-600">{officeLocation.hours.weekdays}</span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-100">
              <span className="text-gray-700 font-medium">Saturday</span>
              <span className="text-gray-600">{officeLocation.hours.saturday}</span>
            </div>
            <div className="flex justify-between items-center py-2">
              <span className="text-gray-700 font-medium">Sunday</span>
              <span className="text-red-600 font-medium">{officeLocation.hours.sunday}</span>
            </div>
          </div>

          <div className="mt-6 p-4 bg-emerald-50 rounded-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-emerald-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-sm text-emerald-800">
                <strong>Walk-ins welcome!</strong> Or schedule an appointment for guaranteed availability.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4">
        <button
          onClick={handleGetDirections}
          className="flex-1 bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-4 rounded-lg font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
          </svg>
          Get Directions
        </button>
        
        <button
          onClick={handleCallOffice}
          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-6 py-4 rounded-lg font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
          </svg>
          Call Office
        </button>
        
        <button
          onClick={handleEmailOffice}
          className="flex-1 border-2 border-gray-300 text-gray-700 hover:border-gray-400 hover:text-gray-900 px-6 py-4 rounded-lg font-semibold transition-all duration-300 hover:scale-105 flex items-center justify-center"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
          Send Email
        </button>
      </div>
    </div>
  );
};

export default InteractiveOfficeMap;

version: 4.39.1
installed_at: '2025-08-20T05:53:01.090Z'
install_type: full
agent: null
ides_setup:
  - cursor
expansion_packs: []
files:
  - path: .bmad-core\working-in-the-brownfield.md
    hash: de000e05b2989f61
    modified: false
  - path: .bmad-core\user-guide.md
    hash: d4d463b385f4aaf9
    modified: false
  - path: .bmad-core\enhanced-ide-development-workflow.md
    hash: c25b8019c3e5f9f5
    modified: false
  - path: .bmad-core\core-config.yaml
    hash: 073cf6d2527c545d
    modified: false
  - path: .bmad-core\utils\workflow-management.md
    hash: 8f9526717cfa6263
    modified: false
  - path: .bmad-core\utils\bmad-doc-template.md
    hash: bafd22330222d0db
    modified: false
  - path: .bmad-core\workflows\greenfield-ui.yaml
    hash: e5802a30eb91b080
    modified: false
  - path: .bmad-core\workflows\greenfield-service.yaml
    hash: ce836936794d76d4
    modified: false
  - path: .bmad-core\workflows\greenfield-fullstack.yaml
    hash: 5318ae5c2404c5a1
    modified: false
  - path: .bmad-core\workflows\brownfield-ui.yaml
    hash: 41fca3a470490c80
    modified: false
  - path: .bmad-core\workflows\brownfield-service.yaml
    hash: 6f7d70907cf31ac6
    modified: false
  - path: .bmad-core\workflows\brownfield-fullstack.yaml
    hash: 41e1b75a8f706fc8
    modified: false
  - path: .bmad-core\tasks\validate-next-story.md
    hash: 9d210935389a7140
    modified: false
  - path: .bmad-core\tasks\trace-requirements.md
    hash: baf845a19fbfe41c
    modified: false
  - path: .bmad-core\tasks\test-design.md
    hash: f67591d4c805d894
    modified: false
  - path: .bmad-core\tasks\shard-doc.md
    hash: fdf6718cd2aeaae7
    modified: false
  - path: .bmad-core\tasks\risk-profile.md
    hash: 113d504e8ace292c
    modified: false
  - path: .bmad-core\tasks\review-story.md
    hash: 24d689a518a10f01
    modified: false
  - path: .bmad-core\tasks\qa-gate.md
    hash: 062167dfcd9e145c
    modified: false
  - path: .bmad-core\tasks\nfr-assess.md
    hash: b19e676b4ff3f398
    modified: false
  - path: .bmad-core\tasks\kb-mode-interaction.md
    hash: 7470b07afc364cb6
    modified: false
  - path: .bmad-core\tasks\index-docs.md
    hash: c87fcefe5f09e297
    modified: false
  - path: .bmad-core\tasks\generate-ai-frontend-prompt.md
    hash: e04831c4ebbe958b
    modified: false
  - path: .bmad-core\tasks\facilitate-brainstorming-session.md
    hash: b1aee58b640f65ab
    modified: false
  - path: .bmad-core\tasks\execute-checklist.md
    hash: 93d051eee5dea60c
    modified: false
  - path: .bmad-core\tasks\document-project.md
    hash: 3ea204e67bf62d5a
    modified: false
  - path: .bmad-core\tasks\create-next-story.md
    hash: a94a5db68e2b14bc
    modified: false
  - path: .bmad-core\tasks\create-doc.md
    hash: 7222f15800b43f06
    modified: false
  - path: .bmad-core\tasks\create-deep-research-prompt.md
    hash: 0fb4d8adab2f7c69
    modified: false
  - path: .bmad-core\tasks\create-brownfield-story.md
    hash: 2ececd8ef3cc27b5
    modified: false
  - path: .bmad-core\tasks\correct-course.md
    hash: 0081e2f5e9894140
    modified: false
  - path: .bmad-core\tasks\brownfield-create-story.md
    hash: bfb994361445d315
    modified: false
  - path: .bmad-core\tasks\brownfield-create-epic.md
    hash: ea89cfcd376973ef
    modified: false
  - path: .bmad-core\tasks\apply-qa-fixes.md
    hash: 8b9458131bb2a05b
    modified: false
  - path: .bmad-core\tasks\advanced-elicitation.md
    hash: add7c0dcc657fa4d
    modified: false
  - path: .bmad-core\templates\story-tmpl.yaml
    hash: 8d968802aba40e63
    modified: false
  - path: .bmad-core\templates\qa-gate-tmpl.yaml
    hash: 743b30b2d2d22542
    modified: false
  - path: .bmad-core\templates\project-brief-tmpl.yaml
    hash: 6acbb5e502aa2383
    modified: false
  - path: .bmad-core\templates\prd-tmpl.yaml
    hash: 3a936f3533d5e035
    modified: false
  - path: .bmad-core\templates\market-research-tmpl.yaml
    hash: 9080b082f7c22ebc
    modified: false
  - path: .bmad-core\templates\fullstack-architecture-tmpl.yaml
    hash: 04ce0cf7a233ebec
    modified: false
  - path: .bmad-core\templates\front-end-spec-tmpl.yaml
    hash: fc373f3dfabc2293
    modified: false
  - path: .bmad-core\templates\front-end-architecture-tmpl.yaml
    hash: fc8e7adaa1d7f40d
    modified: false
  - path: .bmad-core\templates\competitor-analysis-tmpl.yaml
    hash: bc0fc5510e73a4b7
    modified: false
  - path: .bmad-core\templates\brownfield-prd-tmpl.yaml
    hash: c0c3dd64ccbcb5ab
    modified: false
  - path: .bmad-core\templates\brownfield-architecture-tmpl.yaml
    hash: a2607cdb24ba3198
    modified: false
  - path: .bmad-core\templates\brainstorming-output-tmpl.yaml
    hash: d2a6cbc7bef157f3
    modified: false
  - path: .bmad-core\templates\architecture-tmpl.yaml
    hash: 3b941d1b05bd6a14
    modified: false
  - path: .bmad-core\data\test-priorities-matrix.md
    hash: c9cbc6ffbc695f4e
    modified: false
  - path: .bmad-core\data\test-levels-framework.md
    hash: 08da05be3ab5062c
    modified: false
  - path: .bmad-core\data\technical-preferences.md
    hash: a1c491adacfe583f
    modified: false
  - path: .bmad-core\data\elicitation-methods.md
    hash: 32076992d7d51d9f
    modified: false
  - path: .bmad-core\data\brainstorming-techniques.md
    hash: 6b064f8f33dd5b69
    modified: false
  - path: .bmad-core\data\bmad-kb.md
    hash: 34fdcb8b3809d115
    modified: false
  - path: .bmad-core\checklists\story-draft-checklist.md
    hash: 8a09683838c995c4
    modified: false
  - path: .bmad-core\checklists\story-dod-checklist.md
    hash: 0eeeb7a92bc0ee0d
    modified: false
  - path: .bmad-core\checklists\po-master-checklist.md
    hash: 5d14f1b9afbd72de
    modified: false
  - path: .bmad-core\checklists\pm-checklist.md
    hash: 169b98cc6928d7fb
    modified: false
  - path: .bmad-core\checklists\change-checklist.md
    hash: d1e50741ca3fdb96
    modified: false
  - path: .bmad-core\checklists\architect-checklist.md
    hash: f35b1d350aa61ebb
    modified: false
  - path: .bmad-core\agents\ux-expert.md
    hash: caf97fdb62e8bf69
    modified: false
  - path: .bmad-core\agents\sm.md
    hash: 95f0ef651be2e253
    modified: false
  - path: .bmad-core\agents\qa.md
    hash: '528682183887e620'
    modified: false
  - path: .bmad-core\agents\po.md
    hash: 539e4bf842a7a072
    modified: false
  - path: .bmad-core\agents\pm.md
    hash: c3dc0d55bafbecd3
    modified: false
  - path: .bmad-core\agents\dev.md
    hash: 92be0f297bb3a3c3
    modified: false
  - path: .bmad-core\agents\bmad-orchestrator.md
    hash: e36b5804b91be55c
    modified: false
  - path: .bmad-core\agents\bmad-master.md
    hash: 8bcaf8c3b0851393
    modified: false
  - path: .bmad-core\agents\architect.md
    hash: 8af460e44b786c5b
    modified: false
  - path: .bmad-core\agents\analyst.md
    hash: f50c0cd7edd3a187
    modified: false
  - path: .bmad-core\agent-teams\team-no-ui.yaml
    hash: f076baef51b17d65
    modified: false
  - path: .bmad-core\agent-teams\team-ide-minimal.yaml
    hash: 1c3a2b3cc79547ee
    modified: false
  - path: .bmad-core\agent-teams\team-fullstack.yaml
    hash: da93170148300632
    modified: false
  - path: .bmad-core\agent-teams\team-all.yaml
    hash: c8e59dbf648c5e99
    modified: false

/**
 * Homepage E2E Tests
 * 
 * End-to-end tests for the homepage functionality
 */

import { test, expect } from '@playwright/test';

test.describe('Homepage', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test.describe('Page Load and SEO', () => {
    test('loads successfully with correct title', async ({ page }) => {
      await expect(page).toHaveTitle(/Bali Real Estate/);
    });

    test('has proper meta description', async ({ page }) => {
      const metaDescription = page.locator('meta[name="description"]');
      await expect(metaDescription).toHaveAttribute('content', /Find your perfect/);
    });

    test('has canonical URL', async ({ page }) => {
      const canonical = page.locator('link[rel="canonical"]');
      await expect(canonical).toHaveAttribute('href', /localhost:3001/);
    });

    test('has Open Graph tags', async ({ page }) => {
      const ogTitle = page.locator('meta[property="og:title"]');
      const ogDescription = page.locator('meta[property="og:description"]');
      const ogImage = page.locator('meta[property="og:image"]');
      
      await expect(ogTitle).toHaveAttribute('content');
      await expect(ogDescription).toHaveAttribute('content');
      await expect(ogImage).toHaveAttribute('content');
    });

    test('has structured data', async ({ page }) => {
      const structuredData = page.locator('script[type="application/ld+json"]');
      await expect(structuredData).toHaveCount(2); // Organization + Website
    });
  });

  test.describe('Navigation', () => {
    test('has working header navigation', async ({ page }) => {
      // Check logo link
      const logo = page.getByRole('link', { name: /bali real estate/i });
      await expect(logo).toBeVisible();
      await expect(logo).toHaveAttribute('href', '/');

      // Check main navigation links
      await expect(page.getByRole('link', { name: /locations/i })).toBeVisible();
      await expect(page.getByRole('link', { name: /property types/i })).toBeVisible();
      await expect(page.getByRole('link', { name: /contact/i })).toBeVisible();
    });

    test('dropdown menus work on hover', async ({ page }) => {
      const locationsLink = page.getByRole('link', { name: /locations/i });
      
      // Hover over locations
      await locationsLink.hover();
      
      // Check dropdown appears
      await expect(page.getByText('Canggu')).toBeVisible();
      await expect(page.getByText('Seminyak')).toBeVisible();
      await expect(page.getByText('Ubud')).toBeVisible();
    });

    test('mobile menu works', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Click mobile menu button
      const menuButton = page.getByRole('button', { name: /toggle menu/i });
      await menuButton.click();
      
      // Check mobile menu appears
      await expect(page.getByTestId('mobile-menu')).toBeVisible();
      
      // Check navigation links in mobile menu
      await expect(page.getByRole('link', { name: /locations/i })).toBeVisible();
    });
  });

  test.describe('Hero Section', () => {
    test('displays hero content', async ({ page }) => {
      await expect(page.getByRole('heading', { level: 1 })).toBeVisible();
      await expect(page.getByText(/find your perfect/i)).toBeVisible();
    });

    test('has working CTA buttons', async ({ page }) => {
      const ctaButton = page.getByRole('link', { name: /browse properties/i });
      await expect(ctaButton).toBeVisible();
      await expect(ctaButton).toHaveAttribute('href');
    });

    test('hero image loads properly', async ({ page }) => {
      const heroImage = page.locator('img').first();
      await expect(heroImage).toBeVisible();
      
      // Check image has loaded
      await expect(heroImage).toHaveJSProperty('complete', true);
      await expect(heroImage).toHaveJSProperty('naturalHeight', expect.any(Number));
    });
  });

  test.describe('Featured Locations', () => {
    test('displays location cards', async ({ page }) => {
      await expect(page.getByText('Popular Locations')).toBeVisible();
      
      // Check for location cards
      await expect(page.getByText('Canggu')).toBeVisible();
      await expect(page.getByText('Seminyak')).toBeVisible();
      await expect(page.getByText('Ubud')).toBeVisible();
    });

    test('location cards are clickable', async ({ page }) => {
      const cangguCard = page.getByRole('link', { name: /canggu/i });
      await expect(cangguCard).toBeVisible();
      
      await cangguCard.click();
      await expect(page).toHaveURL(/\/locations\/canggu/);
    });

    test('location images load with lazy loading', async ({ page }) => {
      const locationImages = page.locator('img[loading="lazy"]');
      await expect(locationImages.first()).toBeVisible();
    });
  });

  test.describe('Property Types', () => {
    test('displays property type cards', async ({ page }) => {
      await expect(page.getByText('Property Types')).toBeVisible();
      
      await expect(page.getByText('Villa')).toBeVisible();
      await expect(page.getByText('Apartment')).toBeVisible();
    });

    test('property type cards navigate correctly', async ({ page }) => {
      const villaCard = page.getByRole('link', { name: /villa/i });
      await villaCard.click();
      
      await expect(page).toHaveURL(/\/property-types\/villa/);
    });
  });

  test.describe('Contact Section', () => {
    test('displays contact information', async ({ page }) => {
      await expect(page.getByText('Get in Touch')).toBeVisible();
      await expect(page.getByText(/contact us/i)).toBeVisible();
    });

    test('contact form is present and functional', async ({ page }) => {
      const contactForm = page.getByRole('form');
      await expect(contactForm).toBeVisible();
      
      // Check form fields
      await expect(page.getByRole('textbox', { name: /name/i })).toBeVisible();
      await expect(page.getByRole('textbox', { name: /email/i })).toBeVisible();
      await expect(page.getByRole('textbox', { name: /message/i })).toBeVisible();
      
      // Check submit button
      await expect(page.getByRole('button', { name: /send message/i })).toBeVisible();
    });
  });

  test.describe('Footer', () => {
    test('displays footer content', async ({ page }) => {
      const footer = page.getByRole('contentinfo');
      await expect(footer).toBeVisible();
      
      // Check footer links
      await expect(page.getByText('Quick Links')).toBeVisible();
      await expect(page.getByText('Contact Info')).toBeVisible();
    });

    test('footer links work correctly', async ({ page }) => {
      const contactLink = page.getByRole('link', { name: /contact/i }).last();
      await contactLink.click();
      
      await expect(page).toHaveURL(/\/contact/);
    });
  });

  test.describe('Performance', () => {
    test('page loads within performance budget', async ({ page }) => {
      const startTime = Date.now();
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      const loadTime = Date.now() - startTime;
      
      // Should load within 3 seconds
      expect(loadTime).toBeLessThan(3000);
    });

    test('images are optimized', async ({ page }) => {
      const images = page.locator('img');
      const imageCount = await images.count();
      
      for (let i = 0; i < imageCount; i++) {
        const image = images.nth(i);
        const src = await image.getAttribute('src');
        
        if (src && !src.startsWith('data:')) {
          // Check for WebP or AVIF format
          expect(src).toMatch(/\.(webp|avif|jpg|jpeg|png)$/);
        }
      }
    });

    test('has proper lazy loading', async ({ page }) => {
      const lazyImages = page.locator('img[loading="lazy"]');
      const lazyImageCount = await lazyImages.count();
      
      // Should have lazy loaded images
      expect(lazyImageCount).toBeGreaterThan(0);
    });
  });

  test.describe('Accessibility', () => {
    test('has proper heading hierarchy', async ({ page }) => {
      const h1 = page.locator('h1');
      await expect(h1).toHaveCount(1);
      
      const h2 = page.locator('h2');
      const h2Count = await h2.count();
      expect(h2Count).toBeGreaterThan(0);
    });

    test('images have alt text', async ({ page }) => {
      const images = page.locator('img');
      const imageCount = await images.count();
      
      for (let i = 0; i < imageCount; i++) {
        const image = images.nth(i);
        const alt = await image.getAttribute('alt');
        
        // All images should have alt text (can be empty for decorative)
        expect(alt).not.toBeNull();
      }
    });

    test('links have accessible names', async ({ page }) => {
      const links = page.locator('a');
      const linkCount = await links.count();
      
      for (let i = 0; i < linkCount; i++) {
        const link = links.nth(i);
        const text = await link.textContent();
        const ariaLabel = await link.getAttribute('aria-label');
        
        // Links should have text content or aria-label
        expect(text || ariaLabel).toBeTruthy();
      }
    });

    test('form inputs have labels', async ({ page }) => {
      const inputs = page.locator('input, textarea, select');
      const inputCount = await inputs.count();
      
      for (let i = 0; i < inputCount; i++) {
        const input = inputs.nth(i);
        const id = await input.getAttribute('id');
        const ariaLabel = await input.getAttribute('aria-label');
        const ariaLabelledby = await input.getAttribute('aria-labelledby');
        
        if (id) {
          const label = page.locator(`label[for="${id}"]`);
          const hasLabel = await label.count() > 0;
          
          // Input should have label, aria-label, or aria-labelledby
          expect(hasLabel || ariaLabel || ariaLabelledby).toBeTruthy();
        }
      }
    });

    test('keyboard navigation works', async ({ page }) => {
      // Tab through interactive elements
      await page.keyboard.press('Tab');
      
      // Check first focusable element
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
      
      // Continue tabbing
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      
      // Should still have focus on visible element
      const newFocusedElement = page.locator(':focus');
      await expect(newFocusedElement).toBeVisible();
    });
  });

  test.describe('Responsive Design', () => {
    test('works on mobile devices', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Check mobile layout
      await expect(page.getByRole('heading', { level: 1 })).toBeVisible();
      await expect(page.getByRole('button', { name: /toggle menu/i })).toBeVisible();
    });

    test('works on tablet devices', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 });
      
      // Check tablet layout
      await expect(page.getByRole('heading', { level: 1 })).toBeVisible();
      await expect(page.getByRole('navigation')).toBeVisible();
    });

    test('works on desktop devices', async ({ page }) => {
      await page.setViewportSize({ width: 1920, height: 1080 });
      
      // Check desktop layout
      await expect(page.getByRole('heading', { level: 1 })).toBeVisible();
      await expect(page.getByRole('navigation')).toBeVisible();
    });
  });
});

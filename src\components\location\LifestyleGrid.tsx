/**
 * Lifestyle Grid Component
 * Bali Property Scout Website
 * 
 * Icon-based grid layout for lifestyle amenities and features
 * with hover effects and interactive elements.
 */

'use client';

import React from 'react';
import { cn } from '@/lib/utils';

export interface LifestyleFeature {
  id: string;
  title: string;
  icon: string;
  description: string;
  category: 'work' | 'lifestyle' | 'health' | 'entertainment' | 'transport' | 'community';
  rating?: number; // 1-5 scale
}

export interface LifestyleGridProps {
  /** Location name for context */
  locationName: string;
  /** Lifestyle features to display */
  features: LifestyleFeature[];
  /** Grid layout columns */
  columns?: 2 | 3 | 4;
  /** Additional CSS classes */
  className?: string;
}

export const LifestyleGrid: React.FC<LifestyleGridProps> = ({
  locationName,
  features,
  columns = 3,
  className,
}) => {
  // Get category color scheme
  const getCategoryColor = (category: LifestyleFeature['category']) => {
    const colors = {
      work: 'from-blue-500 to-indigo-500',
      lifestyle: 'from-emerald-500 to-teal-500',
      health: 'from-green-500 to-emerald-500',
      entertainment: 'from-purple-500 to-pink-500',
      transport: 'from-orange-500 to-red-500',
      community: 'from-yellow-500 to-orange-500',
    };
    return colors[category] || colors.lifestyle;
  };

  // Get category background color
  const getCategoryBg = (category: LifestyleFeature['category']) => {
    const colors = {
      work: 'bg-blue-50 hover:bg-blue-100',
      lifestyle: 'bg-emerald-50 hover:bg-emerald-100',
      health: 'bg-green-50 hover:bg-green-100',
      entertainment: 'bg-purple-50 hover:bg-purple-100',
      transport: 'bg-orange-50 hover:bg-orange-100',
      community: 'bg-yellow-50 hover:bg-yellow-100',
    };
    return colors[category] || colors.lifestyle;
  };

  // Render rating stars
  const renderRating = (rating?: number) => {
    if (!rating) return null;
    
    return (
      <div className="flex items-center mt-2">
        {[1, 2, 3, 4, 5].map((star) => (
          <span
            key={star}
            className={cn(
              'text-sm',
              star <= rating ? 'text-yellow-400' : 'text-gray-300'
            )}
          >
            ⭐
          </span>
        ))}
        <span className="text-xs text-gray-500 ml-2">
          {rating}/5
        </span>
      </div>
    );
  };

  const gridCols = {
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  };

  return (
    <div className={cn('', className)}>
      {/* Grid Header */}
      <div className="text-center mb-8">
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          Lifestyle & Amenities
        </h3>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Discover what makes {locationName} the perfect place to live, work, and play. 
          From world-class amenities to vibrant community life.
        </p>
      </div>

      {/* Features Grid */}
      <div className={cn('grid gap-6', gridCols[columns])}>
        {features.map((feature) => (
          <div
            key={feature.id}
            className={cn(
              'group relative bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 hover:-translate-y-1',
              getCategoryBg(feature.category)
            )}
          >
            {/* Icon Container */}
            <div className="relative mb-4">
              <div
                className={cn(
                  'w-16 h-16 rounded-2xl bg-gradient-to-br flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300',
                  getCategoryColor(feature.category)
                )}
              >
                <span className="text-3xl text-white">{feature.icon}</span>
              </div>
              
              {/* Category Badge */}
              <div className="absolute -top-2 -right-2">
                <div className="w-6 h-6 bg-white rounded-full shadow-lg flex items-center justify-center">
                  <div className={cn('w-3 h-3 rounded-full bg-gradient-to-br', getCategoryColor(feature.category))}></div>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="text-center">
              <h4 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-emerald-700 transition-colors duration-300">
                {feature.title}
              </h4>
              <p className="text-sm text-gray-600 leading-relaxed mb-3">
                {feature.description}
              </p>
              
              {/* Rating */}
              <div className="flex justify-center">
                {renderRating(feature.rating)}
              </div>
            </div>

            {/* Hover Effect Overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-teal-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
          </div>
        ))}
      </div>

      {/* Category Legend */}
      <div className="mt-12 bg-white rounded-2xl p-6 shadow-lg">
        <h4 className="text-lg font-bold text-gray-900 mb-4 text-center">
          Amenity Categories
        </h4>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {[
            { category: 'work' as const, label: 'Work & Business', icon: '💼' },
            { category: 'lifestyle' as const, label: 'Lifestyle', icon: '🌴' },
            { category: 'health' as const, label: 'Health & Wellness', icon: '🏥' },
            { category: 'entertainment' as const, label: 'Entertainment', icon: '🎭' },
            { category: 'transport' as const, label: 'Transportation', icon: '🚗' },
            { category: 'community' as const, label: 'Community', icon: '👥' },
          ].map((cat) => (
            <div
              key={cat.category}
              className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              <div className={cn('w-8 h-8 rounded-lg bg-gradient-to-br flex items-center justify-center', getCategoryColor(cat.category))}>
                <span className="text-sm text-white">{cat.icon}</span>
              </div>
              <div>
                <div className="text-xs font-medium text-gray-900">
                  {cat.label}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LifestyleGrid;

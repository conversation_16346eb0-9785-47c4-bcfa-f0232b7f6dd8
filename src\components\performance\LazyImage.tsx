/**
 * Lazy Image Component
 * 
 * High-performance image component with lazy loading, WebP support, and responsive sizing
 */

'use client';

import { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface LazyImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  quality?: number;
  sizes?: string;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  onLoad?: () => void;
  onError?: () => void;
}

export function LazyImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  quality = 75,
  sizes,
  placeholder = 'empty',
  blurDataURL,
  onLoad,
  onError,
}: LazyImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(priority);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const [currentSrc, setCurrentSrc] = useState<string>('');

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || isInView) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        });
      },
      {
        rootMargin: '50px',
        threshold: 0.1,
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [priority, isInView]);

  // Generate optimized image sources
  useEffect(() => {
    if (!isInView) return;

    const generateOptimizedSrc = () => {
      // Check if browser supports WebP
      const supportsWebP = () => {
        const canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = 1;
        return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
      };

      // Generate responsive image URL
      const generateResponsiveSrc = (baseSrc: string, targetWidth?: number) => {
        // If it's already an optimized URL, return as is
        if (baseSrc.includes('/_next/image') || baseSrc.includes('w_')) {
          return baseSrc;
        }

        // For external images or unoptimized images, add query parameters
        const url = new URL(baseSrc, window.location.origin);
        
        if (targetWidth) {
          url.searchParams.set('w', targetWidth.toString());
        }
        if (quality !== 75) {
          url.searchParams.set('q', quality.toString());
        }
        if (supportsWebP()) {
          url.searchParams.set('f', 'webp');
        }

        return url.toString();
      };

      const optimizedSrc = generateResponsiveSrc(src, width);
      setCurrentSrc(optimizedSrc);
    };

    generateOptimizedSrc();
  }, [isInView, src, width, quality]);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  // Generate srcSet for responsive images
  const generateSrcSet = () => {
    if (!width || !currentSrc) return undefined;

    const breakpoints = [0.5, 1, 1.5, 2];
    return breakpoints
      .map((multiplier) => {
        const targetWidth = Math.round(width * multiplier);
        const url = new URL(currentSrc);
        url.searchParams.set('w', targetWidth.toString());
        return `${url.toString()} ${multiplier}x`;
      })
      .join(', ');
  };

  // Placeholder component
  const Placeholder = () => (
    <div
      className={cn(
        'bg-gray-200 animate-pulse flex items-center justify-center',
        className
      )}
      style={{ width, height }}
    >
      {placeholder === 'blur' && blurDataURL ? (
        <img
          src={blurDataURL}
          alt=""
          className="w-full h-full object-cover filter blur-sm"
          aria-hidden="true"
        />
      ) : (
        <svg
          className="w-8 h-8 text-gray-400"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fillRule="evenodd"
            d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
            clipRule="evenodd"
          />
        </svg>
      )}
    </div>
  );

  // Error fallback
  if (hasError) {
    return (
      <div
        className={cn(
          'bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center',
          className
        )}
        style={{ width, height }}
      >
        <div className="text-center text-gray-500">
          <svg
            className="w-8 h-8 mx-auto mb-2"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
              clipRule="evenodd"
            />
          </svg>
          <p className="text-sm">Failed to load image</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      {/* Placeholder */}
      {!isLoaded && <Placeholder />}
      
      {/* Actual image */}
      {isInView && (
        <img
          ref={imgRef}
          src={currentSrc}
          srcSet={generateSrcSet()}
          sizes={sizes}
          alt={alt}
          width={width}
          height={height}
          loading={priority ? 'eager' : 'lazy'}
          decoding="async"
          className={cn(
            'transition-opacity duration-300',
            isLoaded ? 'opacity-100' : 'opacity-0',
            !isLoaded && 'absolute inset-0',
            className
          )}
          onLoad={handleLoad}
          onError={handleError}
          style={{
            aspectRatio: width && height ? `${width}/${height}` : undefined,
          }}
        />
      )}
    </div>
  );
}

// Higher-order component for image optimization
export function withImageOptimization<T extends { src: string; alt: string }>(
  Component: React.ComponentType<T>
) {
  return function OptimizedComponent(props: T) {
    return <Component {...props} />;
  };
}

// Utility function to generate optimized image URLs
export function getOptimizedImageUrl(
  src: string,
  options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'jpeg' | 'png';
  } = {}
) {
  const { width, height, quality = 75, format } = options;
  
  // For Next.js Image Optimization API
  const params = new URLSearchParams();
  
  if (width) params.set('w', width.toString());
  if (height) params.set('h', height.toString());
  if (quality !== 75) params.set('q', quality.toString());
  if (format) params.set('f', format);
  
  return `/_next/image?url=${encodeURIComponent(src)}&${params.toString()}`;
}

// Preload critical images
export function preloadImage(src: string, options?: { as?: string; crossOrigin?: string }) {
  if (typeof window === 'undefined') return;
  
  const link = document.createElement('link');
  link.rel = 'preload';
  link.as = options?.as || 'image';
  link.href = src;
  
  if (options?.crossOrigin) {
    link.crossOrigin = options.crossOrigin;
  }
  
  document.head.appendChild(link);
}

// Image performance monitoring
export function trackImagePerformance(src: string, startTime: number) {
  if (typeof window === 'undefined' || !window.gtag) return;
  
  const loadTime = performance.now() - startTime;
  
  window.gtag('event', 'image_load_time', {
    event_category: 'performance',
    event_label: src,
    value: Math.round(loadTime),
    custom_map: {
      metric_name: 'image_load_time',
      metric_value: loadTime,
    },
  });
}

export default LazyImage;

/**
 * Enhanced Contact Forms Component
 * Bali Property Scout Website
 * 
 * Tabbed interface with Free Consultation Request and Quick Inquiry forms.
 * Features real-time validation, success/error feedback, and AI chat integration.
 */

'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import ChatbotCTA from '@/components/ui/ChatbotCTA';

interface FormData {
  name: string;
  email: string;
  phone: string;
  propertyType: string;
  budget: string;
  location: string;
  timeline: string;
  message: string;
}

interface FormErrors {
  [key: string]: string;
}

export const EnhancedContactForms: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'consultation' | 'inquiry'>('consultation');
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    propertyType: '',
    budget: '',
    location: '',
    timeline: '',
    message: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const validateField = (name: string, value: string): string => {
    switch (name) {
      case 'name':
        return value.length < 2 ? 'Name must be at least 2 characters' : '';
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return !emailRegex.test(value) ? 'Please enter a valid email address' : '';
      case 'phone':
        return value.length < 8 ? 'Please enter a valid phone number' : '';
      case 'propertyType':
        return !value ? 'Please select a property type' : '';
      case 'budget':
        return !value ? 'Please select a budget range' : '';
      default:
        return '';
    }
  };

  const handleInputChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Real-time validation
    const error = validateField(name, value);
    setErrors(prev => ({ ...prev, [name]: error }));
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    const requiredFields = activeTab === 'consultation' 
      ? ['name', 'email', 'phone', 'propertyType', 'budget']
      : ['name', 'email', 'message'];

    requiredFields.forEach(field => {
      const error = validateField(field, formData[field as keyof FormData]);
      if (error) newErrors[field] = error;
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setSubmitStatus('success');
      setFormData({
        name: '',
        email: '',
        phone: '',
        propertyType: '',
        budget: '',
        location: '',
        timeline: '',
        message: ''
      });
    } catch (error) {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const propertyTypes = [
    { value: 'villa', label: 'Villa' },
    { value: 'guesthouse', label: 'Guesthouse' },
    { value: 'apartment', label: 'Apartment' },
    { value: 'land', label: 'Land' },
    { value: 'commercial', label: 'Commercial' }
  ];

  const budgetRanges = [
    { value: '300-800', label: '$300 - $800/month' },
    { value: '800-1500', label: '$800 - $1,500/month' },
    { value: '1500-3000', label: '$1,500 - $3,000/month' },
    { value: '3000-5000', label: '$3,000 - $5,000/month' },
    { value: '5000+', label: '$5,000+/month' },
    { value: 'purchase', label: 'Looking to Purchase' }
  ];

  const locations = [
    { value: 'canggu', label: 'Canggu' },
    { value: 'seminyak', label: 'Seminyak' },
    { value: 'ubud', label: 'Ubud' },
    { value: 'sanur', label: 'Sanur' },
    { value: 'jimbaran', label: 'Jimbaran' },
    { value: 'uluwatu', label: 'Uluwatu' },
    { value: 'flexible', label: 'Flexible' }
  ];

  const timelines = [
    { value: 'asap', label: 'ASAP' },
    { value: '1-month', label: 'Within 1 month' },
    { value: '3-months', label: 'Within 3 months' },
    { value: '6-months', label: 'Within 6 months' },
    { value: 'flexible', label: 'Flexible' }
  ];

  return (
    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <div className="flex">
          <button
            onClick={() => setActiveTab('consultation')}
            className={cn(
              'flex-1 px-6 py-4 text-sm font-medium transition-all duration-200',
              activeTab === 'consultation'
                ? 'bg-emerald-50 text-emerald-700 border-b-2 border-emerald-500'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            )}
          >
            Free Consultation Request
          </button>
          <button
            onClick={() => setActiveTab('inquiry')}
            className={cn(
              'flex-1 px-6 py-4 text-sm font-medium transition-all duration-200',
              activeTab === 'inquiry'
                ? 'bg-emerald-50 text-emerald-700 border-b-2 border-emerald-500'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            )}
          >
            Quick Inquiry
          </button>
        </div>
      </div>

      {/* Form Content */}
      <div className="p-8">
        {submitStatus === 'success' && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <p className="text-green-800 font-medium">
                Thank you! We'll get back to you within 24 hours.
              </p>
            </div>
          </div>
        )}

        {submitStatus === 'error' && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              <p className="text-red-800 font-medium">
                Something went wrong. Please try again or contact us directly.
              </p>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Full Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={cn(
                  'w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors',
                  errors.name ? 'border-red-300' : 'border-gray-300'
                )}
                placeholder="Enter your full name"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address *
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={cn(
                  'w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors',
                  errors.email ? 'border-red-300' : 'border-gray-300'
                )}
                placeholder="Enter your email address"
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email}</p>
              )}
            </div>
          </div>

          {activeTab === 'consultation' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number *
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className={cn(
                    'w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors',
                    errors.phone ? 'border-red-300' : 'border-gray-300'
                  )}
                  placeholder="Enter your phone number"
                />
                {errors.phone && (
                  <p className="mt-1 text-sm text-red-600">{errors.phone}</p>
                )}
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Property Type *
                  </label>
                  <select
                    value={formData.propertyType}
                    onChange={(e) => handleInputChange('propertyType', e.target.value)}
                    className={cn(
                      'w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors',
                      errors.propertyType ? 'border-red-300' : 'border-gray-300'
                    )}
                  >
                    <option value="">Select property type</option>
                    {propertyTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                  {errors.propertyType && (
                    <p className="mt-1 text-sm text-red-600">{errors.propertyType}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Budget Range *
                  </label>
                  <select
                    value={formData.budget}
                    onChange={(e) => handleInputChange('budget', e.target.value)}
                    className={cn(
                      'w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors',
                      errors.budget ? 'border-red-300' : 'border-gray-300'
                    )}
                  >
                    <option value="">Select budget range</option>
                    {budgetRanges.map(budget => (
                      <option key={budget.value} value={budget.value}>{budget.label}</option>
                    ))}
                  </select>
                  {errors.budget && (
                    <p className="mt-1 text-sm text-red-600">{errors.budget}</p>
                  )}
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preferred Location
                  </label>
                  <select
                    value={formData.location}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                  >
                    <option value="">Any location</option>
                    {locations.map(location => (
                      <option key={location.value} value={location.value}>{location.label}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Timeline
                  </label>
                  <select
                    value={formData.timeline}
                    onChange={(e) => handleInputChange('timeline', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                  >
                    <option value="">Select timeline</option>
                    {timelines.map(timeline => (
                      <option key={timeline.value} value={timeline.value}>{timeline.label}</option>
                    ))}
                  </select>
                </div>
              </div>
            </>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {activeTab === 'consultation' ? 'Additional Requirements' : 'Message *'}
            </label>
            <textarea
              value={formData.message}
              onChange={(e) => handleInputChange('message', e.target.value)}
              rows={4}
              className={cn(
                'w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors resize-none',
                activeTab === 'inquiry' && errors.message ? 'border-red-300' : 'border-gray-300'
              )}
              placeholder={
                activeTab === 'consultation' 
                  ? 'Tell us about any specific requirements or questions you have...'
                  : 'How can we help you with your Bali property needs?'
              }
            />
            {activeTab === 'inquiry' && errors.message && (
              <p className="mt-1 text-sm text-red-600">{errors.message}</p>
            )}
          </div>

          {/* Submit Buttons */}
          <div className="flex flex-col sm:flex-row gap-4">
            <button
              type="submit"
              disabled={isSubmitting}
              className={cn(
                'flex-1 bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl',
                isSubmitting && 'opacity-50 cursor-not-allowed transform-none'
              )}
            >
              {isSubmitting ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Submitting...
                </div>
              ) : (
                activeTab === 'consultation' ? 'Request Free Consultation' : 'Send Inquiry'
              )}
            </button>

            <div className="flex-1">
              <ChatbotCTA
                query={{ 
                  type: 'contact-form-alternative', 
                  context: 'contact-page-form',
                  formType: activeTab
                }}
                variant="secondary"
                className="w-full btn-secondary text-center px-8 py-4 border-2 border-emerald-600 text-emerald-600 hover:bg-emerald-50 transition-all duration-300"
              >
                Or Chat with AI Now
              </ChatbotCTA>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EnhancedContactForms;

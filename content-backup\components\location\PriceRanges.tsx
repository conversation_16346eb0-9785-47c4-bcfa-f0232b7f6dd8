interface PriceRangesProps {
  location: string
  propertyType: string
  transactionType: string
  priceRange?: {
    min: number
    max: number
    currency: string
    period: string
  }
}

const PriceRanges = ({ propertyType, transactionType, priceRange }: PriceRangesProps) => {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-6xl mx-auto px-4">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">
          Price Ranges for {propertyType.charAt(0).toUpperCase() + propertyType.slice(1)} {transactionType.replace('-', ' ')}
        </h2>
        {priceRange && (
          <div className="bg-emerald-50 rounded-lg p-6">
            <div className="text-2xl font-bold text-emerald-600">
              {priceRange.currency} {priceRange.min.toLocaleString()} - {priceRange.max.toLocaleString()}
            </div>
            <div className="text-gray-600">per {priceRange.period}</div>
          </div>
        )}
      </div>
    </section>
  )
}

export default PriceRanges

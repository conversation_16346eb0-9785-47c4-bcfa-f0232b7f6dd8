/**
 * Client Motion Wrapper Component
 * Epic 6: UI Modernization - Safe client-side Motion wrapper
 */

'use client';

import React from 'react';
import { motion } from 'motion/react';

export interface ClientMotionWrapperProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  hover?: boolean;
  whileInView?: boolean;
}

export const ClientMotionWrapper: React.FC<ClientMotionWrapperProps> = ({
  children,
  className,
  delay = 0,
  hover = true,
  whileInView = true,
}) => {
  const motionProps = {
    initial: whileInView ? { opacity: 0, y: 20 } : undefined,
    whileInView: whileInView ? { opacity: 1, y: 0 } : undefined,
    whileHover: hover ? { scale: 1.02, y: -4 } : undefined,
    transition: {
      duration: 0.3,
      delay,
      ease: 'easeOut',
    },
    viewport: { once: true, margin: '-50px' },
  };

  return (
    <motion.div
      className={className}
      {...motionProps}
    >
      {children}
    </motion.div>
  );
};

export default ClientMotionWrapper;

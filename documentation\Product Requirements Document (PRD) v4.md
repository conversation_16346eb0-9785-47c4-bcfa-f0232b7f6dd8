Product Requirements Document (PRD) - Bali Real Estate Website (English Version)
Objective
The Bali Real Estate site is not meant to display live property listings. It is designed to improve the search visibility of a real estate agent in Bali and to convert visitors into leads through a chatbot. Users looking for villas, guesthouses, apartments or land should be able to learn about Bali’s locations and property types, then start a conversation with the chatbot to get specific recommendations. There are no actual listing pages; therefore every call to action labelled “Explore” or “Find Properties” must lead to the chatbot with a relevant search query.
Current state and major issues
•	Homepage – The header contains drop‑down menus for Locations and Property Types with links that point to pages which are either empty or result in 404 errors[1]. The hero section promotes “Specialized Location Pages” but the underlying pages are incomplete. The high priority location cards (Seminyak, Ubud, Canggu, Jimbaran, Sanur) link to a contact form rather than opening a relevant search[2].
•	All Locations page – Each location card has buttons “Explore” and “Find Properties”, yet both actions currently redirect to the contact page[3].
•	Location detail pages – Pages such as “Seminyak Long‑term Rentals & Properties for Sale” have many sections but lack real data: featured images are missing, property type and pricing sections are blank and the “View Available Properties” button links to the contact page[4].
•	Property type pages – Only “Villas” and “Guesthouses” pages exist and they display generic text like “both Options Available” without prices[5][6]. The “Apartments” and “Land” pages return 404 errors[7].
•	Services and About pages – These pages show “coming soon” content with minimal information[8][9].
•	Chatbot – The /chatbot route shows “Loading chat…” and offers no guidance[10]. The Find My Property button opens the chatbot with a query like ?query=general-consultation but does not explain how to interact.
•	Broken navigation – The majority of header links go nowhere (apartments, land). There is no dedicated 404 page.
Functional requirements
1.	Chatbot integration
2.	Every call‑to‑action that encourages users to find properties must lead to the chatbot: /chatbot?query=[your-query]. For example:
o	From a location card on the All Locations page: “Find Properties in Canggu” → /chatbot?query=rent-villa-in-canggu.
o	From a property type card: “Find Guesthouses” → /chatbot?query=rent-guesthouse-in-bali.
o	From the Find My Property button: /chatbot?query=general-consultation.
3.	The chatbot page must load instantly and display a friendly greeting such as: > Hello, I’m your Bali Real Estate assistant. Tell me what you’re looking for, for example ‘long term villa rental in Ubud’ or ‘buy land in Jimbaran’.
4.	The chat interface should support quick reply buttons (“Rent a villa”, “Buy property”, “Schedule viewing”) and allow free‑text queries. It should be the only entry point for property searches.
5.	Navigation
6.	Remove or hide menu options that are not implemented (e.g., Apartments and Land) until content exists.
7.	The Locations and Property Types dropdowns should link to SEO landing pages that educate visitors and include strong CTAs to the chatbot.
8.	Add a breadcrumbs component on all location and property type pages (Home > Location > Property Type > Purpose).
9.	404 and error handling
10.	Create a custom 404 page: “This page is under construction. Use the chatbot to find properties or return to the homepage.” Include a button linking to /chatbot?query=general-consultation.
11.	Analytics and consent
12.	Integrate Google Analytics or a privacy‑friendly alternative to measure page views, click‑throughs to the chatbot and conversions. Provide a cookie consent banner.
13.	Performance and accessibility
14.	Use Next.js server side rendering for fast page loads. Optimize images and lazy load when appropriate. Ensure form fields have labels and alt attributes. All text should meet contrast guidelines.
Content requirements (in English)
Homepage
•	Hero section – Use a clear value proposition:
Heading: Find your perfect home or investment in Bali.
Subheading: “Leverage our local expertise to rent or purchase villas, guesthouses, apartments or land across Bali. Our chatbot is ready to help you now.”
CTA buttons: “Start Chat” (links to /chatbot?query=general-consultation) and “Browse Locations” (links to All Locations page).
•	High priority locations – Display cards for the five key locations with a 1‑sentence description and indicative price range. Example card:
Title: “Seminyak – Long‑term Villa Rental”.
Description: “Upscale beach area with luxury amenities and fine dining”[11].
Price range: “From $1,000 to $4,000 per month”.
Buttons: “Learn More” → location landing page, “Find villas” → /chatbot?query=rent-villa-in-seminyak.
•	Why choose us – Three bullet points emphasising the agent’s strengths: local expertise, verified properties, personal guidance.
•	Start your Bali journey form – Simple form (name, email, timeline) whose submission triggers a thank you message and suggests starting a chat. A link to the privacy policy must be present.
•	Complete Bali coverage – Keep the lists of Digital Nomad Hubs, Family Destinations and Investment Hotspots but link each location to its page. Add a button: “Not sure? Ask our chatbot” → /chatbot?query=general-consultation.
All Locations page
•	Introduction text: “Discover the right area for your long‑term rental or investment. Each location has unique lifestyle benefits, price points and opportunities.”
•	Each location card must show:
•	Name (e.g., “Canggu”).
•	Priority level (High or Very high).
•	2‑sentence overview summarising lifestyle, amenities and typical price ranges.
•	CTA buttons: “Learn More” (links to location page) and “Find Properties” (links to chatbot with query=rent-villa-in-[location]).
•	Add filter options (dropdowns for property type and budget). Selecting a filter opens the chatbot with the corresponding query.
Location pages (Canggu, Ubud, Seminyak, Sanur, Jimbaran)
Each location page should be structured for SEO and user engagement:
•	Header banner – Feature image with overlay; location title and tagline. Example tagline: “Surf town paradise with digital nomad community and rice field views”[12].
•	Quick stats – Show rental price ranges (e.g., $800‑3,000/month), average yields and growth for purchases. Since no listings exist, these ranges can be approximated.
•	About the location – Two paragraphs describing the history and atmosphere of the area.
•	What makes it special – List 5–7 unique selling points (surf breaks, coworking spaces, high‑speed internet, family friendly amenities, etc.).
•	Property types & pricing – Table summarising available property types (villa, guesthouse, apartment, land) and typical rent and purchase ranges. Include a note that specific properties are only accessible via the chatbot.
•	Lifestyle & amenities – Subsections for Living Experience, Digital Nomad Friendly, Key Amenities, Transportation and Walkability.[13]
•	Practical information – Visa options (30‑day visa free, 60‑day visa on arrival, long stay visa B211/B212), healthcare availability, safety and weather.
•	Market analysis – Present data such as average rental yields (e.g., 8‑12%) and property value growth (e.g., 12‑20% per year)[14].
•	Neighborhoods & sub‑areas – Describe 3–5 neighbourhoods and for each list the type of resident it suits[15].
•	Cost of living – Provide typical monthly budget ranges, housing cost ranges and daily expenses[16].
•	Legal & regulatory information – Explain leasehold vs freehold, PMA structures and visa requirements.[17]
•	FAQs – Answer common questions such as average cost of living, visa durations, safety and internet speed.
•	CTAs – At the end of major sections include a button like “Discuss your requirements” linking to the chatbot with a context‑specific query (e.g., /chatbot?query=rent-villa-in-canggu).
Property type pages
Because there are no real listings, these pages will serve as educational guides for each property type. All “Get Expert Guidance” or “Start Your Search” buttons should open the chatbot with a relevant query.
1.	Villas
2.	Opening paragraph: “Private villas with pools, gardens and modern amenities, ideal for families, digital nomads and luxury seekers.”
3.	Rental price range: $800–4,000 per month depending on location.
4.	Purchase price range: $150,000–2 million (leasehold vs freehold).
5.	Perfect for: families, digital nomads, investors seeking privacy.
6.	CTAs: “Find villas to rent” → /chatbot?query=rent-villa-in-bali, “Buy a villa” → /chatbot?query=buy-villa-in-bali.
7.	Guesthouses
8.	Opening paragraph: “Cozy guesthouses with local charm, perfect for budget travellers or investors who want high occupancy.”
9.	Rental price range: $300–1,500 per month.
10.	Purchase price range: $50,000–300,000 (leasehold).
11.	Perfect for: budget travellers, small investors.
12.	CTAs as above.
13.	Apartments (to be built)
14.	Opening paragraph: “Modern apartments in urban hubs with shared facilities such as pools and gyms.”
15.	Rental price range: $500–2,000 per month.
16.	Purchase price range: $80,000–400,000.
17.	Perfect for: young professionals, digital nomads.
18.	CTAs: “Find apartments” → /chatbot?query=rent-apartment-in-bali, “Buy an apartment” → /chatbot?query=buy-apartment-in-bali.
19.	Land (to be built)
20.	Opening paragraph: “Invest in land to build your own dream home or development project.”
21.	Purchase price range: $50,000–200,000 per are (dependent on location and view).
22.	Perfect for: developers, long‑term investors.
23.	Important: foreign ownership of land is restricted; link to legal services in Services page.
Services page
Create a fully fleshed page that explains the company’s offerings. Each service should include a short description, a few benefits and a CTA pointing to the chatbot.
Service	Description	CTA
Investment consultation	Market analysis, ROI calculations and due diligence for investors. Example: average yield of 12% in Canggu[18].
Start investment conversation → /chatbot?query=investment-consultation
Property purchase assistance	Assist buyers with property selection, negotiation, legal documentation and closing.	Find your dream home → /chatbot?query=buy-property-in-bali
Consultation & viewing arrangements	Organise virtual and physical viewings and personal consultations.	Schedule a viewing → /chatbot?query=schedule-viewing
About page
•	Explain the mission: to help expats, digital nomads and investors find their home or investment in Bali since 2019.
•	Describe the vision and values: integrity, transparency, local community involvement and sustainability.
•	Introduce the team with photos and short biographies; mention combined experience and number of successful transactions since establishment.
•	Tell the company’s story: founded by Indonesian and international partners; over 500 successful deals; emphasise local roots and global perspective.
•	Include testimonials or social proof if available.
•	CTA: “Meet our team” linking to a team subpage or start a chat for an introduction.
Contact page
•	Provide a standard enquiry form (name, email, phone, timeline, message) and a quick enquiry form (timeline dropdown and short text). After submission show a thank‑you message and suggest starting a chat.
•	Include contact details (email, phone, office address in Canggu) and business hours in WITA.
•	Embed a map of the office location.
•	Add a link: “Have a quick question? Chat with us now” → /chatbot?query=general-consultation.
Keyword strategy
A keyword map is crucial for SEO. Below is a list of 50 intent keywords divided into search intent (informational/navigational) and buying stage (commercial/transactional). Each keyword is assigned to the page where it should appear prominently (in title, headings, body text and meta descriptions). Avoid over‑stuffing; use natural language.
Search intent keywords (top of funnel)
Keyword	Assigned page	Reason
bali real estate guide	Homepage, About	High‑level informational search about Bali property.
best areas to live in bali	All Locations page	Users researching locations before deciding.
living in canggu	Canggu location page	Informational query about lifestyle.
long term rental bali	Homepage, Locations pages	Generic search for rentals.
bali expat housing	Homepage, Canggu & Seminyak pages	Appeals to expat audience.
bali digital nomad community	Canggu, Ubud pages	Matches nomad hubs[15].
bali family friendly areas	Sanur, Jimbaran pages	Searches for family destinations.
bali property types explained	Property Types overview page	Informational content on property categories.
leasehold vs freehold bali	Legal & regulatory sections	Users researching ownership options[17].
bali visa requirements for property	Practical info sections	Informational query about visas.
bali investment hotspots	Investment consultation section	Users exploring investment opportunities.
cost of living in bali	Cost of Living sections[16]
General cost research.
bali weather by region	Location pages	Additional info for decision‑making.
how to buy property in bali as foreigner	Legal & regulatory sections	Guides foreigners on purchase process.
bali market analysis	Market analysis sections	People researching investment data.
bali neighbourhoods comparison	Neighbourhoods sections	For users comparing sub‑areas.
bali real estate agent	About page	Navigational search for agents.
moving to bali checklist	About & Practical info	General relocation search.
bali property management tips	Property type pages & FAQ	Informational search about property management.
safe areas in bali	FAQ & Safety sections	Users concerned about safety.
bali coworking spaces	Canggu & Ubud pages	Supports digital nomad queries[15].
bali long term villa rental	Property type pages & location pages	Targeted search for villa rentals.
bali guesthouse investment	Guesthouse property type page	Appeals to investors.
bali apartment living	Apartment property page	Informational about apartments.
buying land in bali	Land property page	Informational search.
bali legal services	Legal & regulatory information sections	Navigational search to find legal help.
Buying stage keywords (commercial/transactional)
Keyword	Assigned page	Reason
rent villa in canggu	Canggu page & chatbot query	Transactional search.
rent villa in ubud	Ubud page & chatbot query	Transactional search.
rent villa in seminyak	Seminyak page & chatbot query	Transactional search.
buy villa in bali	Villas property page & chatbot	Purchase search.
buy guesthouse in bali	Guesthouse property page & chatbot	Purchase search.
rent guesthouse bali	Guesthouse property page & chatbot	Rental search.
buy land in bali	Land property page & chatbot	Purchase search.
rent apartment bali	Apartment property page & chatbot	Rental search.
invest in bali property	Investment consultation page	Commercial intent.
canggu property investment	Canggu page & Investment section	Specific investment query.
bali property for sale	Homepage	General purchase intent.
long term rental canggu	Canggu page & chatbot	Transactional rental search.
long term rental ubud	Ubud page & chatbot	Transactional rental search.
luxury villa bali for sale	Villas page & location pages	High‑end purchase search.
beach villa bali for rent	Location pages (Seminyak, Jimbaran)	Search for beachfront rentals.
affordable villa bali	Villas page & Sanur page	Budget friendly search.
villa with pool bali	Villas page	Feature specific search.
investment property bali	Investment consultation & Services page	Users ready to invest.
schedule villa viewing bali	Property type pages & chatbot	Action oriented search.
bali property consultation	Contact page & Services page	Commercial search for consultation.
canggu guesthouse for sale	Guesthouse page & chatbot	Transactional search.
seminyak villa price	Seminyak page	Users evaluating price.
sanur family villa rent	Sanur page	Family oriented rental search.
Acceptance criteria
1.	All navigation links work and do not return 404 errors. Unimplemented pages are hidden until ready.
2.	All Explore or Find Properties buttons open the chatbot with an appropriate query string. The chatbot loads with a greeting and can accept user input.
3.	Location and property type pages are filled with the content described above; no “coming soon” placeholders remain. There are no listings displayed.
4.	Services and About pages contain full English text, images and clear CTAs. Testimonials or proof points are added where possible.
5.	Contact page has working forms with thank‑you feedback. A map of the office is included.
6.	A custom 404 page appears for unknown routes and links to the chatbot.
7.	Meta titles and descriptions include relevant keywords. All images have alt text. Page headings follow a logical hierarchy.
8.	The site passes basic accessibility checks: labelled form fields, sufficient colour contrast and keyboard navigability.
9.	Google Analytics or another tracking solution records page views and chatbot clicks. A cookie consent banner is displayed.
10.	The copy is in American or British English and does not use em or en dashes; hyphens or commas are used instead.
________________________________________
[1] [2] Bali Real Estate - Long-term Rentals & Property Sales | Expert Local Guidance
https://bali-real-estate-website.vercel.app/
[3] All Locations - Bali Real Estate | Long-term Rentals & Property Sales
https://bali-real-estate-website.vercel.app/locations
[4] [11] [13] [14] [16] [17] Seminyak Long-term Rentals & Properties for Sale | Bali Real Estate
https://bali-real-estate-website.vercel.app/locations/seminyak
[5] Villa Properties in Bali - Rental & Sale | Bali Real Estate
https://bali-real-estate-website.vercel.app/property-types/villa
[6] Guesthouse Properties in Bali - Rental & Sale | Bali Real Estate
https://bali-real-estate-website.vercel.app/property-types/guesthouse
[7] 404: This page could not be found.
https://bali-real-estate-website.vercel.app/property-types/apartment
[8] Our Services - Coming Soon | Bali Real Estate
https://bali-real-estate-website.vercel.app/services
[9] About Us - Coming Soon | Bali Real Estate
https://bali-real-estate-website.vercel.app/about
[10] bali-real-estate-website.vercel.app
https://bali-real-estate-website.vercel.app/chatbot
[12] [15] [18] Canggu Long-term Rentals & Properties for Sale | Bali Real Estate
https://bali-real-estate-website.vercel.app/locations/canggu

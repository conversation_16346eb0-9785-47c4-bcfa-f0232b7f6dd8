'use client'

import { useState, useEffect, useRef, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'

interface Message {
  id: string
  type: 'user' | 'bot'
  content: string
  timestamp: Date
}

interface UserPreferences {
  budget?: string
  timeline?: string
  location?: string
  propertyType?: string
  transactionType?: string
}

function ChatbotContent() {
  const searchParams = useSearchParams()
  const messagesEndRef = useRef<HTMLDivElement>(null)
  
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [userPreferences, setUserPreferences] = useState<UserPreferences>({})
  const [conversationStage, setConversationStage] = useState<'greeting' | 'budget' | 'timeline' | 'location' | 'propertyType' | 'matching' | 'complete'>('greeting')

  // Parse initial query from URL
  useEffect(() => {
    const query = searchParams.get('query')
    if (query) {
      // Parse query like "rent-villa-in-canggu" or "buy-apartment-in-seminyak"
      const parts = query.split('-')
      let transactionType = ''
      let propertyType = ''
      let location = ''
      
      if (parts.includes('rent')) {
        transactionType = 'rental'
        const rentIndex = parts.indexOf('rent')
        if (parts[rentIndex + 1]) propertyType = parts[rentIndex + 1]
      } else if (parts.includes('buy')) {
        transactionType = 'purchase'
        const buyIndex = parts.indexOf('buy')
        if (parts[buyIndex + 1]) propertyType = parts[buyIndex + 1]
      }
      
      if (parts.includes('in') && parts.indexOf('in') < parts.length - 1) {
        location = parts[parts.indexOf('in') + 1]
      }

      setUserPreferences({ transactionType, propertyType, location })
      
      // Add initial bot message based on query
      const initialMessage: Message = {
        id: '1',
        type: 'bot',
        content: `Hi! I see you're interested in ${transactionType === 'rental' ? 'renting' : 'buying'} a ${propertyType} ${location ? `in ${location.charAt(0).toUpperCase() + location.slice(1)}` : 'in Bali'}. I'm here to help you find the perfect property! 

Let me ask you a few questions to provide personalized recommendations.

First, what's your budget range?`,
        timestamp: new Date()
      }
      
      setMessages([initialMessage])
      setConversationStage('budget')
    } else {
      // Default greeting
      const welcomeMessage: Message = {
        id: '1',
        type: 'bot',
        content: `Welcome to Bali Real Estate! 🏝️

I'm your AI property assistant, here to help you find the perfect property in Bali. Whether you're looking to rent or buy, I'll guide you through the process step by step.

What type of property are you interested in?
- Villa rental
- Apartment purchase  
- Land investment
- Or something else?`,
        timestamp: new Date()
      }
      
      setMessages([welcomeMessage])
    }
  }, [searchParams])

  // Auto scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const addMessage = (content: string, type: 'user' | 'bot') => {
    const newMessage: Message = {
      id: Date.now().toString(),
      type,
      content,
      timestamp: new Date()
    }
    setMessages(prev => [...prev, newMessage])
  }

  const getBotResponse = (userInput: string): string => {
    const input = userInput.toLowerCase()
    
    switch (conversationStage) {
      case 'budget':
        setConversationStage('timeline')
        return `Great! I've noted your budget preference. 

Now, when are you looking to ${userPreferences.transactionType === 'rental' ? 'move in' : 'complete the purchase'}?
- Immediately (within 1 month)
- Short-term (1-3 months)  
- Medium-term (3-6 months)
- Long-term (6+ months)
- Just researching for now`

      case 'timeline':
        setConversationStage('location')
        if (!userPreferences.location) {
          return `Perfect! I've noted your timeline.

Which area of Bali interests you most?
- Canggu (surf & digital nomad hub)
- Ubud (wellness & nature)
- Seminyak (luxury & nightlife)
- Sanur (family-friendly & traditional)
- Jimbaran (upscale beachfront)
- Other location`
        } else {
          setConversationStage('propertyType')
          return `Perfect! I've noted your timeline.

Since you're interested in ${userPreferences.location}, what type of property are you looking for?
- Villa (private pool, garden)
- Apartment (modern amenities)
- Guesthouse (budget-friendly)
- Land (investment opportunity)`
        }

      case 'location':
        setConversationStage('propertyType')
        return `Excellent choice! ${input.includes('canggu') ? 'Canggu is perfect for surfers and digital nomads.' : 
                input.includes('ubud') ? 'Ubud offers a peaceful, wellness-focused lifestyle.' :
                input.includes('seminyak') ? 'Seminyak is ideal for luxury living and nightlife.' :
                input.includes('sanur') ? 'Sanur is great for families and traditional Balinese culture.' :
                'Great location choice!'}

What type of property are you looking for?
- Villa (private pool, garden)
- Apartment (modern amenities)  
- Guesthouse (budget-friendly)
- Land (investment opportunity)`

      case 'propertyType':
        setConversationStage('matching')
        return `Perfect! Let me summarize what you're looking for:

📍 **Location**: ${userPreferences.location || 'Flexible'}
🏠 **Property Type**: ${input.includes('villa') ? 'Villa' : input.includes('apartment') ? 'Apartment' : input.includes('guesthouse') ? 'Guesthouse' : input.includes('land') ? 'Land' : 'As discussed'}
💰 **Budget**: As discussed
⏰ **Timeline**: As discussed
🔄 **Type**: ${userPreferences.transactionType === 'rental' ? 'Long-term rental' : 'Purchase'}

I'm now searching our database for matching properties... 

*[In the full implementation, this would connect to the CMS/API to fetch real properties]*

Based on your criteria, I found several excellent options! Would you like me to:

1. **Schedule a viewing** with our local expert
2. **Send detailed property information** to your email
3. **Connect you with our specialist** for this area
4. **Get a market analysis** for your budget range

What would be most helpful?`

      case 'matching':
        setConversationStage('complete')
        return `Excellent! I'll arrange that for you right away.

To complete your request, I'll need to collect some contact information. Would you like me to:

1. **Transfer you to our contact form** to provide your details
2. **Schedule a call** with our local expert
3. **Send you our property guide** for your chosen area

Our team typically responds within 2-4 hours during business hours (9 AM - 6 PM Bali time).

What works best for you?`

      default:
        return `Thank you for your interest! Our local experts will be in touch soon. 

Is there anything else I can help you with regarding Bali real estate?

You can also:
- Browse our [location guides](/locations)
- View [property types](/property-types)  
- [Contact our team directly](/contact)`
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!inputValue.trim() || isLoading) return

    const userMessage = inputValue.trim()
    setInputValue('')
    setIsLoading(true)

    // Add user message
    addMessage(userMessage, 'user')

    // Simulate typing delay
    setTimeout(() => {
      const botResponse = getBotResponse(userMessage)
      addMessage(botResponse, 'bot')
      setIsLoading(false)
    }, 1000)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-4">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/" className="flex items-center">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Back to Home
              </Link>
            </Button>
            <div className="h-6 w-px bg-gray-300" />
            <h1 className="text-lg font-semibold text-gray-900">Property Search Assistant</h1>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm text-gray-600">Online</span>
          </div>
        </div>
      </div>

      {/* Chat Container */}
      <div className="max-w-4xl mx-auto px-4 py-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-[600px] flex flex-col">
          
          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-6 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.type === 'user'
                      ? 'bg-emerald-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  <div className="whitespace-pre-wrap text-sm">{message.content}</div>
                  <div className={`text-xs mt-1 ${
                    message.type === 'user' ? 'text-emerald-100' : 'text-gray-500'
                  }`}>
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                </div>
              </div>
            ))}
            
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-gray-100 text-gray-900 px-4 py-2 rounded-lg">
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          {/* Input Form */}
          <div className="border-t border-gray-200 p-4">
            <form onSubmit={handleSubmit} className="flex space-x-2">
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="Type your message..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                disabled={isLoading}
              />
              <Button
                type="submit"
                disabled={!inputValue.trim() || isLoading}
                className="px-4 py-2"
              >
                Send
              </Button>
            </form>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600 mb-4">Need immediate assistance?</p>
          <div className="flex flex-wrap justify-center gap-3">
            <Button variant="outline" size="sm" asChild>
              <Link href="/contact">Contact Expert</Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="/locations">Browse Locations</Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="/property-types">View Property Types</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function ChatbotPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading chat...</p>
        </div>
      </div>
    }>
      <ChatbotContent />
    </Suspense>
  )
}

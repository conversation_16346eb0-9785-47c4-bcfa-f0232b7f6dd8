/**
 * Bali Property Scout Brand Variables
 * Consistent color palette and design tokens for UI/UX optimization
 * 
 * Following conversion-rate-optimization principles:
 * - Single accent color (deep green) for CTAs and highlights
 * - Neutral backgrounds with high contrast
 * - Professional color scheme for trust and credibility
 */

:root {
  /* Primary Brand Colors */
  --brand-primary: #065f46;        /* Deep green - primary CTAs */
  --brand-primary-hover: #047857;  /* Darker green - hover states */
  --brand-primary-light: #10b981;  /* Light green - accents */
  --brand-primary-bg: #ecfdf5;     /* Very light green - backgrounds */

  /* Neutral Colors */
  --neutral-white: #ffffff;
  --neutral-50: #f9fafb;
  --neutral-100: #f3f4f6;
  --neutral-200: #e5e7eb;
  --neutral-300: #d1d5db;
  --neutral-400: #9ca3af;
  --neutral-500: #6b7280;
  --neutral-600: #4b5563;
  --neutral-700: #374151;
  --neutral-800: #1f2937;
  --neutral-900: #111827;

  /* Semantic Colors */
  --success: #10b981;
  --success-bg: #ecfdf5;
  --warning: #f59e0b;
  --warning-bg: #fffbeb;
  --error: #ef4444;
  --error-bg: #fef2f2;
  --info: #3b82f6;
  --info-bg: #eff6ff;

  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-heading: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  /* Font Sizes - Minimum 16px for accessibility */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px - minimum for body text */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;

  /* Spacing - Increased for better whitespace */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */

  /* Border Radius */
  --radius-sm: 0.375rem;   /* 6px */
  --radius-md: 0.5rem;     /* 8px */
  --radius-lg: 0.75rem;    /* 12px */
  --radius-xl: 1rem;       /* 16px */
  --radius-2xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;

  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Button Styles - WCAG 2.1 AA Compliant */
.btn-primary {
  background-color: var(--brand-primary);
  color: var(--neutral-white);
  border: 2px solid var(--brand-primary);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  transition: all var(--transition-fast);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  min-height: 44px; /* Minimum touch target size */
}

.btn-primary:hover {
  background-color: var(--brand-primary-hover);
  border-color: var(--brand-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-primary:focus {
  outline: 2px solid var(--brand-primary-light);
  outline-offset: 2px;
}

.btn-secondary {
  background-color: transparent;
  color: var(--brand-primary);
  border: 2px solid var(--brand-primary);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  transition: all var(--transition-fast);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  min-height: 44px;
}

.btn-secondary:hover {
  background-color: var(--brand-primary);
  color: var(--neutral-white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary:focus {
  outline: 2px solid var(--brand-primary-light);
  outline-offset: 2px;
}

/* Card Styles with Increased Whitespace */
.card {
  background-color: var(--neutral-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  padding: var(--space-8);
  margin-bottom: var(--space-6);
  transition: all var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

/* Typography Styles */
.heading-1 {
  font-family: var(--font-heading);
  font-size: var(--text-5xl);
  font-weight: 700;
  line-height: var(--leading-tight);
  color: var(--neutral-900);
  margin-bottom: var(--space-6);
}

.heading-2 {
  font-family: var(--font-heading);
  font-size: var(--text-4xl);
  font-weight: 600;
  line-height: var(--leading-tight);
  color: var(--neutral-900);
  margin-bottom: var(--space-5);
}

.heading-3 {
  font-family: var(--font-heading);
  font-size: var(--text-2xl);
  font-weight: 600;
  line-height: var(--leading-normal);
  color: var(--neutral-900);
  margin-bottom: var(--space-4);
}

.body-text {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
  color: var(--neutral-700);
  margin-bottom: var(--space-4);
}

.body-text-large {
  font-family: var(--font-primary);
  font-size: var(--text-lg);
  line-height: var(--leading-relaxed);
  color: var(--neutral-700);
  margin-bottom: var(--space-5);
}

/* Utility Classes */
.text-accent {
  color: var(--brand-primary);
}

.bg-accent {
  background-color: var(--brand-primary);
}

.bg-accent-light {
  background-color: var(--brand-primary-bg);
}

.container-padding {
  padding-left: var(--space-6);
  padding-right: var(--space-6);
}

.section-spacing {
  padding-top: var(--space-20);
  padding-bottom: var(--space-20);
}

.content-spacing {
  margin-bottom: var(--space-8);
}

/* Responsive Design */
@media (min-width: 768px) {
  .container-padding {
    padding-left: var(--space-8);
    padding-right: var(--space-8);
  }
  
  .section-spacing {
    padding-top: var(--space-24);
    padding-bottom: var(--space-24);
  }
}

@media (min-width: 1024px) {
  .container-padding {
    padding-left: var(--space-12);
    padding-right: var(--space-12);
  }
}

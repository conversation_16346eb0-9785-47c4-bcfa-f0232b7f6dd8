interface LifestyleInformationProps {
  location: string
  locationInfo: {
    name: string
    lifestyle: string
  }
}

// Detailed lifestyle content based on PRD requirements (400+ words per location)
const lifestyleContent = {
  'canggu': {
    overview: `The lifestyle in Canggu perfectly embodies the modern Bali experience, where traditional Indonesian culture meets international sophistication. This vibrant coastal community has become synonymous with the digital nomad lifestyle, offering an unparalleled blend of work, wellness, and adventure that attracts professionals from around the globe.

The dining scene in Canggu is nothing short of extraordinary, featuring over 200 restaurants, cafes, and warungs that cater to every taste and budget. From world-class establishments like La Brisa and Finn's Beach Club offering fine dining with ocean views, to local warungs serving authentic nasi goreng for under $2, the culinary diversity is remarkable. Health-conscious options abound, with numerous vegan and organic restaurants like Crate Cafe, Shady Shack, and Milk & Madu leading Bali's wellness food movement.

Coworking spaces have revolutionized the work culture in Canggu, with over 20 professional facilities providing high-speed internet, meeting rooms, and networking opportunities. Dojo Bali, one of Asia's most renowned coworking spaces, offers 24/7 access and consistently delivers internet speeds exceeding 100 Mbps. Other popular spaces include Outsite, Tribal Bali, and BWork, each fostering unique communities of entrepreneurs, freelancers, and remote workers.

The fitness and wellness scene is equally impressive, with world-class gyms like The Gym Canggu and Crossfit Wanderlust providing state-of-the-art equipment and professional training. Yoga studios are abundant, with popular spots like The Practice, Samadi Bali, and Serenity Eco Guesthouse offering daily classes in various styles. The beach provides natural fitness opportunities, from surfing lessons at Echo Beach to beach volleyball and morning runs along the coastline.`,

    dining: [
      { name: 'Fine Dining', options: 'La Brisa, Finn\'s Beach Club, The Lawn', priceRange: '$15-40 per meal' },
      { name: 'Casual Dining', options: 'Crate Cafe, Milk & Madu, Shady Shack', priceRange: '$8-15 per meal' },
      { name: 'Local Warungs', options: 'Warung Bu Mi, Warung Dandelion', priceRange: '$2-5 per meal' },
      { name: 'Health Food', options: 'Peloton Supershop, Nalu Bowls', priceRange: '$6-12 per meal' }
    ],

    coworking: [
      { name: 'Dojo Bali', features: '24/7 access, 100+ Mbps, meeting rooms', price: '$89/month' },
      { name: 'Outsite', features: 'Coliving + coworking, community events', price: '$199/month' },
      { name: 'Tribal Bali', features: 'Beachfront location, surf community', price: '$79/month' },
      { name: 'BWork', features: 'Professional environment, networking', price: '$69/month' }
    ],

    activities: [
      'World-class surfing at Echo Beach and Batu Bolong',
      'Beach clubs and sunset cocktails',
      'Rice paddy cycling and walking tours',
      'Traditional Balinese cooking classes',
      'Yoga and meditation retreats',
      'Scooter adventures to hidden beaches'
    ],

    shopping: [
      { type: 'Markets', locations: 'Canggu Market, Berawa Market', description: 'Fresh produce, local goods' },
      { type: 'Boutiques', locations: 'Jalan Pantai Berawa', description: 'Surf wear, local fashion' },
      { type: 'Supermarkets', locations: 'Pepito, Bintang', description: 'International products' },
      { type: 'Malls', locations: 'Seminyak Village (15 min)', description: 'International brands' }
    ],

    healthcare: [
      { name: 'BIMC Hospital Kuta', distance: '25 minutes', services: 'International standard, English-speaking doctors' },
      { name: 'Canggu Clinic', distance: '5 minutes', services: 'General practice, minor emergencies' },
      { name: 'Pharmacies', locations: 'Multiple locations', services: '24/7 availability, prescription drugs' }
    ],

    entertainment: [
      'Beach clubs: La Brisa, Finn\'s, The Lawn',
      'Rooftop bars: Motel Mexicola, Mrs Sippy',
      'Live music venues: Old Man\'s, Echo Beach Bar',
      'Night markets and cultural performances',
      'Surf competitions and beach festivals'
    ]
  },

  'ubud': {
    overview: `Ubud's lifestyle revolves around wellness, spirituality, and cultural immersion, offering a completely different pace of life compared to Bali's beach destinations. This mountain town has become a global center for yoga, meditation, and holistic healing, attracting those seeking personal growth and connection with nature.

The dining scene in Ubud pioneered Bali's farm-to-table movement, with restaurants sourcing ingredients from local organic farms and traditional markets. Establishments like Locavore, Moksa, and Copper Kitchen & Bar have gained international recognition for their innovative approaches to Indonesian cuisine using locally-sourced, sustainable ingredients. The town offers an abundance of vegetarian and vegan options, with many restaurants incorporating Ayurvedic principles and raw food concepts.

Ubud's wellness infrastructure is unmatched in Southeast Asia, with over 100 yoga studios, meditation centers, and healing practitioners. The Yoga Barn, Radiantly Alive, and The Practice offer world-class instruction and teacher training programs that attract students from around the globe. Traditional Balinese healing practices, including Usada (traditional medicine) and energy healing, are readily available from experienced practitioners.

The cultural richness of daily life in Ubud is extraordinary, with traditional ceremonies, art workshops, and cultural performances happening regularly. Residents can participate in Balinese Hindu ceremonies, learn traditional crafts like wood carving and batik making, and attend daily cultural performances at the Royal Palace. The town's numerous art galleries and museums provide constant inspiration and learning opportunities.`,

    dining: [
      { name: 'Fine Dining', options: 'Locavore, Mozaic, Copper Kitchen', priceRange: '$25-50 per meal' },
      { name: 'Farm-to-Table', options: 'Moksa, Sari Organik, Bebek Bengil', priceRange: '$12-25 per meal' },
      { name: 'Healthy Cafes', options: 'Clear Cafe, Sayuri, Kafe', priceRange: '$8-15 per meal' },
      { name: 'Local Warungs', options: 'Warung Babi Guling Ibu Oka', priceRange: '$3-6 per meal' }
    ],

    wellness: [
      { name: 'Yoga Barn', features: 'Multiple studios, teacher training', price: '$15-20 per class' },
      { name: 'Radiantly Alive', features: 'Vinyasa, Yin, meditation', price: '$18-22 per class' },
      { name: 'The Practice', features: 'Ashtanga, workshops', price: '$16-20 per class' },
      { name: 'Traditional Healing', features: 'Balinese healers, energy work', price: '$30-80 per session' }
    ],

    activities: [
      'Daily yoga and meditation classes',
      'Rice terrace trekking and cycling',
      'Traditional art workshops and classes',
      'Sacred temple visits and ceremonies',
      'Organic farming experiences',
      'Spiritual retreats and workshops'
    ],

    shopping: [
      { type: 'Traditional Markets', locations: 'Ubud Market, Sukawati', description: 'Handicrafts, textiles, art' },
      { type: 'Art Galleries', locations: 'Monkey Forest Road', description: 'Local and international art' },
      { type: 'Organic Stores', locations: 'Bali Buda, Down to Earth', description: 'Organic food, eco products' },
      { type: 'Bookstores', locations: 'Ganesha Bookshop, Periplus', description: 'Spiritual, travel books' }
    ],

    healthcare: [
      { name: 'BIMC Ubud', distance: '5 minutes', services: 'International clinic, English-speaking doctors' },
      { name: 'Traditional Healers', locations: 'Multiple locations', services: 'Balinese traditional medicine' },
      { name: 'Pharmacies', locations: 'Central Ubud', services: 'Western and traditional medicines' }
    ],

    entertainment: [
      'Traditional dance performances at Royal Palace',
      'Art gallery openings and cultural events',
      'Live music at Jazz Cafe and Laughing Buddha',
      'Full moon ceremonies and spiritual gatherings',
      'Organic markets and wellness festivals'
    ]
  },

  'seminyak': {
    overview: `Seminyak epitomizes luxury living in Bali, offering a sophisticated lifestyle that rivals any international resort destination. This upscale area attracts affluent residents and visitors who appreciate fine dining, luxury shopping, premium spas, and exclusive beach clubs, all while maintaining the tropical charm that makes Bali special.

The dining scene in Seminyak is internationally acclaimed, featuring restaurants by celebrity chefs and innovative culinary concepts that have put Bali on the global gastronomic map. Establishments like Merah Putih, Sarong, and Mama San offer world-class cuisine in stunning settings, while beachfront venues like Ku De Ta and Potato Head Beach Club provide unforgettable dining experiences with panoramic ocean views.

Seminyak's beach club culture is legendary, with venues that set global standards for luxury and service. These establishments offer day-long experiences combining gourmet dining, premium cocktails, infinity pools, and world-class DJs, creating an atmosphere that seamlessly blends relaxation with sophisticated entertainment.

The shopping experience in Seminyak is exceptional, featuring a curated selection of international brands, local designers, and unique boutiques. High-end shopping complexes and tree-lined streets offer everything from luxury fashion to handcrafted jewelry, making it easy for residents to access quality goods and services without leaving the area.`,

    dining: [
      { name: 'Fine Dining', options: 'Merah Putih, Sarong, Mama San', priceRange: '$30-60 per meal' },
      { name: 'Beach Clubs', options: 'Ku De Ta, Potato Head, W Lounge', priceRange: '$25-45 per meal' },
      { name: 'Trendy Cafes', options: 'Revolver, Sisterfields, Kynd', priceRange: '$12-20 per meal' },
      { name: 'International', options: 'Bambu, Metis, La Lucciola', priceRange: '$20-35 per meal' }
    ],

    luxury: [
      { name: 'Beach Clubs', venues: 'Ku De Ta, Potato Head, Single Fin', experience: 'Infinity pools, world-class DJs' },
      { name: 'Spas', venues: 'Bodyworks, Prana Spa, AWAY', experience: 'Luxury treatments, wellness programs' },
      { name: 'Hotels', venues: 'W Bali, The Legian, Alila', experience: '5-star amenities, concierge services' }
    ],

    activities: [
      'Luxury beach club experiences',
      'Premium spa and wellness treatments',
      'High-end shopping and boutique browsing',
      'Sunset cocktails at rooftop bars',
      'Private beach dining experiences',
      'Exclusive cultural and art events'
    ],

    shopping: [
      { type: 'Luxury Malls', locations: 'Seminyak Village, Seminyak Square', description: 'International brands, designer stores' },
      { type: 'Boutiques', locations: 'Jalan Laksmana, Jalan Oberoi', description: 'Local designers, unique fashion' },
      { type: 'Art Galleries', locations: 'Jalan Raya Seminyak', description: 'Contemporary art, sculptures' },
      { type: 'Concept Stores', locations: 'Biasa, Magali Pascal', description: 'Curated lifestyle products' }
    ],

    healthcare: [
      { name: 'BIMC Hospital Kuta', distance: '15 minutes', services: 'International hospital, all specialties' },
      { name: 'Seminyak Medical Centre', distance: '5 minutes', services: 'General practice, minor procedures' },
      { name: 'Luxury Spas', locations: 'Multiple locations', services: 'Wellness treatments, health programs' }
    ],

    entertainment: [
      'World-class beach clubs with international DJs',
      'Rooftop bars and cocktail lounges',
      'Exclusive private events and parties',
      'Art gallery openings and fashion shows',
      'Luxury shopping and lifestyle events'
    ]
  }
}

const LifestyleInformation = ({ location, locationInfo }: LifestyleInformationProps) => {
  const content = lifestyleContent[location as keyof typeof lifestyleContent]

  if (!content) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">
            Lifestyle in {locationInfo.name}
          </h2>
          <p className="text-gray-600 text-lg leading-relaxed">
            {locationInfo.lifestyle}
          </p>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            Lifestyle & Amenities in {locationInfo.name}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Everything you need to know about daily life, dining, work, and entertainment
          </p>
        </div>

        {/* Main Overview */}
        <div className="prose prose-lg max-w-none mb-16">
          <div className="text-gray-700 leading-relaxed space-y-6">
            {content.overview.split('\n\n').map((paragraph, index) => (
              <p key={index}>{paragraph}</p>
            ))}
          </div>
        </div>

        {/* Dining Scene */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">Dining & Food Scene</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {content.dining.map((category, index) => (
              <div key={index} className="bg-white rounded-lg p-6 shadow-sm">
                <h4 className="text-lg font-semibold text-gray-900 mb-3">{category.name}</h4>
                <p className="text-gray-600 mb-3">{category.options}</p>
                <div className="flex items-center text-emerald-600">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm font-medium">{category.priceRange}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Work & Coworking (for locations that have it) */}
        {'coworking' in content && content.coworking && (
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Coworking & Internet</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {content.coworking.map((space, index) => (
                <div key={index} className="bg-white rounded-lg p-6 shadow-sm">
                  <h4 className="text-lg font-semibold text-gray-900 mb-3">{space.name}</h4>
                  <p className="text-gray-600 mb-3">{space.features}</p>
                  <div className="flex items-center text-emerald-600">
                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm font-medium">{space.price}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Wellness & Activities (for locations that have it) */}
        {'wellness' in content && content.wellness && (
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Wellness & Fitness</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {content.wellness.map((facility, index) => (
                <div key={index} className="bg-white rounded-lg p-6 shadow-sm">
                  <h4 className="text-lg font-semibold text-gray-900 mb-3">{facility.name}</h4>
                  <p className="text-gray-600 mb-3">{facility.features}</p>
                  <div className="flex items-center text-emerald-600">
                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm font-medium">{facility.price}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Activities */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">Activities & Entertainment</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {content.activities.map((activity, index) => (
              <div key={index} className="flex items-start gap-3 bg-white rounded-lg p-4 shadow-sm">
                <div className="w-6 h-6 bg-emerald-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <svg className="w-3 h-3 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="text-gray-700 text-sm">{activity}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Shopping */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">Shopping & Services</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {content.shopping.map((category, index) => (
              <div key={index} className="bg-white rounded-lg p-6 shadow-sm">
                <h4 className="text-lg font-semibold text-gray-900 mb-3">{category.type}</h4>
                <p className="text-gray-600 mb-2 font-medium">{category.locations}</p>
                <p className="text-gray-500 text-sm">{category.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Healthcare */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">Healthcare & Emergency Services</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {content.healthcare.map((facility, index) => (
              <div key={index} className="bg-white rounded-lg p-6 shadow-sm">
                <h4 className="text-lg font-semibold text-gray-900 mb-3">{facility.name}</h4>
                <div className="space-y-2">
                  <div className="flex items-center text-gray-600">
                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm">{facility.distance || facility.locations}</span>
                  </div>
                  <p className="text-gray-500 text-sm">{facility.services}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Entertainment */}
        <div>
          <h3 className="text-2xl font-bold text-gray-900 mb-8">Entertainment & Nightlife</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {content.entertainment.map((venue, index) => (
              <div key={index} className="flex items-start gap-3 bg-white rounded-lg p-4 shadow-sm">
                <div className="w-6 h-6 bg-emerald-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <svg className="w-3 h-3 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="text-gray-700 text-sm">{venue}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default LifestyleInformation
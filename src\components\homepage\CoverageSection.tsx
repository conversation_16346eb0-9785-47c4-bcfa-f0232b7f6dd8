/**
 * Coverage Section Component
 * Bali Property Scout Website
 * 
 * Comprehensive coverage area showcase with interactive tags
 * for locations, property types, and services.
 */

'use client';

import React from 'react';
import { InteractiveCoverageTags, type CoverageTag } from '@/components/ui/InteractiveCoverageTags';

export const CoverageSection: React.FC = () => {
  // All locations we cover - clean, no emojis
  const locationTags: CoverageTag[] = [
    { id: 'canggu', label: 'Canggu', type: 'location', priority: 'high', description: 'Surf capital with coworking spaces' },
    { id: 'ubud', label: 'Ubud', type: 'location', priority: 'high', description: 'Cultural heart with yoga retreats' },
    { id: 'seminyak', label: 'Seminyak', type: 'location', priority: 'high', description: 'Upscale beach clubs and dining' },
    { id: 'sanur', label: 'Sanur', type: 'location', priority: 'medium', description: 'Family-friendly calm beach area' },
    { id: 'jimbaran', label: 'Jimbaran', type: 'location', priority: 'medium', description: 'Sunset views and seafood dining' },
    { id: 'nusa-dua', label: 'Nusa Dua', type: 'location', priority: 'medium', description: 'Luxury resort area' },
    { id: 'uluwatu', label: 'Uluwatu', type: 'location', priority: 'medium', description: 'Clifftop views and world-class surf' },
    { id: 'denpasar', label: 'Denpasar', type: 'location', priority: 'low', description: 'Business district and local culture' },
    { id: 'tabanan', label: 'Tabanan', type: 'location', priority: 'low', description: 'Rice terraces and traditional villages' },
    { id: 'gianyar', label: 'Gianyar', type: 'location', priority: 'low', description: 'Art villages and cultural sites' },
  ];

  // Property types we specialize in - clean, no emojis
  const propertyTags: CoverageTag[] = [
    { id: 'villa', label: 'Private Villas', type: 'property-type', priority: 'high', description: 'Luxury villas with private pools' },
    { id: 'guesthouse', label: 'Guesthouses', type: 'property-type', priority: 'high', description: 'Authentic Balinese accommodations' },
    { id: 'apartment', label: 'Modern Apartments', type: 'property-type', priority: 'medium', description: 'Contemporary living spaces' },
    { id: 'compound', label: 'Villa Compounds', type: 'property-type', priority: 'medium', description: 'Multiple villas in one location' },
    { id: 'penthouse', label: 'Penthouses', type: 'property-type', priority: 'low', description: 'Luxury high-rise living' },
    { id: 'land', label: 'Land Plots', type: 'property-type', priority: 'low', description: 'Investment land opportunities' },
  ];

  // Services we provide - clean, no emojis, updated to match PRD
  const serviceTags: CoverageTag[] = [
    { id: 'long-term-rental', label: 'Long-term Rentals', type: 'service', priority: 'high', description: '6+ month rental agreements' },
    { id: 'property-purchase', label: 'Property Purchase', type: 'service', priority: 'high', description: 'Freehold and leasehold sales' },
    { id: 'investment-consultation', label: 'Investment Consultation', type: 'service', priority: 'high', description: 'ROI analysis and market insights' },
    { id: 'property-viewing', label: 'Consultation & Viewing', type: 'service', priority: 'medium', description: 'Guided property tours and advice' },
  ];

  return (
    <section className="section-spacing bg-gradient-to-br from-white via-neutral-50 to-neutral-50">
      <div className="max-w-7xl mx-auto container-padding">
        {/* Clean Section Header */}
        <div className="text-center mb-16">
          <div
            className="inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-semibold mb-6"
            style={{
              backgroundColor: 'var(--brand-primary-bg)',
              color: 'var(--brand-primary)'
            }}
          >
            Complete Coverage
          </div>

          <h2 className="heading-2 text-3xl md:text-5xl font-bold mb-6 leading-tight" style={{ color: 'var(--neutral-900)' }}>
            Everywhere You Want to Live in Bali
          </h2>

          <p className="body-text-large text-lg max-w-3xl mx-auto leading-relaxed" style={{ color: 'var(--neutral-600)' }}>
            From surf towns to cultural centers, luxury resorts to traditional villages.
            Our AI-powered search covers every corner of Bali with local expertise.
          </p>
        </div>

        {/* Location Coverage */}
        <div className="mb-16">
          <InteractiveCoverageTags
            tags={locationTags}
            title="All Bali Locations"
            description="Click any location to discover available properties with our AI assistant"
            variant="inline"
            maxVisible={6}
            showPriority={true}
            animated={true}
          />
        </div>

        {/* Property Types */}
        <div className="mb-16">
          <InteractiveCoverageTags
            tags={propertyTags}
            title="Property Types"
            description="From luxury villas to authentic guesthouses - find your perfect property type"
            variant="grid"
            showPriority={false}
            animated={true}
          />
        </div>

        {/* Services */}
        <div className="mb-16">
          <InteractiveCoverageTags
            tags={serviceTags}
            title="Our Services"
            description="Comprehensive real estate services powered by AI and local expertise"
            variant="inline"
            maxVisible={4}
            showPriority={false}
            animated={true}
          />
        </div>

        {/* Single Strong CTA */}
        <div className="text-center">
          <div className="card max-w-3xl mx-auto">
            <h3 className="heading-3 text-2xl font-bold mb-4" style={{ color: 'var(--neutral-900)' }}>
              Ready to Find Your Perfect Bali Property?
            </h3>
            <p className="body-text-large text-lg mb-8" style={{ color: 'var(--neutral-600)' }}>
              Our AI assistant combines local market knowledge with your preferences
              to find properties that match your exact needs and budget.
            </p>

            {/* Primary CTA with highest visual weight */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => {
                  window.location.href = 'https://app.balipropertyscout.com?query=comprehensive-property-search';
                }}
                className="btn-primary text-lg font-semibold px-8 py-4 min-h-[56px]"
                style={{
                  backgroundColor: 'var(--brand-primary)',
                  borderColor: 'var(--brand-primary)',
                  color: 'var(--neutral-white)'
                }}
              >
                Start AI Property Search
              </button>

              {/* Secondary action - less prominent */}
              <button
                onClick={() => {
                  window.location.href = '/contact';
                }}
                className="btn-secondary text-base font-medium px-6 py-3"
                style={{
                  borderColor: 'var(--brand-primary)',
                  color: 'var(--brand-primary)'
                }}
              >
                Talk to Expert
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CoverageSection;

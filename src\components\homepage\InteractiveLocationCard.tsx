/**
 * Interactive Location Card Component
 * Bali Property Scout Website
 * 
 * Enhanced location cards with hover animations, real photography,
 * priority tags, price ranges, and clear CTAs for homepage showcase.
 */

'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { ChatbotCTA } from '@/components/ui/ChatbotCTA';

export interface LocationCardProps {
  location: string;
  locationName: string;
  priority: string;
  description: string;
  priceRange: string;
  image: string;
  features: string[];
  index: number;
}

export const InteractiveLocationCard: React.FC<LocationCardProps> = ({
  location,
  locationName,
  priority,
  description,
  priceRange,
  image,
  features,
  index,
}) => {
  const [isHovered, setIsHovered] = useState(false);

  // Simplified priority badge styling - no gradients, consistent with brand
  const getPriorityBadgeStyle = (priority: string) => {
    switch (priority) {
      case 'ZEER HOOG':
        return 'bg-red-600 text-white border-red-500';
      case 'HOOG':
        return 'bg-orange-600 text-white border-orange-500';
      default:
        return 'bg-blue-600 text-white border-blue-500';
    }
  };

  // Simplified priority text
  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'ZEER HOOG':
        return 'High Priority';
      case 'HOOG':
        return 'Priority';
      default:
        return 'Available';
    }
  };

  return (
    <div
      className="group relative overflow-hidden bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 ease-out transform hover:scale-[1.05] hover:-translate-y-3"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{
        animationDelay: `${index * 150}ms`,
      }}
    >
      {/* Enhanced Image with Overlay */}
      <div className="relative w-full h-64 overflow-hidden rounded-t-3xl">
        <img
          src={image}
          alt={`${locationName} - Premium Properties`}
          className={`w-full h-full object-cover transition-all duration-700 ${
            isHovered ? 'scale-110 brightness-110' : 'scale-100'
          }`}
          loading="lazy"
        />
        
        {/* Dynamic Gradient Overlay */}
        <div className={`absolute inset-0 transition-all duration-500 ${
          isHovered 
            ? 'bg-gradient-to-t from-black/70 via-black/30 to-transparent' 
            : 'bg-gradient-to-t from-black/50 via-black/20 to-transparent'
        }`} />

        {/* Simplified Priority Badge */}
        <div className="absolute top-4 left-4">
          <span className={`px-3 py-1.5 rounded-lg text-xs font-semibold backdrop-blur-md border transition-all duration-300 ${getPriorityBadgeStyle(priority)} ${
            isHovered ? 'scale-105' : ''
          }`}>
            {getPriorityText(priority)}
          </span>
        </div>

        {/* Clean Price Range */}
        <div className="absolute top-4 right-4">
          <span
            className="px-3 py-1.5 backdrop-blur-md font-semibold text-sm rounded-lg shadow-lg border"
            style={{
              backgroundColor: 'var(--neutral-white)',
              color: 'var(--brand-primary)',
              borderColor: 'var(--brand-primary-light)'
            }}
          >
            {priceRange}
          </span>
        </div>

        {/* Clean Location Pin */}
        <div className="absolute bottom-4 left-4">
          <div className="flex items-center gap-2 px-3 py-1.5 bg-black/70 backdrop-blur-md text-white rounded-lg text-sm font-medium">
            <div className="w-2 h-2 rounded-full" style={{ backgroundColor: 'var(--brand-primary-light)' }}></div>
            {locationName}
          </div>
        </div>
      </div>

      {/* Enhanced Content */}
      <div className="relative p-6 space-y-4">
        {/* Clean Title */}
        <div>
          <h3
            className="heading-3 text-xl font-bold mb-2 line-clamp-2 transition-colors duration-300"
            style={{
              color: isHovered ? 'var(--brand-primary)' : 'var(--neutral-900)'
            }}
          >
            {locationName} Properties
          </h3>
          <p className="body-text text-sm leading-relaxed line-clamp-3" style={{ color: 'var(--neutral-600)' }}>
            {description}
          </p>
        </div>

        {/* Clean Features */}
        <div className="flex flex-wrap gap-2">
          {features.slice(0, 4).map((feature, featureIndex) => (
            <span
              key={featureIndex}
              className="px-3 py-1.5 text-xs rounded-lg font-medium border transition-transform duration-200 hover:scale-105"
              style={{
                backgroundColor: 'var(--brand-primary-bg)',
                color: 'var(--brand-primary)',
                borderColor: 'var(--brand-primary-light)'
              }}
            >
              {feature}
            </span>
          ))}
          {features.length > 4 && (
            <span
              className="px-3 py-1.5 text-xs rounded-lg font-medium border"
              style={{
                backgroundColor: 'var(--neutral-100)',
                color: 'var(--neutral-600)',
                borderColor: 'var(--neutral-200)'
              }}
            >
              +{features.length - 4} more
            </span>
          )}
        </div>

        {/* Professional Action Buttons - Clear Hierarchy */}
        <div className="flex gap-3 pt-4">
          <Link
            href={`/locations/${location}`}
            className="btn-secondary flex-1 text-center text-sm font-medium px-4 py-3 transition-all duration-300 hover:scale-105"
            style={{
              borderColor: 'var(--brand-primary)',
              color: 'var(--brand-primary)'
            }}
          >
            Learn More
          </Link>

          <ChatbotCTA
            query={{ type: 'location', location: locationName }}
            variant="primary"
            className="btn-primary flex-1 text-center text-sm font-semibold px-4 py-3 transition-all duration-300 hover:scale-105"
            style={{
              backgroundColor: 'var(--brand-primary)',
              borderColor: 'var(--brand-primary)',
              color: 'var(--neutral-white)'
            }}
          >
            Find Properties
          </ChatbotCTA>
        </div>

        {/* Hover Indicator */}
        <div className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-emerald-500 to-teal-500 transition-all duration-500 ${
          isHovered ? 'opacity-100 scale-x-100' : 'opacity-0 scale-x-0'
        }`} />
      </div>

      {/* Enhanced Glow Effect */}
      <div className={`absolute inset-0 rounded-3xl transition-all duration-500 pointer-events-none ${
        isHovered 
          ? 'shadow-2xl shadow-emerald-500/20 ring-2 ring-emerald-500/20' 
          : ''
      }`} />
    </div>
  );
};

export default InteractiveLocationCard;

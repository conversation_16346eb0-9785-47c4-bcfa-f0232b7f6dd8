/**
 * SEO Head Component
 * 
 * Comprehensive SEO meta tags and structured data for all pages
 */

import Head from 'next/head';
import { NextSeo, NextSeoProps } from 'next-seo';
import { SITE_CONFIG, generateCanonicalUrl, generateOpenGraphImage } from '@/lib/seo';

interface SEOHeadProps extends Partial<NextSeoProps> {
  title?: string;
  description?: string;
  keywords?: string[];
  canonical?: string;
  ogImage?: string;
  ogType?: 'website' | 'article' | 'product';
  structuredData?: Record<string, any> | Record<string, any>[];
  noindex?: boolean;
  nofollow?: boolean;
}

export function SEOHead({
  title,
  description,
  keywords = [],
  canonical,
  ogImage,
  ogType = 'website',
  structuredData,
  noindex = false,
  nofollow = false,
  ...nextSeoProps
}: SEOHeadProps) {
  // Generate full title
  const fullTitle = title 
    ? `${title} | ${SITE_CONFIG.name}`
    : SITE_CONFIG.name;

  // Generate canonical URL
  const canonicalUrl = canonical 
    ? generateCanonicalUrl(canonical)
    : SITE_CONFIG.url;

  // Generate OG image
  const openGraphImage = generateOpenGraphImage(
    title || SITE_CONFIG.name,
    description,
    ogImage
  );

  // Generate robots content
  const robotsContent = [
    noindex ? 'noindex' : 'index',
    nofollow ? 'nofollow' : 'follow',
  ].join(', ');

  return (
    <>
      <NextSeo
        title={fullTitle}
        description={description || SITE_CONFIG.description}
        canonical={canonicalUrl}
        openGraph={{
          type: ogType,
          locale: SITE_CONFIG.locale,
          url: canonicalUrl,
          siteName: SITE_CONFIG.name,
          title: fullTitle,
          description: description || SITE_CONFIG.description,
          images: [
            {
              url: openGraphImage,
              width: 1200,
              height: 630,
              alt: fullTitle,
              type: 'image/jpeg',
            },
          ],
        }}
        twitter={{
          handle: SITE_CONFIG.twitterHandle,
          site: SITE_CONFIG.twitterHandle,
          cardType: 'summary_large_image',
        }}
        additionalMetaTags={[
          {
            name: 'keywords',
            content: keywords.join(', '),
          },
          {
            name: 'author',
            content: SITE_CONFIG.name,
          },
          {
            name: 'robots',
            content: robotsContent,
          },
          {
            name: 'googlebot',
            content: robotsContent,
          },
          {
            name: 'bingbot',
            content: robotsContent,
          },
          {
            property: 'og:locale',
            content: SITE_CONFIG.locale,
          },
          {
            property: 'og:site_name',
            content: SITE_CONFIG.name,
          },
          {
            name: 'twitter:creator',
            content: SITE_CONFIG.twitterHandle,
          },
          // Geo tags for local SEO
          {
            name: 'geo.region',
            content: 'ID-BA',
          },
          {
            name: 'geo.placename',
            content: 'Bali',
          },
          {
            name: 'geo.position',
            content: '-8.3405;115.0920',
          },
          {
            name: 'ICBM',
            content: '-8.3405, 115.0920',
          },
        ]}
        {...nextSeoProps}
      />

      <Head>
        {/* Additional meta tags */}
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta httpEquiv="x-ua-compatible" content="IE=edge" />
        
        {/* Favicon and app icons */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5" />
        <meta name="msapplication-TileColor" content="#da532c" />
        <meta name="theme-color" content="#ffffff" />

        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://www.google-analytics.com" />
        <link rel="preconnect" href="https://www.googletagmanager.com" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* DNS prefetch for performance */}
        <link rel="dns-prefetch" href="//www.google-analytics.com" />
        <link rel="dns-prefetch" href="//www.googletagmanager.com" />

        {/* Structured data */}
        {structuredData && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(
                Array.isArray(structuredData) ? structuredData : [structuredData]
              ),
            }}
          />
        )}

        {/* Google Site Verification */}
        {process.env.GOOGLE_SITE_VERIFICATION && (
          <meta
            name="google-site-verification"
            content={process.env.GOOGLE_SITE_VERIFICATION}
          />
        )}

        {/* Bing Site Verification */}
        {process.env.BING_SITE_VERIFICATION && (
          <meta
            name="msvalidate.01"
            content={process.env.BING_SITE_VERIFICATION}
          />
        )}
      </Head>
    </>
  );
}

// Specialized SEO components for different page types
export function HomepageSEO() {
  return (
    <SEOHead
      title="Bali Property Scout - AI-Powered Property Search & Rentals"
      description="Find your perfect long-term rental or property in Bali with AI-powered search. Expert guidance for expats, digital nomads, and investors. Canggu, Seminyak, Ubud & more."
      keywords={[
        'bali property scout',
        'ai property search bali',
        'property for sale bali',
        'villa for sale bali',
        'bali property investment',
        'real estate agent bali',
        'long term rental bali',
        'expat housing bali',
        'digital nomad bali',
      ]}
      canonical="/"
    />
  );
}

export function LocationSEO({ 
  location, 
  description, 
  keywords = [] 
}: { 
  location: string; 
  description?: string; 
  keywords?: string[]; 
}) {
  return (
    <SEOHead
      title={`${location} Real Estate - Properties for Sale & Rent`}
      description={
        description || 
        `Discover the best properties in ${location}, Bali. Villas, apartments, and land for sale or long-term rent. Expert local guidance.`
      }
      keywords={[
        `${location.toLowerCase()} property for sale`,
        `${location.toLowerCase()} villa for sale`,
        `${location.toLowerCase()} real estate`,
        `property ${location.toLowerCase()} bali`,
        ...keywords,
      ]}
      canonical={`/locations/${location.toLowerCase()}`}
    />
  );
}

export function PropertyTypeSEO({ 
  propertyType, 
  description, 
  keywords = [] 
}: { 
  propertyType: string; 
  description?: string; 
  keywords?: string[]; 
}) {
  return (
    <SEOHead
      title={`${propertyType}s for Sale in Bali - Premium Properties`}
      description={
        description || 
        `Browse premium ${propertyType.toLowerCase()}s for sale across Bali. From beachfront properties to mountain retreats. Professional guidance included.`
      }
      keywords={[
        `${propertyType.toLowerCase()} for sale bali`,
        `bali ${propertyType.toLowerCase()} investment`,
        `${propertyType.toLowerCase()} property bali`,
        ...keywords,
      ]}
      canonical={`/property-types/${propertyType.toLowerCase()}`}
    />
  );
}

export function ContactSEO() {
  return (
    <SEOHead
      title="Contact Us - Bali Real Estate Experts"
      description="Get in touch with our Bali real estate experts. Professional guidance for property purchases, rentals, and investments. Free consultation available."
      keywords={[
        'bali real estate agent',
        'property consultation bali',
        'real estate advice bali',
        'contact real estate bali',
      ]}
      canonical="/contact"
    />
  );
}

export default SEOHead;

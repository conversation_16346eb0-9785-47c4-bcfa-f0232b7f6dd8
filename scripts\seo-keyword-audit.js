#!/usr/bin/env node

/**
 * SEO Keyword Audit Script
 * Epic 4: SEO & Analytics - Verify 50+ keyword implementation
 */

const fs = require('fs');
const path = require('path');

// PRD Target Keywords (50+ keywords)
const TARGET_KEYWORDS = [
  // Primary Keywords
  'bali real estate guide',
  'best areas to live in bali',
  'long term rental bali',
  'bali expat housing',
  'bali property for sale',
  'bali digital nomad community',
  'moving to bali checklist',
  'bali real estate agent',
  'cost of living in bali',
  'bali investment hotspots',
  'invest in bali property',
  'bali property consultation',
  
  // Location-Specific Keywords
  'canggu villa rental',
  'ubud property investment',
  'seminyak luxury villa',
  'sanur family home',
  'jimbaran beachfront property',
  'canggu surf lifestyle',
  'ubud wellness retreat',
  'seminyak nightlife district',
  'sanur quiet beach town',
  'jimbaran seafood restaurants',
  
  // Property Type Keywords
  'bali villa rental',
  'luxury villa bali',
  'traditional balinese villa',
  'modern villa canggu',
  'bali guesthouse rental',
  'authentic balinese accommodation',
  'budget guesthouse bali',
  'coliving space bali',
  'bali apartment rental',
  'modern apartment bali',
  'serviced apartment bali',
  'bali land investment',
  'land for sale bali',
  'development land bali',
  
  // Service Keywords
  'bali property consultation',
  'investment consultation bali',
  'property purchase assistance',
  'bali viewing arrangement',
  'real estate agent bali',
  'property management bali',
  
  // Long-tail Keywords
  'how to rent villa in bali',
  'buying property in bali guide',
  'bali property investment tips',
  'expat living in bali',
  'digital nomad bali housing',
  'bali property market trends',
  'foreign ownership bali property',
  'leasehold vs freehold bali',
  'bali property legal advice',
  'best bali neighborhoods expats'
];

// Files to audit
const FILES_TO_AUDIT = [
  'src/app/page.tsx',
  'src/app/services/page.tsx',
  'src/app/about/page.tsx',
  'src/app/locations/page.tsx',
  'src/app/property-types/villa/page.tsx',
  'src/app/property-types/guesthouse/page.tsx',
  'src/app/property-types/apartment/page.tsx',
  'src/app/property-types/land/page.tsx',
  'src/app/locations/[slug]/page.tsx',
  'src/lib/seo.ts'
];

function auditKeywords() {
  console.log('🔍 Starting SEO Keyword Audit...\n');
  
  const results = {
    totalKeywords: TARGET_KEYWORDS.length,
    foundKeywords: new Set(),
    missingKeywords: [],
    fileResults: {}
  };

  FILES_TO_AUDIT.forEach(filePath => {
    const fullPath = path.join(process.cwd(), filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return;
    }

    const content = fs.readFileSync(fullPath, 'utf8').toLowerCase();
    const fileKeywords = [];
    
    TARGET_KEYWORDS.forEach(keyword => {
      if (content.includes(keyword.toLowerCase())) {
        fileKeywords.push(keyword);
        results.foundKeywords.add(keyword);
      }
    });

    results.fileResults[filePath] = {
      keywordCount: fileKeywords.length,
      keywords: fileKeywords
    };

    console.log(`📄 ${filePath}: ${fileKeywords.length} keywords found`);
  });

  // Find missing keywords
  TARGET_KEYWORDS.forEach(keyword => {
    if (!results.foundKeywords.has(keyword)) {
      results.missingKeywords.push(keyword);
    }
  });

  // Generate report
  console.log('\n📊 SEO KEYWORD AUDIT RESULTS');
  console.log('================================');
  console.log(`Total Target Keywords: ${results.totalKeywords}`);
  console.log(`Keywords Found: ${results.foundKeywords.size}`);
  console.log(`Keywords Missing: ${results.missingKeywords.length}`);
  console.log(`Coverage: ${Math.round((results.foundKeywords.size / results.totalKeywords) * 100)}%`);

  if (results.missingKeywords.length > 0) {
    console.log('\n❌ Missing Keywords:');
    results.missingKeywords.forEach(keyword => {
      console.log(`   - ${keyword}`);
    });
  }

  console.log('\n📈 File Breakdown:');
  Object.entries(results.fileResults).forEach(([file, data]) => {
    console.log(`   ${file}: ${data.keywordCount} keywords`);
  });

  // Epic 4 Success Criteria
  const successThreshold = 40; // 80% of 50 keywords
  const isSuccess = results.foundKeywords.size >= successThreshold;
  
  console.log('\n🎯 EPIC 4 STATUS:');
  console.log(`Target: ${successThreshold}+ keywords implemented`);
  console.log(`Actual: ${results.foundKeywords.size} keywords implemented`);
  console.log(`Status: ${isSuccess ? '✅ SUCCESS' : '❌ NEEDS IMPROVEMENT'}`);

  return results;
}

// Run audit
if (require.main === module) {
  auditKeywords();
}

module.exports = { auditKeywords, TARGET_KEYWORDS };

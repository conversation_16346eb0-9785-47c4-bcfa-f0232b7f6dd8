/**
 * Input Component
 * 
 * A flexible input component with validation, different sizes, and states.
 * Supports various input types and includes proper accessibility features.
 */

import React from 'react';
import { cn } from '@/lib/utils';

export type InputSize = 'sm' | 'md' | 'lg';
export type InputVariant = 'default' | 'filled' | 'outlined';

export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  /** Input size */
  size?: InputSize;
  /** Input visual variant */
  variant?: InputVariant;
  /** Input label */
  label?: string;
  /** Helper text displayed below input */
  helperText?: string;
  /** Error message */
  error?: string;
  /** Whether input is required */
  required?: boolean;
  /** Icon to display before input */
  leftIcon?: React.ReactNode;
  /** Icon to display after input */
  rightIcon?: React.ReactNode;
  /** Whether input should take full width */
  fullWidth?: boolean;
  /** Custom className for input wrapper */
  wrapperClassName?: string;
  /** Custom className for input element */
  className?: string;
}

// Input size styles
const inputSizes = {
  sm: 'px-3 py-1.5 text-sm min-h-[32px]',
  md: 'px-4 py-2 text-sm min-h-[40px]',
  lg: 'px-4 py-3 text-base min-h-[48px]',
};

// Input variant styles
const inputVariants = {
  default: [
    'bg-white border border-neutral-300',
    'focus:border-primary-500 focus:ring-1 focus:ring-primary-500',
  ].join(' '),
  
  filled: [
    'bg-neutral-50 border border-transparent',
    'focus:bg-white focus:border-primary-500 focus:ring-1 focus:ring-primary-500',
  ].join(' '),
  
  outlined: [
    'bg-transparent border-2 border-neutral-300',
    'focus:border-primary-500',
  ].join(' '),
};

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      size = 'md',
      variant = 'default',
      label,
      helperText,
      error,
      required = false,
      leftIcon,
      rightIcon,
      fullWidth = false,
      wrapperClassName,
      className,
      disabled,
      id,
      ...props
    },
    ref
  ) => {
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
    const hasError = !!error;

    return (
      <div className={cn('flex flex-col', fullWidth && 'w-full', wrapperClassName)}>
        {/* Label */}
        {label && (
          <label
            htmlFor={inputId}
            className={cn(
              'block text-sm font-medium mb-2',
              hasError ? 'text-red-700' : 'text-neutral-700',
              disabled && 'text-neutral-400'
            )}
          >
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}

        {/* Input Container */}
        <div className="relative">
          {/* Left Icon */}
          {leftIcon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400">
              {leftIcon}
            </div>
          )}

          {/* Input Element */}
          <input
            ref={ref}
            id={inputId}
            className={cn(
              // Base styles
              'w-full rounded-md transition-colors duration-200',
              'placeholder:text-neutral-400',
              'focus:outline-none',
              'disabled:bg-neutral-100 disabled:text-neutral-400 disabled:cursor-not-allowed',
              
              // Size styles
              inputSizes[size],
              
              // Variant styles
              hasError 
                ? 'border-red-300 focus:border-red-500 focus:ring-1 focus:ring-red-500'
                : inputVariants[variant],
              
              // Icon padding adjustments
              leftIcon && 'pl-10',
              rightIcon && 'pr-10',
              
              // Custom className
              className
            )}
            disabled={disabled}
            required={required}
            {...props}
          />

          {/* Right Icon */}
          {rightIcon && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400">
              {rightIcon}
            </div>
          )}
        </div>

        {/* Helper Text or Error */}
        {(helperText || error) && (
          <p
            className={cn(
              'mt-2 text-xs',
              hasError ? 'text-red-600' : 'text-neutral-500'
            )}
          >
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

// Textarea component
export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  /** Textarea size */
  size?: InputSize;
  /** Textarea visual variant */
  variant?: InputVariant;
  /** Textarea label */
  label?: string;
  /** Helper text displayed below textarea */
  helperText?: string;
  /** Error message */
  error?: string;
  /** Whether textarea is required */
  required?: boolean;
  /** Whether textarea should take full width */
  fullWidth?: boolean;
  /** Custom className for textarea wrapper */
  wrapperClassName?: string;
  /** Custom className for textarea element */
  className?: string;
  /** Minimum number of rows */
  minRows?: number;
  /** Maximum number of rows */
  maxRows?: number;
}

export const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      size = 'md',
      variant = 'default',
      label,
      helperText,
      error,
      required = false,
      fullWidth = false,
      wrapperClassName,
      className,
      disabled,
      id,
      minRows = 3,
      maxRows,
      rows,
      ...props
    },
    ref
  ) => {
    const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`;
    const hasError = !!error;

    return (
      <div className={cn('flex flex-col', fullWidth && 'w-full', wrapperClassName)}>
        {/* Label */}
        {label && (
          <label
            htmlFor={textareaId}
            className={cn(
              'block text-sm font-medium mb-2',
              hasError ? 'text-red-700' : 'text-neutral-700',
              disabled && 'text-neutral-400'
            )}
          >
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}

        {/* Textarea Element */}
        <textarea
          ref={ref}
          id={textareaId}
          rows={rows || minRows}
          className={cn(
            // Base styles
            'w-full rounded-md transition-colors duration-200',
            'placeholder:text-neutral-400',
            'focus:outline-none resize-vertical',
            'disabled:bg-neutral-100 disabled:text-neutral-400 disabled:cursor-not-allowed',
            
            // Size styles
            inputSizes[size],
            
            // Variant styles
            hasError 
              ? 'border-red-300 focus:border-red-500 focus:ring-1 focus:ring-red-500'
              : inputVariants[variant],
            
            // Custom className
            className
          )}
          disabled={disabled}
          required={required}
          style={{
            minHeight: `${(minRows || 3) * 1.5}rem`,
            maxHeight: maxRows ? `${maxRows * 1.5}rem` : undefined,
          }}
          {...props}
        />

        {/* Helper Text or Error */}
        {(helperText || error) && (
          <p
            className={cn(
              'mt-2 text-xs',
              hasError ? 'text-red-600' : 'text-neutral-500'
            )}
          >
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);

Textarea.displayName = 'Textarea';

// Select component
export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'size'> {
  /** Select size */
  size?: InputSize;
  /** Select visual variant */
  variant?: InputVariant;
  /** Select label */
  label?: string;
  /** Helper text displayed below select */
  helperText?: string;
  /** Error message */
  error?: string;
  /** Whether select is required */
  required?: boolean;
  /** Select options */
  options: SelectOption[];
  /** Placeholder option */
  placeholder?: string;
  /** Whether select should take full width */
  fullWidth?: boolean;
  /** Custom className for select wrapper */
  wrapperClassName?: string;
  /** Custom className for select element */
  className?: string;
}

export const Select = React.forwardRef<HTMLSelectElement, SelectProps>(
  (
    {
      size = 'md',
      variant = 'default',
      label,
      helperText,
      error,
      required = false,
      options,
      placeholder,
      fullWidth = false,
      wrapperClassName,
      className,
      disabled,
      id,
      ...props
    },
    ref
  ) => {
    const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;
    const hasError = !!error;

    return (
      <div className={cn('flex flex-col', fullWidth && 'w-full', wrapperClassName)}>
        {/* Label */}
        {label && (
          <label
            htmlFor={selectId}
            className={cn(
              'block text-sm font-medium mb-2',
              hasError ? 'text-red-700' : 'text-neutral-700',
              disabled && 'text-neutral-400'
            )}
          >
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}

        {/* Select Container */}
        <div className="relative">
          {/* Select Element */}
          <select
            ref={ref}
            id={selectId}
            className={cn(
              // Base styles
              'w-full rounded-md transition-colors duration-200 appearance-none',
              'focus:outline-none cursor-pointer',
              'disabled:bg-neutral-100 disabled:text-neutral-400 disabled:cursor-not-allowed',
              
              // Size styles
              inputSizes[size],
              'pr-10', // Space for dropdown arrow
              
              // Variant styles
              hasError 
                ? 'border-red-300 focus:border-red-500 focus:ring-1 focus:ring-red-500'
                : inputVariants[variant],
              
              // Custom className
              className
            )}
            disabled={disabled}
            required={required}
            {...props}
          >
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {options.map((option) => (
              <option
                key={option.value}
                value={option.value}
                disabled={option.disabled}
              >
                {option.label}
              </option>
            ))}
          </select>

          {/* Dropdown Arrow */}
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
            <svg
              className="w-4 h-4 text-neutral-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>
        </div>

        {/* Helper Text or Error */}
        {(helperText || error) && (
          <p
            className={cn(
              'mt-2 text-xs',
              hasError ? 'text-red-600' : 'text-neutral-500'
            )}
          >
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);

Select.displayName = 'Select';

// Checkbox component
export interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  /** Checkbox size */
  size?: InputSize;
  /** Checkbox label */
  label?: string;
  /** Helper text displayed below checkbox */
  helperText?: string;
  /** Error message */
  error?: string;
  /** Custom className for checkbox wrapper */
  wrapperClassName?: string;
  /** Custom className for checkbox element */
  className?: string;
}

export const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
  (
    {
      size = 'md',
      label,
      helperText,
      error,
      wrapperClassName,
      className,
      disabled,
      id,
      ...props
    },
    ref
  ) => {
    const checkboxId = id || `checkbox-${Math.random().toString(36).substr(2, 9)}`;
    const hasError = !!error;

    const sizeClasses = {
      sm: 'w-4 h-4',
      md: 'w-5 h-5',
      lg: 'w-6 h-6',
    };

    return (
      <div className={cn('flex flex-col', wrapperClassName)}>
        <div className="flex items-start">
          <input
            ref={ref}
            type="checkbox"
            id={checkboxId}
            className={cn(
              // Base styles
              'rounded border-neutral-300 text-primary-600',
              'focus:ring-primary-500 focus:ring-2 focus:ring-offset-2',
              'disabled:bg-neutral-100 disabled:cursor-not-allowed',

              // Size styles
              sizeClasses[size],

              // Error styles
              hasError && 'border-red-300 focus:ring-red-500',

              // Custom className
              className
            )}
            disabled={disabled}
            {...props}
          />

          {label && (
            <label
              htmlFor={checkboxId}
              className={cn(
                'ml-3 text-sm',
                hasError ? 'text-red-700' : 'text-neutral-700',
                disabled ? 'text-neutral-400 cursor-not-allowed' : 'cursor-pointer'
              )}
            >
              {label}
            </label>
          )}
        </div>

        {/* Helper Text or Error */}
        {(helperText || error) && (
          <p
            className={cn(
              'mt-2 text-xs ml-8',
              hasError ? 'text-red-600' : 'text-neutral-500'
            )}
          >
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);

Checkbox.displayName = 'Checkbox';

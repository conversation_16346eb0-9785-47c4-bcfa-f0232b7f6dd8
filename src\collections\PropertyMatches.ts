import { CollectionConfig } from 'payload/types'

export const PropertyMatches: CollectionConfig = {
  slug: 'property-matches',
  labels: {
    singular: 'Property Match',
    plural: 'Property Matches',
  },
  admin: {
    useAsTitle: 'id',
    defaultColumns: ['conversationId', 'matchScore', 'userFeedback', 'createdAt'],
    group: 'AI System',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: [
    {
      name: 'conversationId',
      type: 'text',
      required: true,
      index: true,
      admin: {
        description: 'Reference to the AI conversation that generated this match',
      },
    },
    {
      name: 'propertyId',
      type: 'text',
      admin: {
        description: 'External property ID if available',
      },
    },
    {
      name: 'propertyData',
      type: 'group',
      fields: [
        {
          name: 'name',
          type: 'text',
          admin: {
            description: 'Property name or title',
          },
        },
        {
          name: 'type',
          type: 'select',
          options: [
            { label: 'Villa', value: 'villa' },
            { label: 'Apartment', value: 'apartment' },
            { label: 'Guesthouse', value: 'guesthouse' },
            { label: 'House', value: 'house' },
            { label: 'Studio', value: 'studio' },
          ],
        },
        {
          name: 'location',
          type: 'text',
          admin: {
            description: 'Property location (Canggu, Ubud, etc.)',
          },
        },
        {
          name: 'price',
          type: 'group',
          fields: [
            {
              name: 'amount',
              type: 'number',
            },
            {
              name: 'currency',
              type: 'select',
              defaultValue: 'USD',
              options: [
                { label: 'USD', value: 'USD' },
                { label: 'EUR', value: 'EUR' },
                { label: 'IDR', value: 'IDR' },
              ],
            },
            {
              name: 'period',
              type: 'select',
              options: [
                { label: 'Per Month', value: 'month' },
                { label: 'Per Year', value: 'year' },
                { label: 'Total Price', value: 'total' },
              ],
            },
          ],
        },
        {
          name: 'features',
          type: 'array',
          fields: [
            {
              name: 'feature',
              type: 'text',
            },
          ],
        },
        {
          name: 'images',
          type: 'array',
          fields: [
            {
              name: 'url',
              type: 'text',
            },
            {
              name: 'alt',
              type: 'text',
            },
          ],
        },
        {
          name: 'description',
          type: 'textarea',
        },
        {
          name: 'contactInfo',
          type: 'group',
          fields: [
            {
              name: 'email',
              type: 'email',
            },
            {
              name: 'phone',
              type: 'text',
            },
            {
              name: 'whatsapp',
              type: 'text',
            },
          ],
        },
      ],
    },
    {
      name: 'matchScore',
      type: 'number',
      required: true,
      min: 0,
      max: 100,
      admin: {
        description: 'AI-calculated match score (0-100)',
      },
      index: true,
    },
    {
      name: 'reasoning',
      type: 'textarea',
      required: true,
      admin: {
        description: 'AI explanation for why this property matches user preferences',
      },
    },
    {
      name: 'matchCriteria',
      type: 'group',
      fields: [
        {
          name: 'budgetMatch',
          type: 'number',
          min: 0,
          max: 100,
          admin: {
            description: 'How well property price matches user budget (0-100)',
          },
        },
        {
          name: 'locationMatch',
          type: 'number',
          min: 0,
          max: 100,
          admin: {
            description: 'How well property location matches user preferences (0-100)',
          },
        },
        {
          name: 'typeMatch',
          type: 'number',
          min: 0,
          max: 100,
          admin: {
            description: 'How well property type matches user preferences (0-100)',
          },
        },
        {
          name: 'lifestyleMatch',
          type: 'number',
          min: 0,
          max: 100,
          admin: {
            description: 'How well property lifestyle matches user preferences (0-100)',
          },
        },
        {
          name: 'featuresMatch',
          type: 'number',
          min: 0,
          max: 100,
          admin: {
            description: 'How well property features match user requirements (0-100)',
          },
        },
      ],
    },
    {
      name: 'userFeedback',
      type: 'select',
      options: [
        { label: 'Interested', value: 'interested' },
        { label: 'Not Interested', value: 'not_interested' },
        { label: 'Maybe', value: 'maybe' },
        { label: 'Contacted', value: 'contacted' },
      ],
      index: true,
      admin: {
        description: 'User feedback on this property match',
      },
    },
    {
      name: 'feedbackNotes',
      type: 'textarea',
      admin: {
        description: 'Additional user feedback or notes',
      },
    },
    {
      name: 'presented',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Whether this match has been presented to the user',
      },
    },
    {
      name: 'presentedAt',
      type: 'date',
      admin: {
        description: 'When this match was first presented to the user',
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ data, operation }) => {
        // Set presentedAt when marking as presented
        if (data.presented && !data.presentedAt) {
          data.presentedAt = new Date()
        }
        return data
      },
    ],
  },
  timestamps: true,
}

/**
 * Chatbot CTA Component
 * Bali Property Scout Website
 * 
 * Enhanced CTA button that routes to the chatbot with appropriate query parameters.
 * Includes hover animations and accessibility features.
 */

'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { generateChatbotUrl, ChatbotQuery } from '@/utils/chatbot-routing';

export interface ChatbotCTAProps {
  /** Button text */
  children: React.ReactNode;
  /** Chatbot query configuration */
  query: ChatbotQuery;
  /** Button variant */
  variant?: 'primary' | 'secondary' | 'outline';
  /** Button size */
  size?: 'sm' | 'md' | 'lg';
  /** Custom className */
  className?: string;
  /** Whether button is disabled */
  disabled?: boolean;
  /** Click handler (optional, for analytics) */
  onClick?: () => void;
  /** Fallback URL if chatbot fails */
  fallbackUrl?: string;
}

const variants = {
  primary: 'btn-primary bg-brand-primary text-white hover:bg-brand-primary-hover border-brand-primary hover:border-brand-primary-hover shadow-lg hover:shadow-xl',
  secondary: 'btn-secondary bg-transparent text-brand-primary border-2 border-brand-primary hover:bg-brand-primary hover:text-white shadow-md hover:shadow-lg',
  outline: 'btn-secondary border-2 border-brand-primary text-brand-primary hover:bg-brand-primary hover:text-white shadow-sm hover:shadow-md',
};

const sizes = {
  sm: 'px-4 py-2 text-sm min-h-[36px]',
  md: 'px-6 py-3 text-base min-h-[44px]',
  lg: 'px-8 py-4 text-lg min-h-[56px]',
};

export const ChatbotCTA: React.FC<ChatbotCTAProps> = ({
  children,
  query,
  variant = 'primary',
  size = 'md',
  className,
  disabled = false,
  onClick,
  fallbackUrl = '/contact',
}) => {
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    
    // Call optional onClick handler for analytics
    if (onClick) {
      onClick();
    }
    
    try {
      const chatbotUrl = generateChatbotUrl(query);
      
      // Navigate to chatbot in same tab for seamless UX
      window.location.href = chatbotUrl;
    } catch (error) {
      console.error('Failed to navigate to chatbot:', error);
      
      // Fallback to contact page
      window.location.href = fallbackUrl;
    }
  };

  return (
    <button
      onClick={handleClick}
      disabled={disabled}
      className={cn(
        // Base styles with brand variables
        'inline-flex items-center justify-center rounded-lg font-semibold transition-all duration-200',
        'focus:outline-none focus:ring-2 focus:ring-offset-2',
        'transform hover:scale-105 active:scale-95',
        'disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none',

        // Variant styles
        variants[variant],

        // Size styles
        sizes[size],

        // Custom className
        className
      )}
      style={{
        // Ensure brand colors are applied
        ...(variant === 'primary' && {
          backgroundColor: 'var(--brand-primary)',
          borderColor: 'var(--brand-primary)',
          color: 'var(--neutral-white)',
          '--tw-ring-color': 'var(--brand-primary-light)'
        }),
        ...(variant !== 'primary' && {
          borderColor: 'var(--brand-primary)',
          color: 'var(--brand-primary)',
          '--tw-ring-color': 'var(--brand-primary-light)'
        })
      }}
      aria-label={`${children} - Opens AI property assistant`}
    >
      {children}
    </button>
  );
};

/**
 * Predefined CTA buttons for common use cases
 */
export const ChatbotCTAs = {
  // General CTAs with action-oriented labels
  StartConsultation: (props?: Partial<ChatbotCTAProps>) => (
    <ChatbotCTA query={{ type: 'general' }} {...props}>
      Start Consultation
    </ChatbotCTA>
  ),

  ChatWithAIAdvisor: (props?: Partial<ChatbotCTAProps>) => (
    <ChatbotCTA query={{ type: 'general' }} {...props}>
      Chat with AI Advisor
    </ChatbotCTA>
  ),

  FindMyProperty: (props?: Partial<ChatbotCTAProps>) => (
    <ChatbotCTA query={{ type: 'general' }} {...props}>
      Find My Property
    </ChatbotCTA>
  ),

  GetExpertAdvice: (props?: Partial<ChatbotCTAProps>) => (
    <ChatbotCTA query={{ type: 'general' }} {...props}>
      Get Expert Advice
    </ChatbotCTA>
  ),
  
  // Location-specific CTAs
  ExploreLocation: (location: string, props?: Partial<ChatbotCTAProps>) => (
    <ChatbotCTA query={{ type: 'location', location }} {...props}>
      Explore {location}
    </ChatbotCTA>
  ),
  
  FindPropertiesIn: (location: string, props?: Partial<ChatbotCTAProps>) => (
    <ChatbotCTA query={{ type: 'location', location }} {...props}>
      Find Properties in {location}
    </ChatbotCTA>
  ),
  
  // Property type CTAs
  SearchPropertyType: (propertyType: string, location?: string, props?: Partial<ChatbotCTAProps>) => (
    <ChatbotCTA query={{ type: 'property-type', propertyType, location }} {...props}>
      Search {propertyType}s
    </ChatbotCTA>
  ),
  
  // Service CTAs
  BookConsultation: (service: string, props?: Partial<ChatbotCTAProps>) => (
    <ChatbotCTA query={{ type: 'service', service }} {...props}>
      Book {service} Consultation
    </ChatbotCTA>
  ),
};

export default ChatbotCTA;

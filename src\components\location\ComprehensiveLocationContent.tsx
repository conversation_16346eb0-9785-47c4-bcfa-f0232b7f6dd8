// STORY-011: Comprehensive Location Content Display Component
import { comprehensiveLocationContent } from '@/seed/comprehensive-content'

interface ComprehensiveLocationContentProps {
  locationSlug: string
}

export default function ComprehensiveLocationContent({ locationSlug }: ComprehensiveLocationContentProps) {
  // Get comprehensive content for this location
  const locationData = comprehensiveLocationContent[locationSlug as keyof typeof comprehensiveLocationContent]
  
  if (!locationData) {
    return null
  }

  return (
    <div className="space-y-16">
      {/* Market Analysis Section */}
      <section className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">Market Analysis & Investment Data</h2>
        
        <div className="grid md:grid-cols-2 gap-8">
          {/* Rental Yield Information */}
          <div className="bg-emerald-50 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-emerald-900 mb-4">Rental Yield Performance</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-700">Average Yield:</span>
                <span className="font-semibold text-emerald-600">{locationData.marketAnalysis.rentalYield.averageYield}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-700">Yield Range:</span>
                <span className="font-semibold">{locationData.marketAnalysis.rentalYield.yieldRange}</span>
              </div>
            </div>
            <div className="mt-4 text-sm text-gray-600" 
                 dangerouslySetInnerHTML={{ __html: locationData.marketAnalysis.rentalYield.seasonalDemand.replace(/<[^>]*>/g, '').substring(0, 200) + '...' }} />
          </div>

          {/* Price Trends */}
          <div className="bg-blue-50 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-blue-900 mb-4">Price Trends & Growth</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-700">Annual Growth:</span>
                <span className="font-semibold text-blue-600">{locationData.marketAnalysis.pricetrends.priceGrowth}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-700">ROI Timeline:</span>
                <span className="font-semibold">{locationData.marketAnalysis.roiCalculation.averageROI}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Investment Highlights */}
        <div className="mt-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">Investment Highlights</h3>
          <div className="grid md:grid-cols-2 gap-4">
            {locationData.marketAnalysis.pricetrends.investmentHighlights.map((highlight, index) => (
              <div key={index} className="flex items-center">
                <div className="flex-shrink-0 w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                <span className="text-gray-700">{highlight.highlight}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Neighborhoods Section */}
      <section className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">Neighborhoods & Sub-Areas</h2>
        
        <div className="grid md:grid-cols-2 gap-6">
          {locationData.neighborhoods.map((neighborhood, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">{neighborhood.name}</h3>
              <p className="text-gray-600 mb-4">{neighborhood.description}</p>
              <div className="bg-emerald-50 rounded-md p-3">
                <span className="text-sm font-medium text-emerald-800">Best For: </span>
                <span className="text-sm text-emerald-700">{neighborhood.bestFor}</span>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Cost of Living Section */}
      <section className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">Cost of Living Guide</h2>
        
        <div className="grid md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Monthly Budget</h3>
            <div className="text-2xl font-bold text-blue-600 mb-2">
              {locationSlug === 'seminyak' ? '$2,500-5,000' :
               locationSlug === 'jimbaran' ? '$2,000-4,500' :
               locationSlug === 'canggu' ? '$1,500-3,500' :
               locationSlug === 'sanur' ? '$1,400-3,200' : '$1,200-2,800'}
            </div>
            <p className="text-sm text-gray-600">Per month for comfortable living</p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Housing Costs</h3>
            <div className="text-2xl font-bold text-green-600 mb-2">
              {locationSlug === 'seminyak' ? '$1,200-3,000' :
               locationSlug === 'jimbaran' ? '$1,200-5,000' :
               locationSlug === 'canggu' ? '$800-2,500' :
               locationSlug === 'sanur' ? '$800-2,500' : '$600-2,000'}
            </div>
            <p className="text-sm text-gray-600">Monthly villa rental range</p>
          </div>
          
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Daily Expenses</h3>
            <div className="text-2xl font-bold text-purple-600 mb-2">$30-80</div>
            <p className="text-sm text-gray-600">Food, transport, entertainment</p>
          </div>
        </div>
      </section>

      {/* Legal & Regulatory Information */}
      <section className="bg-white rounded-lg shadow-lg p-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">Legal & Regulatory Information</h2>
        
        <div className="grid md:grid-cols-2 gap-8">
          <div>
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Foreign Ownership Options</h3>
            <div className="space-y-4">
              <div className="border-l-4 border-emerald-500 pl-4">
                <h4 className="font-semibold text-gray-900">Leasehold (25-30 years)</h4>
                <p className="text-gray-600 text-sm">Most straightforward option for foreigners with renewal possibilities</p>
              </div>
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-semibold text-gray-900">Freehold via PMA</h4>
                <p className="text-gray-600 text-sm">Company structure providing strongest ownership rights</p>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Visa & Tax Information</h3>
            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-2">Available Visas</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Visit Visa (B211) - 60 days</li>
                  <li>• Business Visa (B213) - Extended stays</li>
                  <li>• Investment Visa (B213A) - For investors</li>
                  <li>• Golden Visa - Long-term residence</li>
                </ul>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 mb-2">Tax Considerations</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Acquisition tax: 5% of property value</li>
                  <li>• Annual property tax: 0.1-0.3%</li>
                  <li>• Rental income tax: 20% (non-residents)</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Infrastructure & Services */}
      <section className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">Infrastructure & Services</h2>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Internet</h3>
            <p className="text-sm text-gray-600">50-100+ Mbps fiber optic</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Healthcare</h3>
            <p className="text-sm text-gray-600">International clinics & hospitals</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Banking</h3>
            <p className="text-sm text-gray-600">International banks & ATMs</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Safety</h3>
            <p className="text-sm text-gray-600">Low crime, tourist police</p>
          </div>
        </div>
      </section>

      {/* SEO Keywords Display */}
      <section className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Target Keywords (SEO)</h3>
        <div className="flex flex-wrap gap-2">
          {locationData.targetKeywords.map((keyword, index) => (
            <span key={index} className="bg-emerald-100 text-emerald-800 px-3 py-1 rounded-full text-sm">
              {keyword.keyword}
            </span>
          ))}
        </div>
      </section>
    </div>
  )
}

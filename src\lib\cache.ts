/**
 * Advanced Caching System
 * 
 * Multi-layer caching with memory, localStorage, and service worker support
 */

// Cache configuration
export const CACHE_CONFIG = {
  // Memory cache settings
  memory: {
    maxSize: 50 * 1024 * 1024, // 50MB
    maxAge: 5 * 60 * 1000,     // 5 minutes
    cleanupInterval: 60 * 1000, // 1 minute
  },
  
  // LocalStorage cache settings
  localStorage: {
    maxSize: 10 * 1024 * 1024, // 10MB
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    keyPrefix: 'bali-re-cache-',
  },
  
  // API cache settings
  api: {
    defaultTTL: 5 * 60 * 1000,  // 5 minutes
    longTTL: 60 * 60 * 1000,    // 1 hour
    shortTTL: 30 * 1000,        // 30 seconds
  },
  
  // Static resource cache
  static: {
    images: 7 * 24 * 60 * 60 * 1000,    // 7 days
    fonts: 30 * 24 * 60 * 60 * 1000,    // 30 days
    scripts: 24 * 60 * 60 * 1000,       // 24 hours
    styles: 24 * 60 * 60 * 1000,        // 24 hours
  },
};

// Cache entry interface
interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  size: number;
  hits: number;
  lastAccessed: number;
}

// Memory cache implementation
class MemoryCache {
  private cache = new Map<string, CacheEntry>();
  private currentSize = 0;
  private cleanupTimer?: NodeJS.Timeout;

  constructor() {
    this.startCleanup();
  }

  set<T>(key: string, data: T, ttl: number = CACHE_CONFIG.memory.maxAge): void {
    const size = this.calculateSize(data);
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      size,
      hits: 0,
      lastAccessed: Date.now(),
    };

    // Remove existing entry if it exists
    if (this.cache.has(key)) {
      const existing = this.cache.get(key)!;
      this.currentSize -= existing.size;
    }

    // Ensure we don't exceed max size
    this.ensureCapacity(size);

    this.cache.set(key, entry);
    this.currentSize += size;
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key) as CacheEntry<T> | undefined;
    
    if (!entry) return null;
    
    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.delete(key);
      return null;
    }

    // Update access statistics
    entry.hits++;
    entry.lastAccessed = Date.now();

    return entry.data;
  }

  delete(key: string): boolean {
    const entry = this.cache.get(key);
    if (entry) {
      this.currentSize -= entry.size;
      return this.cache.delete(key);
    }
    return false;
  }

  clear(): void {
    this.cache.clear();
    this.currentSize = 0;
  }

  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.delete(key);
      return false;
    }
    
    return true;
  }

  getStats() {
    return {
      size: this.cache.size,
      currentSize: this.currentSize,
      maxSize: CACHE_CONFIG.memory.maxSize,
      utilization: (this.currentSize / CACHE_CONFIG.memory.maxSize) * 100,
    };
  }

  private calculateSize(data: any): number {
    return JSON.stringify(data).length * 2; // Rough estimate (UTF-16)
  }

  private ensureCapacity(requiredSize: number): void {
    while (this.currentSize + requiredSize > CACHE_CONFIG.memory.maxSize && this.cache.size > 0) {
      // Remove least recently used entry
      let lruKey = '';
      let lruTime = Date.now();
      
      for (const [key, entry] of this.cache.entries()) {
        if (entry.lastAccessed < lruTime) {
          lruTime = entry.lastAccessed;
          lruKey = key;
        }
      }
      
      if (lruKey) {
        this.delete(lruKey);
      } else {
        break;
      }
    }
  }

  private startCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      const now = Date.now();
      for (const [key, entry] of this.cache.entries()) {
        if (now - entry.timestamp > entry.ttl) {
          this.delete(key);
        }
      }
    }, CACHE_CONFIG.memory.cleanupInterval);
  }

  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.clear();
  }
}

// LocalStorage cache implementation
class LocalStorageCache {
  private keyPrefix = CACHE_CONFIG.localStorage.keyPrefix;

  set<T>(key: string, data: T, ttl: number = CACHE_CONFIG.localStorage.maxAge): void {
    if (typeof window === 'undefined') return;

    try {
      const entry: CacheEntry<T> = {
        data,
        timestamp: Date.now(),
        ttl,
        size: 0,
        hits: 0,
        lastAccessed: Date.now(),
      };

      const serialized = JSON.stringify(entry);
      entry.size = serialized.length;

      // Check storage quota
      this.ensureStorageCapacity(serialized.length);

      localStorage.setItem(this.keyPrefix + key, serialized);
    } catch (error) {
      console.warn('LocalStorage cache set failed:', error);
    }
  }

  get<T>(key: string): T | null {
    if (typeof window === 'undefined') return null;

    try {
      const item = localStorage.getItem(this.keyPrefix + key);
      if (!item) return null;

      const entry: CacheEntry<T> = JSON.parse(item);
      
      // Check if expired
      if (Date.now() - entry.timestamp > entry.ttl) {
        this.delete(key);
        return null;
      }

      // Update access statistics
      entry.hits++;
      entry.lastAccessed = Date.now();
      localStorage.setItem(this.keyPrefix + key, JSON.stringify(entry));

      return entry.data;
    } catch (error) {
      console.warn('LocalStorage cache get failed:', error);
      return null;
    }
  }

  delete(key: string): boolean {
    if (typeof window === 'undefined') return false;

    try {
      localStorage.removeItem(this.keyPrefix + key);
      return true;
    } catch (error) {
      console.warn('LocalStorage cache delete failed:', error);
      return false;
    }
  }

  clear(): void {
    if (typeof window === 'undefined') return;

    try {
      const keys = Object.keys(localStorage).filter(key => 
        key.startsWith(this.keyPrefix)
      );
      keys.forEach(key => localStorage.removeItem(key));
    } catch (error) {
      console.warn('LocalStorage cache clear failed:', error);
    }
  }

  has(key: string): boolean {
    if (typeof window === 'undefined') return false;
    return this.get(key) !== null;
  }

  private ensureStorageCapacity(requiredSize: number): void {
    if (typeof window === 'undefined') return;

    try {
      // Estimate current usage
      let currentSize = 0;
      const cacheKeys: string[] = [];
      
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith(this.keyPrefix)) {
          const value = localStorage.getItem(key);
          if (value) {
            currentSize += key.length + value.length;
            cacheKeys.push(key);
          }
        }
      }

      // If we're approaching the limit, clean up old entries
      while (currentSize + requiredSize > CACHE_CONFIG.localStorage.maxSize && cacheKeys.length > 0) {
        // Find oldest entry
        let oldestKey = '';
        let oldestTime = Date.now();
        
        for (const key of cacheKeys) {
          try {
            const item = localStorage.getItem(key);
            if (item) {
              const entry = JSON.parse(item);
              if (entry.timestamp < oldestTime) {
                oldestTime = entry.timestamp;
                oldestKey = key;
              }
            }
          } catch (error) {
            // Invalid entry, remove it
            localStorage.removeItem(key);
          }
        }
        
        if (oldestKey) {
          const value = localStorage.getItem(oldestKey);
          if (value) {
            currentSize -= oldestKey.length + value.length;
          }
          localStorage.removeItem(oldestKey);
          cacheKeys.splice(cacheKeys.indexOf(oldestKey), 1);
        } else {
          break;
        }
      }
    } catch (error) {
      console.warn('Storage capacity management failed:', error);
    }
  }
}

// Main cache manager
class CacheManager {
  private memoryCache = new MemoryCache();
  private localStorageCache = new LocalStorageCache();

  // Get from cache with fallback strategy
  async get<T>(key: string, fallback?: () => Promise<T>, ttl?: number): Promise<T | null> {
    // Try memory cache first
    let data = this.memoryCache.get<T>(key);
    if (data !== null) return data;

    // Try localStorage cache
    data = this.localStorageCache.get<T>(key);
    if (data !== null) {
      // Promote to memory cache
      this.memoryCache.set(key, data, ttl);
      return data;
    }

    // Use fallback if provided
    if (fallback) {
      try {
        data = await fallback();
        if (data !== null) {
          this.set(key, data, ttl);
        }
        return data;
      } catch (error) {
        console.warn('Cache fallback failed:', error);
        return null;
      }
    }

    return null;
  }

  // Set in both caches
  set<T>(key: string, data: T, ttl?: number): void {
    const effectiveTTL = ttl || CACHE_CONFIG.api.defaultTTL;
    
    this.memoryCache.set(key, data, effectiveTTL);
    this.localStorageCache.set(key, data, effectiveTTL);
  }

  // Delete from both caches
  delete(key: string): void {
    this.memoryCache.delete(key);
    this.localStorageCache.delete(key);
  }

  // Clear both caches
  clear(): void {
    this.memoryCache.clear();
    this.localStorageCache.clear();
  }

  // Check if key exists in any cache
  has(key: string): boolean {
    return this.memoryCache.has(key) || this.localStorageCache.has(key);
  }

  // Get cache statistics
  getStats() {
    return {
      memory: this.memoryCache.getStats(),
      localStorage: {
        available: typeof window !== 'undefined',
        keyPrefix: this.localStorageCache['keyPrefix'],
      },
    };
  }

  // Destroy cache manager
  destroy(): void {
    this.memoryCache.destroy();
  }
}

// Global cache instance
export const cache = new CacheManager();

// Cache key generators
export const cacheKeys = {
  location: (slug: string) => `location:${slug}`,
  locations: () => 'locations:all',
  property: (id: string) => `property:${id}`,
  properties: (filters: string) => `properties:${filters}`,
  search: (query: string) => `search:${query}`,
  user: (id: string) => `user:${id}`,
  api: (endpoint: string, params?: Record<string, any>) => 
    `api:${endpoint}${params ? ':' + JSON.stringify(params) : ''}`,
};

// Cache decorators
export function cached<T extends (...args: any[]) => Promise<any>>(
  keyGenerator: (...args: Parameters<T>) => string,
  ttl?: number
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: Parameters<T>) {
      const key = keyGenerator(...args);
      
      return cache.get(key, async () => {
        return method.apply(this, args);
      }, ttl);
    };

    return descriptor;
  };
}

// Utility functions
export function invalidateCache(pattern: string): void {
  // This would require implementing pattern matching for cache keys
  // For now, we'll clear all caches
  cache.clear();
}

export function warmupCache(keys: string[], fetcher: (key: string) => Promise<any>): Promise<void[]> {
  return Promise.all(
    keys.map(async (key) => {
      if (!cache.has(key)) {
        try {
          const data = await fetcher(key);
          cache.set(key, data);
        } catch (error) {
          console.warn(`Cache warmup failed for key ${key}:`, error);
        }
      }
    })
  );
}

export default cache;

/**
 * Footer Component
 *
 * Website footer with links, company information, and contact details.
 * Includes responsive design and consistent styling.
 */

import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { InteractiveCoverageTags } from '@/components/ui/InteractiveCoverageTags';
import { ClientOnly } from '@/lib/hydration-safe';

export interface FooterProps {
  /** Custom className */
  className?: string;
}

const footerLinks = {
  locations: [
    { id: 'canggu', label: 'Canggu', type: 'location' as const, priority: 'high' as const, description: 'Surf, coworking, trendy cafes' },
    { id: 'ubud', label: 'Ubud', type: 'location' as const, priority: 'high' as const, description: 'Culture, yoga, rice fields' },
    { id: 'seminyak', label: 'Seminyak', type: 'location' as const, priority: 'high' as const, description: 'Beach clubs, fine dining' },
    { id: 'sanur', label: 'Sanur', type: 'location' as const, priority: 'medium' as const, description: 'Family-friendly, calm beach' },
    { id: 'jimbaran', label: 'Jimbaran', type: 'location' as const, priority: 'medium' as const, description: 'Sunset views, seafood' },
  ],
  properties: [
    { name: 'Private Villas', href: '/properties/villa' },
    { name: 'Guesthouses', href: '/properties/guesthouse' },
    { name: 'Modern Apartments', href: '/properties/apartment' },
    { name: 'All Properties', href: '/properties' },
  ],
  services: [
    { name: 'Investment Consultation', href: '/services/investment' },
    { name: 'Property Purchase Assistance', href: '/services/purchase' },
    { name: 'Consultation & Viewing', href: '/services/consultation' },
  ],
  company: [
    { name: 'About Us', href: '/about' },
    { name: 'Contact', href: '/contact' },
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' },
  ],
};

const socialLinks = [
  {
    name: 'Facebook',
    href: '#',
    icon: (
      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
      </svg>
    ),
  },
  {
    name: 'Instagram',
    href: '#',
    icon: (
      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323C6.001 8.198 7.152 7.708 8.449 7.708s2.448.49 3.323 1.416c.875.875 1.365 2.026 1.365 3.323s-.49 2.448-1.365 3.323c-.875.807-2.026 1.218-3.323 1.218zm7.718-1.297c-.875.807-2.026 1.297-3.323 1.297s-2.448-.49-3.323-1.297c-.875-.875-1.365-2.026-1.365-3.323s.49-2.448 1.365-3.323c.875-.926 2.026-1.416 3.323-1.416s2.448.49 3.323 1.416c.875.875 1.365 2.026 1.365 3.323s-.49 2.448-1.365 3.323z"/>
      </svg>
    ),
  },
  {
    name: 'LinkedIn',
    href: '#',
    icon: (
      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
      </svg>
    ),
  },
];

export const Footer: React.FC<FooterProps> = ({ className }) => {
  const currentYear = new Date().getFullYear();

  return (
    <footer
      className={cn('text-white', className)}
      style={{ backgroundColor: 'var(--brand-primary)' }}
    >
      <div className="container mx-auto container-padding section-spacing">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Company Info with Trust Elements */}
          <div className="lg:col-span-2">
            <div className="flex items-center mb-4">
              <div
                className="w-8 h-8 rounded-lg flex items-center justify-center mr-3"
                style={{ backgroundColor: 'var(--neutral-white)' }}
              >
                <span
                  className="font-bold text-sm"
                  style={{ color: 'var(--brand-primary)' }}
                >
                  BPS
                </span>
              </div>
              <span className="text-xl font-bold">Bali Property Scout</span>
            </div>
            <p className="text-white/90 mb-6 leading-relaxed">
              AI-powered property discovery meets local expertise.
              Your trusted partner for Bali real estate since 2019.
            </p>

            {/* Trust Indicators */}
            <div className="mb-6 space-y-3">
              <div className="flex items-center gap-3 text-sm text-white/80">
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <span>500+ Satisfied Clients</span>
              </div>
              <div className="flex items-center gap-3 text-sm text-white/80">
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <span>AI-Powered Property Matching</span>
              </div>
              <div className="flex items-center gap-3 text-sm text-white/80">
                <div className="w-2 h-2 bg-white rounded-full"></div>
                <span>Local Market Expertise</span>
              </div>
            </div>

            {/* Social Links */}
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <a
                  key={social.name}
                  href={social.href}
                  className="text-white/60 hover:text-white transition-colors"
                  aria-label={social.name}
                >
                  {social.icon}
                </a>
              ))}
            </div>
          </div>

          {/* Clean Location Tags */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-white">Popular Locations</h3>
            <ClientOnly fallback={<div className="text-white/60">Loading locations...</div>}>
              <div>
                {footerLinks?.locations && (
                  <InteractiveCoverageTags
                    tags={footerLinks.locations}
                    variant="compact"
                    showPriority={false}
                    animated={true}
                    className="[&_button]:!bg-white/10 [&_button]:!text-white [&_button]:!border-white/20 [&_button:hover]:!bg-white/20 [&_button]:!shadow-none"
                  />
                )}
              </div>
            </ClientOnly>
          </div>

          {/* Property Types */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-white">Property Types</h3>
            <ul className="space-y-2">
              {footerLinks.properties.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-white/70 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Core Services Only */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-white">Our Services</h3>
            <ul className="space-y-2">
              {footerLinks.services.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-white/70 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Clean Bottom Section with Contact Info */}
        <div className="border-t border-white/20 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-white/60 text-sm mb-4 md:mb-0">
              © {currentYear} Bali Property Scout. All rights reserved.
            </div>
            <div className="flex flex-col md:flex-row items-center gap-4 md:gap-6 text-sm">
              <div className="flex space-x-6">
                {footerLinks.company.map((link) => (
                  <Link
                    key={link.name}
                    href={link.href}
                    className="text-white/60 hover:text-white transition-colors"
                  >
                    {link.name}
                  </Link>
                ))}
              </div>
              <div className="text-white/60 text-xs">
                Secure SSL • Trusted by 500+ Clients
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

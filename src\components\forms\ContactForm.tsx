'use client'

import { useState } from 'react'
import { trackConversion } from '@/components/analytics/GoogleAnalytics'

interface ContactFormProps {
  formType?: 'general' | 'property-interest' | 'consultation'
  source?: string
  locationSlug?: string
  propertyType?: string
}

interface FormData {
  name: string
  email: string
  phone: string
  message: string
  leadType: string
  source: string
  budget?: {
    budgetRange: string
    budgetFlexible: boolean
  }
  timeline: string
  propertyPreferences?: {
    preferredLocations: string[]
    propertyType: string[]
    transactionType: string
  }
  personalInfo?: {
    currentLocation: string
    occupation: string
    familySize: number
    previousBaliExperience: boolean
  }
  emailOptIn: boolean
  gdprConsent: boolean
}

export default function ContactForm({ 
  formType = 'general', 
  source = 'contact-form',
  locationSlug,
  propertyType 
}: ContactFormProps) {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    message: '',
    leadType: formType,
    source: source,
    timeline: '',
    emailOptIn: true,
    gdprConsent: false,
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [showAdvanced, setShowAdvanced] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked
      setFormData(prev => ({ ...prev, [name]: checked }))
    } else if (name.includes('.')) {
      // Handle nested properties
      const [parent, child] = name.split('.')
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof FormData],
          [child]: value
        }
      }))
    } else {
      setFormData(prev => ({ ...prev, [name]: value }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setSubmitStatus('idle')

    try {
      // Add location and property type if provided
      const submitData = {
        ...formData,
        propertyPreferences: {
          ...formData.propertyPreferences,
          preferredLocations: locationSlug ? [locationSlug] : formData.propertyPreferences?.preferredLocations || [],
          propertyType: propertyType ? [propertyType] : formData.propertyPreferences?.propertyType || [],
        }
      }

      const response = await fetch('/api/leads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (response.ok) {
        setSubmitStatus('success')
        trackConversion('contact_form_submission')
        
        // Reset form
        setFormData({
          name: '',
          email: '',
          phone: '',
          message: '',
          leadType: formType,
          source: source,
          timeline: '',
          emailOptIn: true,
          gdprConsent: false,
        })
      } else {
        setSubmitStatus('error')
      }
    } catch (error) {
      console.error('Form submission error:', error)
      setSubmitStatus('error')
    } finally {
      setIsSubmitting(false)
    }
  }

  const getFormTitle = () => {
    switch (formType) {
      case 'property-interest':
        return 'Property Interest Form'
      case 'consultation':
        return 'Free Consultation Request'
      default:
        return 'Contact Us'
    }
  }

  const getFormDescription = () => {
    switch (formType) {
      case 'property-interest':
        return 'Tell us about your property requirements and we\'ll find the perfect match for you.'
      case 'consultation':
        return 'Get expert advice on Bali real estate, visa requirements, and investment opportunities.'
      default:
        return 'Get in touch with our Bali real estate experts. We\'re here to help with all your property needs.'
    }
  }

  if (submitStatus === 'success') {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
        <div className="text-green-600 text-4xl mb-4">✓</div>
        <h3 className="text-lg font-semibold text-green-800 mb-2">Thank You!</h3>
        <p className="text-green-700 mb-4">
          Your message has been received. Our team will get back to you within 24 hours.
        </p>
        <button
          onClick={() => setSubmitStatus('idle')}
          className="text-green-600 hover:text-green-800 font-medium"
        >
          Send Another Message
        </button>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">{getFormTitle()}</h2>
        <p className="text-gray-600">{getFormDescription()}</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Full Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              required
              value={formData.name}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
            />
          </div>
          
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email Address *
            </label>
            <input
              type="email"
              id="email"
              name="email"
              required
              value={formData.email}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
            />
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
              Phone Number
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
            />
          </div>
          
          <div>
            <label htmlFor="timeline" className="block text-sm font-medium text-gray-700 mb-1">
              Timeline *
            </label>
            <select
              id="timeline"
              name="timeline"
              required
              value={formData.timeline}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
            >
              <option value="">Select timeline</option>
              <option value="immediate">Immediate (within 1 month)</option>
              <option value="short-term">Short-term (1-3 months)</option>
              <option value="medium-term">Medium-term (3-6 months)</option>
              <option value="long-term">Long-term (6+ months)</option>
              <option value="research">Just researching</option>
            </select>
          </div>
        </div>

        {/* Message */}
        <div>
          <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
            Message *
          </label>
          <textarea
            id="message"
            name="message"
            required
            rows={4}
            value={formData.message}
            onChange={handleInputChange}
            placeholder="Tell us about your property requirements, budget, preferred locations, or any questions you have..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
          />
        </div>

        {/* Advanced Options Toggle */}
        <div>
          <button
            type="button"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="text-emerald-600 hover:text-emerald-700 font-medium text-sm"
          >
            {showAdvanced ? '− Hide' : '+ Show'} Additional Options (helps us serve you better)
          </button>
        </div>

        {/* Advanced Fields */}
        {showAdvanced && (
          <div className="space-y-4 border-t pt-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="budget.budgetRange" className="block text-sm font-medium text-gray-700 mb-1">
                  Budget Range
                </label>
                <select
                  id="budget.budgetRange"
                  name="budget.budgetRange"
                  value={formData.budget?.budgetRange || ''}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                >
                  <option value="">Select budget range</option>
                  <option value="under-500">Under $500/month</option>
                  <option value="500-1000">$500-1000/month</option>
                  <option value="1000-2000">$1000-2000/month</option>
                  <option value="2000-3000">$2000-3000/month</option>
                  <option value="over-3000">Over $3000/month</option>
                  <option value="under-100k">Under $100k purchase</option>
                  <option value="100k-300k">$100k-300k purchase</option>
                  <option value="300k-500k">$300k-500k purchase</option>
                  <option value="over-500k">Over $500k purchase</option>
                </select>
              </div>
              
              <div>
                <label htmlFor="personalInfo.occupation" className="block text-sm font-medium text-gray-700 mb-1">
                  Occupation/Situation
                </label>
                <select
                  id="personalInfo.occupation"
                  name="personalInfo.occupation"
                  value={formData.personalInfo?.occupation || ''}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                >
                  <option value="">Select occupation</option>
                  <option value="digital-nomad">Digital Nomad</option>
                  <option value="remote-worker">Remote Worker</option>
                  <option value="entrepreneur">Entrepreneur</option>
                  <option value="retiree">Retiree</option>
                  <option value="student">Student</option>
                  <option value="family">Family</option>
                  <option value="investor">Investor</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>

            <div>
              <label htmlFor="personalInfo.currentLocation" className="block text-sm font-medium text-gray-700 mb-1">
                Current Location/Country
              </label>
              <input
                type="text"
                id="personalInfo.currentLocation"
                name="personalInfo.currentLocation"
                value={formData.personalInfo?.currentLocation || ''}
                onChange={handleInputChange}
                placeholder="e.g., United States, Australia, Germany"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
              />
            </div>
          </div>
        )}

        {/* Consent Checkboxes */}
        <div className="space-y-3">
          <label className="flex items-start">
            <input
              type="checkbox"
              name="emailOptIn"
              checked={formData.emailOptIn}
              onChange={handleInputChange}
              className="mt-1 mr-3 h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"
            />
            <span className="text-sm text-gray-700">
              I'd like to receive helpful tips, market updates, and property recommendations via email.
            </span>
          </label>
          
          <label className="flex items-start">
            <input
              type="checkbox"
              name="gdprConsent"
              required
              checked={formData.gdprConsent}
              onChange={handleInputChange}
              className="mt-1 mr-3 h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"
            />
            <span className="text-sm text-gray-700">
              I consent to the processing of my personal data for the purpose of this inquiry. *
            </span>
          </label>
        </div>

        {/* Submit Button */}
        <div>
          <button
            type="submit"
            disabled={isSubmitting || !formData.gdprConsent}
            className="w-full bg-emerald-600 text-white px-6 py-3 rounded-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
          >
            {isSubmitting ? 'Sending...' : 'Send Message'}
          </button>
        </div>

        {submitStatus === 'error' && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-700 text-sm">
              There was an error sending your message. Please try again or contact us directly.
            </p>
          </div>
        )}
      </form>
    </div>
  )
}

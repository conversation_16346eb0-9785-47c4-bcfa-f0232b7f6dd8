/**
 * Button Component
 * 
 * A flexible button component with multiple variants, sizes, and states.
 * Supports all standard button functionality with consistent styling.
 */

import React from 'react';
import { cn } from '@/lib/utils';

// Button variant types
export type ButtonVariant = 
  | 'primary' 
  | 'secondary' 
  | 'outline' 
  | 'ghost' 
  | 'destructive'
  | 'success'
  | 'warning';

export type ButtonSize = 'sm' | 'md' | 'lg' | 'xl';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** Button visual variant */
  variant?: ButtonVariant;
  /** Button size */
  size?: ButtonSize;
  /** Whether button is in loading state */
  loading?: boolean;
  /** Whether button should take full width */
  fullWidth?: boolean;
  /** Icon to display before text */
  leftIcon?: React.ReactNode;
  /** Icon to display after text */
  rightIcon?: React.ReactNode;
  /** Custom className */
  className?: string;
  /** Button content */
  children?: React.ReactNode;
  /** Whether to render as child element (for Link components) */
  asChild?: boolean;
}

// Button variant styles
const buttonVariants = {
  primary: [
    'bg-primary-500 text-white',
    'hover:bg-primary-600 active:bg-primary-700',
    'focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
    'disabled:bg-primary-300 disabled:cursor-not-allowed',
  ].join(' '),
  
  secondary: [
    'bg-secondary-500 text-white',
    'hover:bg-secondary-600 active:bg-secondary-700',
    'focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2',
    'disabled:bg-secondary-300 disabled:cursor-not-allowed',
  ].join(' '),
  
  outline: [
    'bg-transparent text-neutral-700 border-2 border-neutral-300',
    'hover:bg-neutral-50 hover:border-neutral-400',
    'active:bg-neutral-100',
    'focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
    'disabled:text-neutral-400 disabled:border-neutral-200 disabled:cursor-not-allowed',
  ].join(' '),
  
  ghost: [
    'bg-transparent text-neutral-700',
    'hover:bg-neutral-100 active:bg-neutral-200',
    'focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
    'disabled:text-neutral-400 disabled:cursor-not-allowed',
  ].join(' '),
  
  destructive: [
    'bg-red-500 text-white',
    'hover:bg-red-600 active:bg-red-700',
    'focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
    'disabled:bg-red-300 disabled:cursor-not-allowed',
  ].join(' '),
  
  success: [
    'bg-green-500 text-white',
    'hover:bg-green-600 active:bg-green-700',
    'focus:ring-2 focus:ring-green-500 focus:ring-offset-2',
    'disabled:bg-green-300 disabled:cursor-not-allowed',
  ].join(' '),
  
  warning: [
    'bg-yellow-500 text-white',
    'hover:bg-yellow-600 active:bg-yellow-700',
    'focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2',
    'disabled:bg-yellow-300 disabled:cursor-not-allowed',
  ].join(' '),
};

// Button size styles
const buttonSizes = {
  sm: 'px-3 py-1.5 text-sm font-medium rounded-md min-h-[32px]',
  md: 'px-4 py-2 text-sm font-medium rounded-md min-h-[40px]',
  lg: 'px-6 py-3 text-base font-medium rounded-lg min-h-[48px]',
  xl: 'px-8 py-4 text-lg font-semibold rounded-lg min-h-[56px]',
};

// Loading spinner component
const LoadingSpinner: React.FC<{ size: ButtonSize }> = ({ size }) => {
  const spinnerSizes = {
    sm: 'w-4 h-4',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
    xl: 'w-6 h-6',
  };

  return (
    <svg
      className={cn('animate-spin', spinnerSizes[size])}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );
};

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      loading = false,
      fullWidth = false,
      leftIcon,
      rightIcon,
      className,
      children,
      disabled,
      asChild = false,
      ...props
    },
    ref
  ) => {
    const isDisabled = disabled || loading;

    const buttonClasses = cn(
      // Base styles
      'inline-flex items-center justify-center gap-2',
      'font-medium transition-all duration-200',
      'focus:outline-none focus:ring-offset-white',
      'disabled:opacity-60 disabled:pointer-events-none',

      // Variant styles
      buttonVariants[variant],

      // Size styles
      buttonSizes[size],

      // Full width
      fullWidth && 'w-full',

      // Custom className
      className
    );

    const buttonContent = (
      <>
        {/* Left icon or loading spinner */}
        {loading ? (
          <LoadingSpinner size={size} />
        ) : leftIcon ? (
          <span className="flex-shrink-0">{leftIcon}</span>
        ) : null}

        {/* Button text */}
        {children && !asChild && (
          <span className={loading ? 'opacity-0' : ''}>{children}</span>
        )}

        {/* Right icon */}
        {!loading && rightIcon && (
          <span className="flex-shrink-0">{rightIcon}</span>
        )}
      </>
    );

    if (asChild && React.isValidElement(children)) {
      return React.cloneElement(children, {
        className: cn(buttonClasses, children.props.className),
        ...props,
      });
    }

    return (
      <button
        ref={ref}
        className={buttonClasses}
        disabled={isDisabled}
        {...props}
      >
        {buttonContent}
      </button>
    );
  }
);

Button.displayName = 'Button';

// Button group component for related actions
export interface ButtonGroupProps {
  children: React.ReactNode;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  spacing?: 'none' | 'sm' | 'md' | 'lg';
}

export const ButtonGroup: React.FC<ButtonGroupProps> = ({
  children,
  className,
  orientation = 'horizontal',
  spacing = 'sm',
}) => {
  const spacingClasses = {
    none: '',
    sm: orientation === 'horizontal' ? 'space-x-2' : 'space-y-2',
    md: orientation === 'horizontal' ? 'space-x-4' : 'space-y-4',
    lg: orientation === 'horizontal' ? 'space-x-6' : 'space-y-6',
  };

  return (
    <div
      className={cn(
        'flex',
        orientation === 'horizontal' ? 'flex-row' : 'flex-col',
        spacingClasses[spacing],
        className
      )}
    >
      {children}
    </div>
  );
};

// Icon button variant for buttons with only icons
export interface IconButtonProps extends Omit<ButtonProps, 'leftIcon' | 'rightIcon' | 'children'> {
  icon: React.ReactNode;
  'aria-label': string;
}

export const IconButton = React.forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ icon, size = 'md', className, ...props }, ref) => {
    const iconSizes = {
      sm: 'w-8 h-8',
      md: 'w-10 h-10',
      lg: 'w-12 h-12',
      xl: 'w-14 h-14',
    };

    return (
      <Button
        ref={ref}
        size={size}
        className={cn(
          'p-0 aspect-square',
          iconSizes[size],
          className
        )}
        {...props}
      >
        {icon}
      </Button>
    );
  }
);

IconButton.displayName = 'IconButton';

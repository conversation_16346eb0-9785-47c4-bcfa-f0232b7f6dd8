/**
 * Navigation API Route
 * Bali Property Scout Website
 * 
 * Provides navigation data from CMS with fallback to static data.
 * Server-side only to avoid browser/database conflicts.
 */

import { NextRequest, NextResponse } from 'next/server';
import { getLocations, getPropertyTypes } from '@/lib/payload';

export interface NavigationItem {
  label: string;
  href: string;
  children?: NavigationItem[];
}

// Static navigation fallback
const STATIC_NAVIGATION: NavigationItem[] = [
  {
    label: 'Home',
    href: '/',
  },
  {
    label: 'Locations',
    href: '/locations',
    children: [
      { label: 'All Locations', href: '/locations' },
      { label: 'Canggu', href: '/locations/canggu' },
      { label: 'Ubud', href: '/locations/ubud' },
      { label: 'Seminyak', href: '/locations/seminyak' },
      { label: 'Sanur', href: '/locations/sanur' },
      { label: 'Jimbaran', href: '/locations/jimbaran' },
    ],
  },
  {
    label: 'Property Types',
    href: '/property-types',
    children: [
      { label: 'All Property Types', href: '/property-types' },
      { label: 'Villa', href: '/property-types/villa' },
      { label: 'Apartment', href: '/property-types/apartment' },
      { label: 'Guesthouse', href: '/property-types/guesthouse' },
      { label: 'Land', href: '/property-types/land' },
    ],
  },
  {
    label: 'Services',
    href: '/services',
  },
  {
    label: 'About',
    href: '/about',
  },
  {
    label: 'Contact',
    href: '/contact',
  },
];

async function buildNavigationItems(): Promise<NavigationItem[]> {
  try {
    console.log('🔄 Building navigation from CMS...');
    const startTime = Date.now();

    // Fetch data with timeout (increased for database performance)
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Navigation fetch timeout')), 10000);
    });

    const dataPromise = Promise.all([
      getLocations(),
      getPropertyTypes(),
    ]);

    const [locations, propertyTypes] = await Promise.race([dataPromise, timeoutPromise]) as any;

    // Build dynamic navigation
    const dynamicNavigation: NavigationItem[] = [
      {
        label: 'Home',
        href: '/',
      },
      {
        label: 'Locations',
        href: '/locations',
        children: [
          { label: 'All Locations', href: '/locations' },
          ...locations.map((location: any) => ({
            label: location.name,
            href: `/locations/${location.slug}`,
          })),
        ],
      },
      {
        label: 'Property Types',
        href: '/property-types',
        children: [
          { label: 'All Property Types', href: '/property-types' },
          ...propertyTypes.map((type: any) => ({
            label: type.name,
            href: `/property-types/${type.slug}`,
          })),
        ],
      },
      {
        label: 'Services',
        href: '/services',
      },
      {
        label: 'About',
        href: '/about',
      },
      {
        label: 'Contact',
        href: '/contact',
      },
    ];

    const buildTime = Date.now() - startTime;
    console.log(`✅ Navigation built from CMS in ${buildTime}ms`);

    return dynamicNavigation;
  } catch (error) {
    console.warn('Failed to build navigation from CMS, using static fallback:', error);
    return STATIC_NAVIGATION;
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Navigation API called - returning static fallback data...');

    // Temporarily return only static navigation to prevent errors
    return NextResponse.json({
      success: true,
      data: STATIC_NAVIGATION,
      timestamp: new Date().toISOString(),
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600', // Cache for 5 minutes
      },
    });
  } catch (error) {
    console.error('Navigation API error:', error);

    return NextResponse.json({
      success: false,
      data: STATIC_NAVIGATION, // Always return fallback data
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, {
      status: 200, // Return 200 even on error since we have fallback data
      headers: {
        'Cache-Control': 'public, s-maxage=60, stale-while-revalidate=120', // Shorter cache on error
      },
    });
  }
}

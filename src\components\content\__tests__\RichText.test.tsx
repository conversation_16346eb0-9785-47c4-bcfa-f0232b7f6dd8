/**
 * RichText Component Tests
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { RichText, RichTextLead, Markdown, HtmlContent } from '../RichText';

describe('RichText Component', () => {
  describe('Basic Rendering', () => {
    it('renders text content correctly', () => {
      render(<RichText content="Hello World" />);
      expect(screen.getByText('Hello World')).toBeInTheDocument();
    });

    it('renders React node content correctly', () => {
      const content = <span>React Node Content</span>;
      render(<RichText content={content} />);
      expect(screen.getByText('React Node Content')).toBeInTheDocument();
    });

    it('renders empty content gracefully', () => {
      const { container } = render(<RichText content="" />);
      expect(container.firstChild).toBeInTheDocument();
    });

    it('renders undefined content gracefully', () => {
      const { container } = render(<RichText />);
      expect(container.firstChild).toBeInTheDocument();
    });
  });

  describe('Typography Variants', () => {
    it('applies body variant styles by default', () => {
      const { container } = render(<RichText content="Body text" />);
      const element = container.querySelector('.rich-text > div');
      expect(element).toHaveClass('text-base', 'leading-relaxed');
    });

    it('applies lead variant styles', () => {
      const { container } = render(<RichText content="Lead text" variant="lead" />);
      const element = container.querySelector('.rich-text > div');
      expect(element).toHaveClass('text-lg', 'leading-relaxed', 'font-medium');
    });

    it('applies small variant styles', () => {
      const { container } = render(<RichText content="Small text" variant="small" />);
      const element = container.querySelector('.rich-text > div');
      expect(element).toHaveClass('text-sm', 'leading-normal');
    });

    it('applies caption variant styles', () => {
      const { container } = render(<RichText content="Caption text" variant="caption" />);
      const element = container.querySelector('.rich-text > div');
      expect(element).toHaveClass('text-xs', 'leading-tight');
    });
  });

  describe('Size Override', () => {
    it('overrides variant with size prop', () => {
      const { container } = render(
        <RichText content="Text" variant="lead" size="sm" />
      );
      const element = container.querySelector('.rich-text > div');
      expect(element).toHaveClass('text-sm');
      expect(element).not.toHaveClass('text-lg');
    });
  });

  describe('Color Variants', () => {
    it('applies default color', () => {
      const { container } = render(<RichText content="Text" />);
      const element = container.querySelector('.rich-text > div');
      expect(element).toHaveClass('text-neutral-900');
    });

    it('applies muted color', () => {
      const { container } = render(<RichText content="Text" color="muted" />);
      const element = container.querySelector('.rich-text > div');
      expect(element).toHaveClass('text-neutral-600');
    });

    it('applies primary color', () => {
      const { container } = render(<RichText content="Text" color="primary" />);
      const element = container.querySelector('.rich-text > div');
      expect(element).toHaveClass('text-primary');
    });
  });

  describe('Markdown Parsing', () => {
    it('parses markdown headers', () => {
      const content = '# Header 1\n## Header 2\n### Header 3';
      render(<RichText content={content} markdown={true} />);
      
      expect(screen.getByText('Header 1')).toBeInTheDocument();
      expect(screen.getByText('Header 2')).toBeInTheDocument();
      expect(screen.getByText('Header 3')).toBeInTheDocument();
    });

    it('parses markdown bold text', () => {
      const content = '**Bold text** and __also bold__';
      render(<RichText content={content} markdown={true} />);
      
      const boldElements = screen.getAllByText(/Bold text|also bold/);
      expect(boldElements).toHaveLength(2);
    });

    it('parses markdown italic text', () => {
      const content = '*Italic text* and _also italic_';
      render(<RichText content={content} markdown={true} />);
      
      const italicElements = screen.getAllByText(/Italic text|also italic/);
      expect(italicElements).toHaveLength(2);
    });

    it('parses markdown links', () => {
      const content = '[Link text](https://example.com)';
      render(<RichText content={content} markdown={true} />);
      
      const link = screen.getByText('Link text');
      expect(link.closest('a')).toHaveAttribute('href', 'https://example.com');
    });

    it('parses markdown code', () => {
      const content = 'Some `inline code` here';
      render(<RichText content={content} markdown={true} />);
      
      expect(screen.getByText('inline code')).toBeInTheDocument();
    });
  });

  describe('HTML Sanitization', () => {
    it('removes script tags by default', () => {
      const content = '<p>Safe content</p><script>alert("xss")</script>';
      render(<RichText content={content} />);
      
      expect(screen.getByText('Safe content')).toBeInTheDocument();
      expect(screen.queryByText('alert("xss")')).not.toBeInTheDocument();
    });

    it('removes event handlers by default', () => {
      const content = '<button onclick="alert(\'xss\')">Click me</button>';
      const { container } = render(<RichText content={content} />);
      
      const button = container.querySelector('button');
      expect(button).not.toHaveAttribute('onclick');
    });

    it('can disable sanitization', () => {
      const content = '<button onclick="alert(\'test\')">Click me</button>';
      const { container } = render(<RichText content={content} sanitize={false} />);
      
      const button = container.querySelector('button');
      expect(button).toHaveAttribute('onclick');
    });
  });

  describe('Expandable Functionality', () => {
    it('shows read more button when expandable and maxLines are set', () => {
      render(
        <RichText 
          content="Long content that should be truncated" 
          maxLines={2} 
          expandable={true} 
        />
      );
      
      expect(screen.getByText('Read more')).toBeInTheDocument();
    });

    it('toggles between read more and show less', () => {
      render(
        <RichText 
          content="Long content that should be truncated" 
          maxLines={2} 
          expandable={true} 
        />
      );
      
      const button = screen.getByText('Read more');
      fireEvent.click(button);
      
      expect(screen.getByText('Show less')).toBeInTheDocument();
      
      fireEvent.click(screen.getByText('Show less'));
      expect(screen.getByText('Read more')).toBeInTheDocument();
    });

    it('does not show toggle button when not expandable', () => {
      render(
        <RichText 
          content="Long content" 
          maxLines={2} 
          expandable={false} 
        />
      );
      
      expect(screen.queryByText('Read more')).not.toBeInTheDocument();
    });
  });

  describe('Custom Element Styles', () => {
    it('applies custom element styles', () => {
      const customStyles = {
        h1: 'custom-h1-class',
        p: 'custom-p-class',
      };
      
      const content = '<h1>Header</h1><p>Paragraph</p>';
      const { container } = render(
        <RichText content={content} elementStyles={customStyles} />
      );
      
      const h1 = container.querySelector('h1');
      const p = container.querySelector('p');
      
      expect(h1).toHaveClass('custom-h1-class');
      expect(p).toHaveClass('custom-p-class');
    });
  });

  describe('Custom ClassName', () => {
    it('applies custom className', () => {
      const { container } = render(
        <RichText content="Text" className="custom-class" />
      );
      
      const element = container.querySelector('.rich-text > div');
      expect(element).toHaveClass('custom-class');
    });
  });
});

describe('RichText Variants', () => {
  it('RichTextLead applies lead variant', () => {
    const { container } = render(<RichTextLead content="Lead text" />);
    const element = container.querySelector('.rich-text > div');
    expect(element).toHaveClass('text-lg', 'leading-relaxed', 'font-medium');
  });

  it('Markdown component enables markdown parsing', () => {
    render(<Markdown content="**Bold text**" />);
    expect(screen.getByText('Bold text')).toBeInTheDocument();
  });

  it('HtmlContent component disables markdown and enables sanitization', () => {
    const content = '<p>HTML content</p><script>alert("test")</script>';
    render(<HtmlContent content={content} />);
    
    expect(screen.getByText('HTML content')).toBeInTheDocument();
    expect(screen.queryByText('alert("test")')).not.toBeInTheDocument();
  });
});

# Story 003: Payload CMS Setup & Admin Panel Functionality

**Story ID:** STORY-003  
**Epic:** Backend Infrastructure  
**Priority:** CRITICAL  
**Estimate:** 13 Story Points  
**Sprint:** 1  
**Status:** Draft

---

## User Story

**As a** content manager and developer  
**I want** a fully functional Payload CMS with admin panel access and visible collections  
**So that** I can manage location data, property types, and content through a user-friendly interface

---

## Acceptance Criteria

### AC1: Payload CMS Core Setup
- [ ] **GIVEN** the project needs a content management system
- [ ] **WHEN** Payload CMS is configured and deployed
- [ ] **THEN** the admin panel is accessible at `/admin`
- [ ] **AND** authentication works with admin credentials
- [ ] **AND** the CMS connects to the database successfully

### AC2: Collections Configuration
- [ ] **GIVEN** the PRD specifies required collections
- [ ] **WHEN** collections are configured in Payload
- [ ] **THEN** Locations collection is visible in admin panel with all fields
- [ ] **AND** PropertyTypes collection is visible in admin panel with all fields
- [ ] **AND** Leads collection is visible in admin panel with all fields
- [ ] **AND** Media collection is configured for file uploads

### AC3: Data Seeding & Visibility
- [ ] **GIVEN** collections are configured
- [ ] **WHEN** initial data is seeded into the database
- [ ] **THEN** seeded locations (Canggu, Ubud, Seminyak) are visible in admin panel
- [ ] **AND** seeded property types (Villa, Guesthouse) are visible in admin panel
- [ ] **AND** all collection data can be edited through the admin interface

### AC4: API Integration
- [ ] **GIVEN** Payload CMS is configured
- [ ] **WHEN** API endpoints are tested
- [ ] **THEN** `/api/locations` returns seeded location data
- [ ] **AND** `/api/property-types` returns seeded property type data
- [ ] **AND** API responses include all required fields per PRD specification

---

## Dev Notes

### Previous Story Insights
- STORY-001 and STORY-002 were implemented with static data
- Frontend components expect specific data structure from CMS
- Homepage already has CMS integration code that needs working backend

### Database Configuration
**Source:** [docs/architecture.md#Backend/CMS Layer]
- **Database:** PostgreSQL via Supabase (current setup)
- **Connection:** Environment variable `SUPABASE_DATABASE_URL` configured
- **Schema:** Use separate `payload_cms` schema to avoid conflicts with existing tables

### Collections Specifications
**Source:** [docs/prd.md#Technical Architecture]

**Locations Collection:**
```typescript
interface Location {
  id: string
  name: string           // e.g., "Canggu"
  slug: string          // e.g., "canggu"
  description: string   // Brief description for SEO
  lifestyle: RichText   // Detailed lifestyle information (400+ words)
  priority: 'zeer-hoog' | 'hoog' | 'middel'
  priceRanges: {
    villa: {
      longTermRental: string    // e.g., "$800-3000/month"
      forSaleFreehold: string   // e.g., "$150k-800k"
    }
  }
  amenities: Array<{amenity: string}>
  coordinates?: {lat: number, lng: number}
  seo: {
    metaTitle: string
    metaDescription: string
  }
}
```

**PropertyTypes Collection:**
```typescript
interface PropertyType {
  id: string
  name: string          // e.g., "Villa"
  slug: string         // e.g., "villa"
  description: RichText
  features: Array<{feature: string}>
  seo: {
    metaTitle: string
    metaDescription: string
  }
}
```

### File Locations
**Source:** [docs/architecture.md#Project Structure]
- **Payload Config:** `payload.config.ts` (root level)
- **Collections:** `src/collections/` directory
- **API Routes:** `src/app/api/[[...slug]]/route.ts`
- **Seeding Script:** `src/seed/index.ts`

### Technical Constraints
**Source:** [docs/architecture.md#Backend/CMS Layer]
- **Payload Version:** 3.x
- **Rich Text Editor:** Lexical editor for content creation
- **Authentication:** JWT-based for admin access
- **API Format:** RESTful APIs auto-generated by Payload

---

## Tasks / Subtasks

### Task 1: Database Schema Configuration (AC: 1)
1.1. Configure PostgreSQL adapter in `payload.config.ts`
1.2. Set up separate `payload_cms` schema to avoid table conflicts
1.3. Test database connection and schema creation
1.4. Verify environment variables are loaded correctly

### Task 2: Collections Implementation (AC: 2)
2.1. Create `src/collections/Locations.ts` with full field specification
2.2. Create `src/collections/PropertyTypes.ts` with features array
2.3. Update `src/collections/Leads.ts` for proper admin visibility
2.4. Configure `src/collections/Media.ts` for file uploads
2.5. Register all collections in `payload.config.ts`

### Task 3: Admin Panel Setup (AC: 1, 2)
3.1. Configure admin user creation and authentication
3.2. Set up admin panel access at `/admin` route
3.3. Test login functionality with admin credentials
3.4. Verify all collections are visible in admin interface

### Task 4: Data Seeding Fix (AC: 3)
4.1. Debug current seeding script in `src/seed/index.ts`
4.2. Ensure seeded data appears in admin panel
4.3. Verify data persistence across server restarts
4.4. Test CRUD operations through admin interface

### Task 5: API Integration Testing (AC: 4)
5.1. Test `/api/locations` endpoint returns correct data structure
5.2. Test `/api/property-types` endpoint functionality
5.3. Verify API responses match frontend expectations
5.4. Update frontend API calls if needed for compatibility

### Task 6: Unit Testing
6.1. Write tests for collection schemas
6.2. Write tests for API endpoints
6.3. Write tests for seeding functionality
6.4. Verify all tests pass in CI/CD pipeline

---

## Testing

### Manual Testing Checklist
- [ ] Admin panel accessible at `http://localhost:3002/admin`
- [ ] Login works with admin credentials
- [ ] All collections visible in admin sidebar
- [ ] Seeded data appears in each collection
- [ ] Data can be edited and saved through admin interface
- [ ] API endpoints return expected data structure

### Automated Testing
- [ ] Collection schema validation tests
- [ ] API endpoint response tests
- [ ] Database connection tests
- [ ] Seeding script tests

---

## Definition of Done
- [ ] All acceptance criteria met
- [ ] Admin panel fully functional with visible data
- [ ] API endpoints working and tested
- [ ] Code reviewed and approved
- [ ] Tests written and passing
- [ ] Documentation updated

---

## Change Log
- **2025-01-20:** Story created by Scrum Master (Bob)
- **Status:** Draft - Ready for Dev Agent implementation

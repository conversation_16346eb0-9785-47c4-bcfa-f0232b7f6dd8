/**
 * Pricing Chart Component
 * Bali Property Scout Website
 * 
 * Interactive pricing visualization with bar charts for property types
 * and price trends using Chart.js with dynamic import for performance.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

// Dynamic import types
type ChartData = {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor: string | string[];
    borderColor: string | string[];
    borderWidth: number;
    borderRadius?: number;
  }[];
};

type ChartOptions = {
  responsive: boolean;
  maintainAspectRatio: boolean;
  plugins: {
    legend: {
      display: boolean;
      position?: 'top' | 'bottom' | 'left' | 'right';
    };
    tooltip: {
      callbacks: {
        label: (context: any) => string;
      };
    };
  };
  scales: {
    x: {
      grid: {
        display: boolean;
      };
    };
    y: {
      grid: {
        display: boolean;
      };
      ticks: {
        callback: (value: any) => string;
      };
    };
  };
};

export interface PricingData {
  propertyType: string;
  rentalRange: {
    min: number;
    max: number;
    currency: string;
  };
  saleRange: {
    min: number;
    max: number;
    currency: string;
  };
  growthTrend: number; // Percentage growth
  popularityScore: number; // 1-10 scale
}

export interface PricingChartProps {
  /** Location name for context */
  locationName: string;
  /** Pricing data for different property types */
  pricingData: PricingData[];
  /** Chart type to display */
  chartType?: 'rental' | 'sale' | 'growth';
  /** Additional CSS classes */
  className?: string;
}

export const PricingChart: React.FC<PricingChartProps> = ({
  locationName,
  pricingData,
  chartType = 'rental',
  className,
}) => {
  const [Chart, setChart] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeChart, setActiveChart] = useState<'rental' | 'sale' | 'growth'>(chartType);

  // Dynamic import Chart.js components
  useEffect(() => {
    const loadChart = async () => {
      try {
        const { Bar } = await import('react-chartjs-2');
        const ChartModule = await import('chart.js');
        const {
          Chart: ChartJS,
          CategoryScale,
          LinearScale,
          BarElement,
          Title,
          Tooltip,
          Legend,
        } = ChartModule;

        // Register Chart.js components
        ChartJS.register(
          CategoryScale,
          LinearScale,
          BarElement,
          Title,
          Tooltip,
          Legend
        );

        setChart(() => Bar);
        setIsLoading(false);
      } catch (error) {
        console.error('Failed to load chart components:', error);
        setIsLoading(false);
      }
    };

    loadChart();
  }, []);

  // Generate chart data based on active chart type
  const generateChartData = (): ChartData => {
    const labels = pricingData.map(item => item.propertyType);
    
    switch (activeChart) {
      case 'rental':
        return {
          labels,
          datasets: [
            {
              label: 'Min Rental (USD/month)',
              data: pricingData.map(item => item.rentalRange.min),
              backgroundColor: 'rgba(16, 185, 129, 0.8)',
              borderColor: 'rgba(16, 185, 129, 1)',
              borderWidth: 2,
              borderRadius: 8,
            },
            {
              label: 'Max Rental (USD/month)',
              data: pricingData.map(item => item.rentalRange.max),
              backgroundColor: 'rgba(6, 182, 212, 0.8)',
              borderColor: 'rgba(6, 182, 212, 1)',
              borderWidth: 2,
              borderRadius: 8,
            },
          ],
        };
      
      case 'sale':
        return {
          labels,
          datasets: [
            {
              label: 'Min Sale Price (USD)',
              data: pricingData.map(item => item.saleRange.min),
              backgroundColor: 'rgba(99, 102, 241, 0.8)',
              borderColor: 'rgba(99, 102, 241, 1)',
              borderWidth: 2,
              borderRadius: 8,
            },
            {
              label: 'Max Sale Price (USD)',
              data: pricingData.map(item => item.saleRange.max),
              backgroundColor: 'rgba(168, 85, 247, 0.8)',
              borderColor: 'rgba(168, 85, 247, 1)',
              borderWidth: 2,
              borderRadius: 8,
            },
          ],
        };
      
      case 'growth':
        return {
          labels,
          datasets: [
            {
              label: 'Price Growth (%)',
              data: pricingData.map(item => item.growthTrend),
              backgroundColor: pricingData.map(item => 
                item.growthTrend > 0 
                  ? 'rgba(34, 197, 94, 0.8)' 
                  : 'rgba(239, 68, 68, 0.8)'
              ),
              borderColor: pricingData.map(item => 
                item.growthTrend > 0 
                  ? 'rgba(34, 197, 94, 1)' 
                  : 'rgba(239, 68, 68, 1)'
              ),
              borderWidth: 2,
              borderRadius: 8,
            },
          ],
        };
      
      default:
        return { labels: [], datasets: [] };
    }
  };

  // Chart options
  const chartOptions: ChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: (context: any) => {
            const value = context.parsed.y;
            if (activeChart === 'growth') {
              return `${context.dataset.label}: ${value}%`;
            }
            return `${context.dataset.label}: $${value.toLocaleString()}`;
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
      },
      y: {
        grid: {
          display: true,
        },
        ticks: {
          callback: (value: any) => {
            if (activeChart === 'growth') {
              return `${value}%`;
            }
            return `$${value.toLocaleString()}`;
          },
        },
      },
    },
  };

  if (isLoading) {
    return (
      <div className={cn('bg-white rounded-2xl p-8 shadow-lg', className)}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4 w-1/2"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!Chart) {
    return (
      <div className={cn('bg-white rounded-2xl p-8 shadow-lg', className)}>
        <div className="text-center text-gray-500">
          <p>Chart visualization unavailable</p>
          <p className="text-sm">Please check your connection and try again</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300', className)}>
      {/* Chart Header */}
      <div className="mb-6">
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          {locationName} Property Pricing
        </h3>
        <p className="text-gray-600">
          Interactive pricing data and market trends for different property types
        </p>
      </div>

      {/* Chart Type Selector */}
      <div className="flex flex-wrap gap-2 mb-6">
        <button
          onClick={() => setActiveChart('rental')}
          className={cn(
            'px-4 py-2 rounded-lg font-medium text-sm transition-all duration-300',
            activeChart === 'rental'
              ? 'bg-emerald-600 text-white shadow-lg'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          )}
        >
          📊 Rental Prices
        </button>
        <button
          onClick={() => setActiveChart('sale')}
          className={cn(
            'px-4 py-2 rounded-lg font-medium text-sm transition-all duration-300',
            activeChart === 'sale'
              ? 'bg-indigo-600 text-white shadow-lg'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          )}
        >
          🏠 Sale Prices
        </button>
        <button
          onClick={() => setActiveChart('growth')}
          className={cn(
            'px-4 py-2 rounded-lg font-medium text-sm transition-all duration-300',
            activeChart === 'growth'
              ? 'bg-green-600 text-white shadow-lg'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          )}
        >
          📈 Growth Trends
        </button>
      </div>

      {/* Chart Container */}
      <div className="h-80 mb-6">
        <Chart data={generateChartData()} options={chartOptions} />
      </div>

      {/* Chart Legend/Info */}
      <div className="text-sm text-gray-500 text-center">
        <p>
          {activeChart === 'rental' && 'Monthly rental prices in USD'}
          {activeChart === 'sale' && 'Property sale prices in USD'}
          {activeChart === 'growth' && 'Year-over-year price growth percentage'}
        </p>
      </div>
    </div>
  );
};

export default PricingChart;

/**
 * useScrollBehavior Hook
 * Bali Property Scout Website
 * 
 * Custom hook for managing scroll-based UI behaviors including
 * header transparency, size changes, and scroll direction detection.
 */

'use client';

import { useState, useEffect, useCallback, useRef } from 'react';

export interface ScrollBehaviorState {
  /** Current scroll position */
  scrollY: number;
  /** Whether user has scrolled past threshold */
  isScrolled: boolean;
  /** Scroll direction */
  scrollDirection: 'up' | 'down' | null;
  /** Whether user is scrolling */
  isScrolling: boolean;
  /** Percentage of page scrolled (0-100) */
  scrollProgress: number;
}

export interface UseScrollBehaviorOptions {
  /** Threshold for isScrolled state (default: 10) */
  threshold?: number;
  /** Debounce delay for scroll events (default: 10) */
  debounceDelay?: number;
  /** Whether to track scroll direction (default: true) */
  trackDirection?: boolean;
  /** Whether to track scroll progress (default: false) */
  trackProgress?: boolean;
}

export function useScrollBehavior(options: UseScrollBehaviorOptions = {}): ScrollBehaviorState {
  const {
    threshold = 10,
    debounceDelay = 10,
    trackDirection = true,
    trackProgress = false,
  } = options;

  const [scrollState, setScrollState] = useState<ScrollBehaviorState>({
    scrollY: 0,
    isScrolled: false,
    scrollDirection: null,
    isScrolling: false,
    scrollProgress: 0,
  });

  // Use useRef to avoid dependency issues with timeout and options
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const optionsRef = useRef({ threshold, trackDirection, trackProgress });

  // Update options ref when props change
  useEffect(() => {
    optionsRef.current = { threshold, trackDirection, trackProgress };
  }, [threshold, trackDirection, trackProgress]);

  const updateScrollState = useCallback(() => {
    const currentScrollY = window.scrollY;
    const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
    const { threshold: currentThreshold, trackDirection: currentTrackDirection, trackProgress: currentTrackProgress } = optionsRef.current;

    setScrollState(prevState => {
      const newState: ScrollBehaviorState = {
        scrollY: currentScrollY,
        isScrolled: currentScrollY > currentThreshold,
        scrollDirection: currentTrackDirection
          ? currentScrollY > prevState.scrollY
            ? 'down'
            : currentScrollY < prevState.scrollY
              ? 'up'
              : prevState.scrollDirection
          : null,
        isScrolling: true,
        scrollProgress: currentTrackProgress
          ? Math.min(100, Math.max(0, (currentScrollY / documentHeight) * 100))
          : 0,
      };

      return newState;
    });

    // Clear existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Set new timeout to detect when scrolling stops
    scrollTimeoutRef.current = setTimeout(() => {
      setScrollState(prevState => ({
        ...prevState,
        isScrolling: false,
      }));
    }, 150);
  }, []); // Empty dependency array to prevent infinite loop

  useEffect(() => {
    // Throttled scroll handler
    let ticking = false;
    
    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          updateScrollState();
          ticking = false;
        });
        ticking = true;
      }
    };

    // Initial state
    updateScrollState();

    // Add scroll listener
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []); // Empty dependency array - only run once on mount

  return scrollState;
}

/**
 * Hook for header-specific scroll behavior
 */
export function useHeaderScrollBehavior() {
  const [mounted, setMounted] = useState(false);
  const scrollBehavior = useScrollBehavior({
    threshold: 20,
    trackDirection: true,
    trackProgress: false,
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  // Header-specific computed values - use safe defaults for SSR
  const headerOpacity = mounted && scrollBehavior.isScrolled ? 0.95 : 0.85;
  const headerScale = mounted && scrollBehavior.isScrolled ? 0.98 : 1;
  const headerBlur = mounted && scrollBehavior.isScrolled ? 'xl' : 'md';
  const headerShadow = mounted && scrollBehavior.isScrolled ? '2xl' : 'lg';

  return {
    ...scrollBehavior,
    headerOpacity,
    headerScale,
    headerBlur,
    headerShadow,
    mounted,
  };
}

export default useScrollBehavior;

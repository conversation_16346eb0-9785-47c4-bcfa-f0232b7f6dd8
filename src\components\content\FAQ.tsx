/**
 * FAQ Component
 * 
 * Expandable FAQ component with search, categorization, and smooth animations.
 * Supports multiple layouts and interaction modes.
 */

'use client';

import React, { useState, useMemo } from 'react';
import { cn } from '@/lib/utils';
import { RichText } from './RichText';

export interface FAQItem {
  /** Unique identifier */
  id: string;
  /** Question text */
  question: string;
  /** Answer content (can be HTML or markdown) */
  answer: string;
  /** Category for grouping */
  category?: string;
  /** Tags for filtering */
  tags?: string[];
  /** Whether this item is initially expanded */
  defaultExpanded?: boolean;
}

export interface FAQProps {
  /** Array of FAQ items */
  items: FAQItem[];
  /** Whether to enable search */
  searchable?: boolean;
  /** Whether to show categories */
  showCategories?: boolean;
  /** Whether to allow multiple items expanded at once */
  allowMultiple?: boolean;
  /** Layout variant */
  variant?: 'default' | 'bordered' | 'minimal';
  /** Custom className */
  className?: string;
  /** Search placeholder text */
  searchPlaceholder?: string;
  /** No results message */
  noResultsMessage?: string;
  /** Whether answers support markdown */
  markdown?: boolean;
  /** Callback when item is expanded/collapsed */
  onToggle?: (itemId: string, isExpanded: boolean) => void;
}

// Variant styles
const variantStyles = {
  default: {
    container: 'space-y-4',
    item: 'bg-white border border-neutral-200 rounded-lg overflow-hidden',
    question: 'p-6 cursor-pointer hover:bg-neutral-50 transition-colors',
    answer: 'px-6 pb-6 border-t border-neutral-100',
  },
  bordered: {
    container: 'divide-y divide-neutral-200 border border-neutral-200 rounded-lg overflow-hidden',
    item: 'bg-white',
    question: 'p-6 cursor-pointer hover:bg-neutral-50 transition-colors',
    answer: 'px-6 pb-6 bg-neutral-50',
  },
  minimal: {
    container: 'space-y-2',
    item: 'bg-transparent',
    question: 'py-4 cursor-pointer hover:text-primary transition-colors border-b border-neutral-200',
    answer: 'pb-4 text-neutral-600',
  },
};

export const FAQ: React.FC<FAQProps> = React.memo(({
  items,
  searchable = true,
  showCategories = true,
  allowMultiple = false,
  variant = 'default',
  className,
  searchPlaceholder = 'Search FAQs...',
  noResultsMessage = 'No FAQs found matching your search.',
  markdown = false,
  onToggle,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(
    new Set(items.filter(item => item.defaultExpanded).map(item => item.id))
  );

  const styles = variantStyles[variant];

  // Get unique categories
  const categories = useMemo(() => {
    const cats = items
      .map(item => item.category)
      .filter((cat): cat is string => Boolean(cat));
    return Array.from(new Set(cats)).sort();
  }, [items]);

  // Filter items based on search and category
  const filteredItems = useMemo(() => {
    let filtered = items;

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item =>
        item.question.toLowerCase().includes(query) ||
        item.answer.toLowerCase().includes(query) ||
        item.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter(item => item.category === selectedCategory);
    }

    return filtered;
  }, [items, searchQuery, selectedCategory]);

  // Group items by category
  const groupedItems = useMemo(() => {
    if (!showCategories) {
      return { '': filteredItems };
    }

    const grouped: Record<string, FAQItem[]> = {};
    
    filteredItems.forEach(item => {
      const category = item.category || 'General';
      if (!grouped[category]) {
        grouped[category] = [];
      }
      grouped[category].push(item);
    });

    return grouped;
  }, [filteredItems, showCategories]);

  // Handle item toggle
  const handleToggle = (itemId: string) => {
    setExpandedItems(prev => {
      const newExpanded = new Set(prev);
      const isCurrentlyExpanded = newExpanded.has(itemId);

      if (isCurrentlyExpanded) {
        newExpanded.delete(itemId);
      } else {
        if (!allowMultiple) {
          newExpanded.clear();
        }
        newExpanded.add(itemId);
      }

      onToggle?.(itemId, !isCurrentlyExpanded);
      return newExpanded;
    });
  };

  // Render search bar
  const renderSearch = () => {
    if (!searchable) return null;

    return (
      <div className="relative mb-6">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg className="h-5 w-5 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        <input
          type="text"
          placeholder={searchPlaceholder}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="block w-full pl-10 pr-3 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
        />
      </div>
    );
  };

  // Render category filter
  const renderCategoryFilter = () => {
    if (!showCategories || categories.length <= 1) return null;

    return (
      <div className="mb-6">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setSelectedCategory(null)}
            className={cn(
              'px-4 py-2 rounded-full text-sm font-medium transition-colors',
              selectedCategory === null
                ? 'bg-primary text-primary-foreground'
                : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
            )}
          >
            All
          </button>
          {categories.map(category => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={cn(
                'px-4 py-2 rounded-full text-sm font-medium transition-colors',
                selectedCategory === category
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
              )}
            >
              {category}
            </button>
          ))}
        </div>
      </div>
    );
  };

  // Render FAQ item
  const renderFAQItem = (item: FAQItem) => {
    const isExpanded = expandedItems.has(item.id);

    return (
      <div key={item.id} className={styles.item}>
        {/* Question */}
        <button
          className={cn(styles.question, 'flex items-center justify-between w-full text-left')}
          onClick={() => handleToggle(item.id)}
          aria-expanded={isExpanded}
          aria-controls={`faq-answer-${item.id}`}
          id={`faq-question-${item.id}`}
        >
          <h3 className="text-lg font-semibold text-neutral-900 pr-4">
            {item.question}
          </h3>
          <div className={cn(
            'flex-shrink-0 transition-transform duration-200',
            isExpanded && 'rotate-180'
          )}>
            <svg className="w-5 h-5 text-neutral-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </button>

        {/* Answer */}
        <div
          className={cn(
            'overflow-hidden transition-all duration-300',
            isExpanded ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'
          )}
          id={`faq-answer-${item.id}`}
          role="region"
          aria-labelledby={`faq-question-${item.id}`}
        >
          <div className={styles.answer}>
            <RichText
              content={item.answer}
              markdown={markdown}
              variant="body"
              className="text-neutral-700"
            />
          </div>
        </div>
      </div>
    );
  };

  // Render category section
  const renderCategorySection = (category: string, categoryItems: FAQItem[]) => {
    if (!showCategories || category === '') {
      return categoryItems.map(renderFAQItem);
    }

    return (
      <div key={category} className="mb-8">
        <h2 className="text-xl font-bold text-neutral-900 mb-4">
          {category}
        </h2>
        <div className={styles.container}>
          {categoryItems.map(renderFAQItem)}
        </div>
      </div>
    );
  };

  return (
    <div className={cn('faq-component', className)}>
      {/* Search */}
      {renderSearch()}

      {/* Category filter */}
      {renderCategoryFilter()}

      {/* FAQ items */}
      {filteredItems.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-neutral-400 mb-2">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 20c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8a7.962 7.962 0 01-2 5.291z" />
            </svg>
          </div>
          <p className="text-neutral-600">{noResultsMessage}</p>
        </div>
      ) : (
        <div className={showCategories ? 'space-y-8' : styles.container}>
          {Object.entries(groupedItems).map(([category, categoryItems]) =>
            renderCategorySection(category, categoryItems)
          )}
        </div>
      )}
    </div>
  );
});

FAQ.displayName = 'FAQ';

// Specialized FAQ variants
export const FAQBordered: React.FC<Omit<FAQProps, 'variant'>> = (props) => (
  <FAQ {...props} variant="bordered" />
);

export const FAQMinimal: React.FC<Omit<FAQProps, 'variant'>> = (props) => (
  <FAQ {...props} variant="minimal" />
);

'use client'

import React, { createContext, useContext, useReducer, ReactNode } from 'react'

// Types
interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  metadata?: Record<string, any>
}

interface UserPreferences {
  budget?: { min: number; max: number }
  duration?: string
  propertyType?: string[]
  locations?: string[]
  lifestyle?: string[]
}

interface PropertyMatch {
  id: string
  propertyId?: string
  matchScore: number
  reasoning: string
  userFeedback?: 'interested' | 'not_interested' | 'maybe'
}

interface LeadScore {
  budgetAlignment: number
  timelineUrgency: number
  locationSpecificity: number
  propertyTypeClarity: number
  engagementLevel: number
  totalScore: number
  qualificationStatus: 'unqualified' | 'qualified' | 'hot_lead'
}

interface AIContextState {
  currentConversation: {
    sessionId: string | null
    messages: ChatMessage[]
    isActive: boolean
  }
  isTyping: boolean
  propertyMatches: PropertyMatch[]
  userPreferences: UserPreferences
  leadScore: LeadScore | null
  context: {
    location?: string
    propertyType?: string
    page: string
  }
}

type AIAction = 
  | { type: 'START_CONVERSATION'; sessionId: string }
  | { type: 'ADD_MESSAGE'; message: ChatMessage }
  | { type: 'UPDATE_PREFERENCES'; preferences: Partial<UserPreferences> }
  | { type: 'SET_PROPERTY_MATCHES'; matches: PropertyMatch[] }
  | { type: 'UPDATE_LEAD_SCORE'; score: LeadScore }
  | { type: 'SET_TYPING'; isTyping: boolean }
  | { type: 'SET_CONTEXT'; context: Partial<AIContextState['context']> }
  | { type: 'END_CONVERSATION' }

// Initial state
const initialState: AIContextState = {
  currentConversation: {
    sessionId: null,
    messages: [],
    isActive: false
  },
  isTyping: false,
  propertyMatches: [],
  userPreferences: {},
  leadScore: null,
  context: {
    page: 'unknown'
  }
}

// Reducer
const aiReducer = (state: AIContextState, action: AIAction): AIContextState => {
  switch (action.type) {
    case 'START_CONVERSATION':
      return {
        ...state,
        currentConversation: {
          sessionId: action.sessionId,
          messages: [],
          isActive: true
        }
      }
    
    case 'ADD_MESSAGE':
      return {
        ...state,
        currentConversation: {
          ...state.currentConversation,
          messages: [...state.currentConversation.messages, action.message]
        }
      }
    
    case 'UPDATE_PREFERENCES':
      return {
        ...state,
        userPreferences: { ...state.userPreferences, ...action.preferences }
      }
    
    case 'SET_PROPERTY_MATCHES':
      return {
        ...state,
        propertyMatches: action.matches
      }
    
    case 'UPDATE_LEAD_SCORE':
      return {
        ...state,
        leadScore: action.score
      }
    
    case 'SET_TYPING':
      return {
        ...state,
        isTyping: action.isTyping
      }
    
    case 'SET_CONTEXT':
      return {
        ...state,
        context: { ...state.context, ...action.context }
      }
    
    case 'END_CONVERSATION':
      return {
        ...state,
        currentConversation: {
          sessionId: null,
          messages: [],
          isActive: false
        },
        isTyping: false,
        propertyMatches: [],
        leadScore: null
      }
    
    default:
      return state
  }
}

// Context
const AIContext = createContext<{
  state: AIContextState
  dispatch: React.Dispatch<AIAction>
} | null>(null)

// Provider component
interface AIContextProviderProps {
  children: ReactNode
  initialContext?: Partial<AIContextState['context']>
}

export const AIContextProvider: React.FC<AIContextProviderProps> = ({ 
  children, 
  initialContext = {} 
}) => {
  const [state, dispatch] = useReducer(aiReducer, {
    ...initialState,
    context: { ...initialState.context, ...initialContext }
  })

  return (
    <AIContext.Provider value={{ state, dispatch }}>
      {children}
    </AIContext.Provider>
  )
}

// Hook to use AI context
export const useAIContext = () => {
  const context = useContext(AIContext)
  if (!context) {
    throw new Error('useAIContext must be used within an AIContextProvider')
  }
  return context
}

// Export types for use in other components
export type { 
  ChatMessage, 
  UserPreferences, 
  PropertyMatch, 
  LeadScore, 
  AIContextState,
  AIAction 
}

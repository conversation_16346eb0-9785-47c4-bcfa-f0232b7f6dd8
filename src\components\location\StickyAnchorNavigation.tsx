/**
 * Sticky Anchor Navigation Component
 * Bali Property Scout Website
 * 
 * Horizontal anchor navigation that becomes sticky below floating header
 * with active section highlighting and smooth scrolling functionality.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

export interface NavigationItem {
  id: string;
  label: string;
  icon?: string;
}

export interface StickyAnchorNavigationProps {
  /** Navigation items with section IDs and labels */
  items: NavigationItem[];
  /** Additional CSS classes */
  className?: string;
  /** Offset from top when sticky (to account for floating header) */
  stickyOffset?: number;
}

export const StickyAnchorNavigation: React.FC<StickyAnchorNavigationProps> = ({
  items,
  className,
  stickyOffset = 100,
}) => {
  const [activeSection, setActiveSection] = useState<string>('');
  const [isSticky, setIsSticky] = useState(false);

  // Handle scroll events for active section detection and sticky behavior
  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      const navElement = document.getElementById('sticky-anchor-nav');
      
      if (navElement) {
        const navTop = navElement.offsetTop;
        setIsSticky(scrollY > navTop - stickyOffset);
      }

      // Find active section based on scroll position
      const sections = items.map(item => document.getElementById(item.id)).filter(Boolean);
      
      let currentActiveSection = '';
      
      for (const section of sections) {
        if (section) {
          const rect = section.getBoundingClientRect();
          const sectionTop = rect.top + scrollY;
          const sectionHeight = rect.height;
          
          // Consider section active if it's in the viewport
          if (scrollY >= sectionTop - stickyOffset - 50 && 
              scrollY < sectionTop + sectionHeight - stickyOffset - 50) {
            currentActiveSection = section.id;
          }
        }
      }
      
      // If no section is clearly active, use the first one that's visible
      if (!currentActiveSection && sections.length > 0) {
        for (const section of sections) {
          if (section) {
            const rect = section.getBoundingClientRect();
            if (rect.top <= stickyOffset + 100) {
              currentActiveSection = section.id;
            }
          }
        }
      }
      
      if (currentActiveSection && currentActiveSection !== activeSection) {
        setActiveSection(currentActiveSection);
      }
    };

    // Initial check
    handleScroll();
    
    // Add scroll listener with throttling
    let ticking = false;
    const throttledScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledScroll, { passive: true });
    window.addEventListener('resize', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', throttledScroll);
      window.removeEventListener('resize', handleScroll);
    };
  }, [items, activeSection, stickyOffset]);

  // Smooth scroll to section
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const elementTop = element.offsetTop;
      const offsetPosition = elementTop - stickyOffset - 20;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  };

  return (
    <>
      {/* Sticky Navigation */}
      <nav
        id="sticky-anchor-nav"
        className={cn(
          'bg-white/95 backdrop-blur-xl border-b border-gray-200/50 transition-all duration-300 z-40',
          isSticky && 'fixed top-20 left-0 right-0 shadow-lg',
          className
        )}
        style={{
          top: isSticky ? `${stickyOffset}px` : 'auto',
        }}
      >
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex items-center justify-between py-4">
            {/* Navigation Items */}
            <div className="flex space-x-1 overflow-x-auto scrollbar-hide">
              {items.map((item) => (
                <button
                  key={item.id}
                  onClick={() => scrollToSection(item.id)}
                  className={cn(
                    'flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium text-sm transition-all duration-300 whitespace-nowrap',
                    'hover:bg-emerald-50 hover:text-emerald-700 hover:scale-105',
                    activeSection === item.id
                      ? 'bg-gradient-to-r from-emerald-600 to-teal-600 text-white shadow-lg scale-105'
                      : 'text-gray-600 hover:text-emerald-600'
                  )}
                >
                  {item.icon && <span className="text-lg">{item.icon}</span>}
                  <span>{item.label}</span>
                </button>
              ))}
            </div>

            {/* Progress Indicator */}
            <div className="hidden md:flex items-center gap-2 text-sm text-gray-500">
              <span>
                {items.findIndex(item => item.id === activeSection) + 1} of {items.length}
              </span>
              <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-emerald-500 to-teal-500 transition-all duration-300"
                  style={{
                    width: `${((items.findIndex(item => item.id === activeSection) + 1) / items.length) * 100}%`
                  }}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Active Section Indicator Line */}
        <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-emerald-500 to-teal-500 transform origin-left transition-all duration-300" />
      </nav>

      {/* Spacer when sticky to prevent content jump */}
      {isSticky && (
        <div className="h-20" aria-hidden="true" />
      )}
    </>
  );
};

export default StickyAnchorNavigation;

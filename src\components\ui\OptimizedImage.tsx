/**
 * Optimized Image Component
 * 
 * Performance-optimized image component with lazy loading, responsive sizing,
 * and progressive enhancement.
 */

'use client';

import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { useIntersectionPerformance } from '@/hooks/usePerformance';

export interface OptimizedImageProps {
  /** Image source URL */
  src: string;
  /** Image alt text */
  alt: string;
  /** Image width */
  width?: number;
  /** Image height */
  height?: number;
  /** CSS classes */
  className?: string;
  /** Whether to use lazy loading */
  lazy?: boolean;
  /** Whether to show loading placeholder */
  showPlaceholder?: boolean;
  /** Placeholder color */
  placeholderColor?: string;
  /** Image quality (1-100) */
  quality?: number;
  /** Image priority (for above-the-fold images) */
  priority?: boolean;
  /** Responsive sizes */
  sizes?: string;
  /** Image fill mode */
  fill?: boolean;
  /** Object fit */
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  /** Object position */
  objectPosition?: string;
  /** Blur data URL for placeholder */
  blurDataURL?: string;
  /** Whether to use blur placeholder */
  placeholder?: 'blur' | 'empty';
  /** Callback when image loads */
  onLoad?: () => void;
  /** Callback when image fails to load */
  onError?: () => void;
  /** Whether to enable progressive loading */
  progressive?: boolean;
  /** Intersection observer root margin */
  rootMargin?: string;
}

/**
 * Generate a simple blur data URL for placeholder
 */
function generateBlurDataURL(width: number = 10, height: number = 10): string {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) return '';
  
  // Create a simple gradient
  const gradient = ctx.createLinearGradient(0, 0, width, height);
  gradient.addColorStop(0, '#f3f4f6');
  gradient.addColorStop(1, '#e5e7eb');
  
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, width, height);
  
  return canvas.toDataURL();
}

/**
 * Generate responsive sizes string based on breakpoints
 */
function generateResponsiveSizes(width?: number): string {
  if (!width) {
    return '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw';
  }
  
  return `(max-width: 768px) 100vw, (max-width: 1200px) ${Math.min(width, 768)}px, ${width}px`;
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className,
  lazy = true,
  showPlaceholder = true,
  placeholderColor = '#f3f4f6',
  quality = 85,
  priority = false,
  sizes,
  fill = false,
  objectFit = 'cover',
  objectPosition = 'center',
  blurDataURL,
  placeholder = 'blur',
  onLoad,
  onError,
  progressive = true,
  rootMargin = '50px',
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(!lazy || priority);
  const [hasError, setHasError] = useState(false);
  const imageRef = useRef<HTMLDivElement>(null);

  // Generate blur data URL if not provided
  const defaultBlurDataURL = blurDataURL || (typeof window !== 'undefined' ? generateBlurDataURL() : '');

  // Intersection observer for lazy loading
  const observer = useIntersectionPerformance(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          if (observer) {
            observer.unobserve(entry.target);
          }
        }
      });
    },
    {
      rootMargin,
      threshold: 0.1,
    }
  );

  // Set up intersection observer
  useEffect(() => {
    if (lazy && !priority && imageRef.current && observer) {
      observer.observe(imageRef.current);
    }

    return () => {
      if (observer && imageRef.current) {
        observer.unobserve(imageRef.current);
      }
    };
  }, [lazy, priority, observer]);

  // Handle image load
  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  // Handle image error
  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  // Generate responsive sizes if not provided
  const responsiveSizes = sizes || generateResponsiveSizes(width);

  // Placeholder component
  const Placeholder = () => (
    <div
      className={cn(
        'animate-pulse flex items-center justify-center',
        fill ? 'absolute inset-0' : '',
        className
      )}
      style={{
        backgroundColor: placeholderColor,
        width: fill ? '100%' : width,
        height: fill ? '100%' : height,
      }}
    >
      {showPlaceholder && (
        <svg
          className="w-8 h-8 text-gray-400"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fillRule="evenodd"
            d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
            clipRule="evenodd"
          />
        </svg>
      )}
    </div>
  );

  // Error component
  const ErrorFallback = () => (
    <div
      className={cn(
        'bg-gray-100 flex items-center justify-center text-gray-400',
        fill ? 'absolute inset-0' : '',
        className
      )}
      style={{
        width: fill ? '100%' : width,
        height: fill ? '100%' : height,
      }}
    >
      <svg
        className="w-8 h-8"
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path
          fillRule="evenodd"
          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
          clipRule="evenodd"
        />
      </svg>
    </div>
  );

  return (
    <div
      ref={imageRef}
      className={cn(
        'relative overflow-hidden',
        fill ? 'w-full h-full' : '',
        className
      )}
      style={!fill ? { width, height } : undefined}
    >
      {hasError ? (
        <ErrorFallback />
      ) : !isInView ? (
        <Placeholder />
      ) : (
        <>
          {/* Show placeholder while loading */}
          {!isLoaded && <Placeholder />}
          
          {/* Actual image */}
          <Image
            src={src}
            alt={alt}
            width={fill ? undefined : width}
            height={fill ? undefined : height}
            fill={fill}
            sizes={responsiveSizes}
            quality={quality}
            priority={priority}
            placeholder={placeholder}
            blurDataURL={defaultBlurDataURL}
            className={cn(
              'transition-opacity duration-300',
              isLoaded ? 'opacity-100' : 'opacity-0',
              fill ? 'object-cover' : '',
              className
            )}
            style={{
              objectFit: fill ? objectFit : undefined,
              objectPosition: fill ? objectPosition : undefined,
            }}
            onLoad={handleLoad}
            onError={handleError}
            loading={priority ? 'eager' : 'lazy'}
          />
        </>
      )}
    </div>
  );
};

// Specialized variants
export const OptimizedHeroImage: React.FC<Omit<OptimizedImageProps, 'priority' | 'lazy'>> = (props) => (
  <OptimizedImage {...props} priority={true} lazy={false} />
);

export const OptimizedThumbnail: React.FC<Omit<OptimizedImageProps, 'quality' | 'progressive'>> = (props) => (
  <OptimizedImage {...props} quality={60} progressive={true} />
);

export const OptimizedGalleryImage: React.FC<Omit<OptimizedImageProps, 'lazy' | 'rootMargin'>> = (props) => (
  <OptimizedImage {...props} lazy={true} rootMargin="100px" />
);

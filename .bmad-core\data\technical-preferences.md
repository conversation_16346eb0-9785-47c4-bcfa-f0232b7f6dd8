<!-- Powered by BMAD™ Core -->

# User-Defined Preferred Patterns and Preferences

## Tech Stack
- **Frontend:** Next.js 15 with App Router, TypeScript, Tailwind CSS
- **Backend/CMS:** Payload CMS 3.x with MongoDB
- **Hosting:** Vercel for deployment and CDN
- **Database:** MongoDB for content and lead management
- **Email:** SendGrid/Mailgun for lead generation emails

## Development Patterns
- **Component Architecture:** Reusable React components with TypeScript
- **Styling:** Tailwind CSS utility-first approach
- **State Management:** React hooks and context for local state
- **API Design:** RESTful APIs through Payload CMS
- **SEO:** Next.js built-in SEO optimization with dynamic meta tags

## Code Quality Standards
- **TypeScript:** Strict mode enabled for type safety
- **ESLint:** Configured for Next.js and React best practices
- **File Structure:** Feature-based organization in src/ directory
- **Naming Conventions:** camelCase for variables, PascalCase for components
- **Import Organization:** Absolute imports using @/ alias

## Content Management
- **Collections:** Locations, PropertyTypes, Content, Leads, Media
- **Rich Text:** Lexical editor for content creation
- **Image Handling:** Automatic optimization with multiple sizes
- **SEO Fields:** Integrated SEO plugin for meta tags and structured data

## Performance Requirements
- **Core Web Vitals:** LCP < 1.5s, FID < 100ms, CLS < 0.1
- **Image Optimization:** Next.js Image component with lazy loading
- **Caching:** ISR for static content, SWR for dynamic data
- **Bundle Size:** Code splitting and dynamic imports

## Lead Generation Focus
- **Forms:** Contact, property interest, consultation request forms
- **Lead Scoring:** Automated scoring based on budget, timeline, specificity
- **Email Automation:** Welcome sequences and follow-up campaigns
- **GDPR Compliance:** Consent management and data protection

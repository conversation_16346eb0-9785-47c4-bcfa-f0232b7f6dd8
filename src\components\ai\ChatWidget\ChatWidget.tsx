'use client'

import React, { useState, useReducer, useEffect } from 'react'
import { ChatBubble } from './ChatBubble'
import { ChatInput } from './ChatInput'
import { TypingIndicator } from './TypingIndicator'
import { useAIContext } from '../AIContext'
import { colors } from '@/components/design-system/colors'

// Types
interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

interface ChatContext {
  location?: string
  propertyType?: string
  page: string
}

interface ChatWidgetState {
  isOpen: boolean
  isMinimized: boolean
  messages: ChatMessage[]
  isTyping: boolean
  context: ChatContext
}

type ChatAction =
  | { type: 'TOGGLE_WIDGET' }
  | { type: 'MINIMIZE_WIDGET' }
  | { type: 'ADD_MESSAGE'; message: ChatMessage }
  | { type: 'SET_MESSAGES'; messages: ChatMessage[] }
  | { type: 'SET_TYPING'; isTyping: boolean }
  | { type: 'SET_CONTEXT'; context: ChatContext }

// Reducer
const chatReducer = (state: ChatWidgetState, action: ChatAction): ChatWidgetState => {
  switch (action.type) {
    case 'TOGGLE_WIDGET':
      return { ...state, isOpen: !state.isOpen, isMinimized: false }
    case 'MINIMIZE_WIDGET':
      return { ...state, isMinimized: !state.isMinimized }
    case 'ADD_MESSAGE':
      return { ...state, messages: [...state.messages, action.message] }
    case 'SET_MESSAGES':
      return { ...state, messages: action.messages }
    case 'SET_TYPING':
      return { ...state, isTyping: action.isTyping }
    case 'SET_CONTEXT':
      return { ...state, context: action.context }
    default:
      return state
  }
}

// Props interface
interface ChatWidgetProps {
  context?: ChatContext
  initialPreferences?: {
    location?: string
    propertyType?: string
  }
}

export const ChatWidget: React.FC<ChatWidgetProps> = ({
  context = { page: 'unknown' },
  initialPreferences = {}
}) => {
  const { state: aiState, dispatch: aiDispatch } = useAIContext()
  const [state, dispatch] = useReducer(chatReducer, {
    isOpen: false,
    isMinimized: false,
    messages: [],
    isTyping: false,
    context: aiState.context.location || aiState.context.propertyType ? aiState.context : context
  })

  // Update context when AI context changes
  useEffect(() => {
    if (aiState.context.location || aiState.context.propertyType) {
      dispatch({ type: 'SET_CONTEXT', context: aiState.context })
    }
  }, [aiState.context])

  // Generate contextual welcome message
  const getWelcomeMessage = (): string => {
    const currentContext = state.context

    if (currentContext.location) {
      switch (currentContext.location.toLowerCase()) {
        case 'canggu':
          return "Find your perfect Canggu property with AI assistance"
        case 'ubud':
          return "Discover Ubud properties tailored to your lifestyle"
        case 'seminyak':
          return "Explore luxury Seminyak properties with AI guidance"
        case 'sanur':
          return "Find serene Sanur properties with AI guidance"
        case 'jimbaran':
          return "Discover beautiful Jimbaran properties with AI help"
        default:
          return "Find your ideal Bali property with AI help"
      }
    }

    if (currentContext.propertyType) {
      switch (currentContext.propertyType.toLowerCase()) {
        case 'villa':
          return "Looking for a villa? Let me help you find the perfect match"
        case 'apartment':
          return "Interested in apartments? I can show you the best options"
        case 'guesthouse':
          return "Searching for guesthouses? Let's find your ideal stay"
        default:
          return "Find your ideal Bali property with AI help"
      }
    }

    return "Find your ideal Bali property with AI help"
  }



  // Handle user message
  const handleUserMessage = async (content: string) => {
    const userMessage: ChatMessage = {
      id: `msg-${Date.now()}-user`,
      role: 'user',
      content,
      timestamp: new Date()
    }

    dispatch({ type: 'ADD_MESSAGE', message: userMessage })
    dispatch({ type: 'SET_TYPING', isTyping: true })

    // Simulate AI response (placeholder for now)
    setTimeout(() => {
      const aiResponse: ChatMessage = {
        id: `msg-${Date.now()}-ai`,
        role: 'assistant',
        content: "Thanks for your message! I'm still learning. For now, please use our contact form for assistance.",
        timestamp: new Date()
      }
      dispatch({ type: 'ADD_MESSAGE', message: aiResponse })
      dispatch({ type: 'SET_TYPING', isTyping: false })
    }, 1500)
  }

  // Initialize conversation when widget opens
  useEffect(() => {
    if (state.isOpen && state.messages.length === 0) {
      const welcomeMessage: ChatMessage = {
        id: `msg-${Date.now()}`,
        role: 'assistant',
        content: getWelcomeMessage(),
        timestamp: new Date()
      }
      dispatch({ type: 'ADD_MESSAGE', message: welcomeMessage })
    }
  }, [state.isOpen])

  // Persist conversation state
  useEffect(() => {
    if (state.messages.length > 0) {
      sessionStorage.setItem('chat-conversation', JSON.stringify(state.messages))
    }
  }, [state.messages])

  // Load conversation state on mount
  useEffect(() => {
    const savedConversation = sessionStorage.getItem('chat-conversation')
    if (savedConversation) {
      try {
        const messages = JSON.parse(savedConversation)
        messages.forEach((message: ChatMessage) => {
          dispatch({ type: 'ADD_MESSAGE', message })
        })
      } catch (error) {
        console.error('Failed to load conversation:', error)
      }
    }
  }, [])

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Chat Widget Button */}
      {!state.isOpen && (
        <button
          onClick={() => dispatch({ type: 'TOGGLE_WIDGET' })}
          className="w-14 h-14 bg-emerald-600 hover:bg-emerald-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center group"
          aria-label="Open AI Chat Assistant"
        >
          <svg 
            className="w-6 h-6 group-hover:scale-110 transition-transform" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" 
            />
          </svg>
        </button>
      )}

      {/* Chat Interface */}
      {state.isOpen && (
        <div className={`bg-white rounded-lg shadow-2xl border border-gray-200 w-80 sm:w-96 transition-all duration-300 ${
          state.isMinimized ? 'h-14' : 'h-96'
        }`}>
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-emerald-600 text-white rounded-t-lg">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-sm">AI Property Assistant</h3>
                <p className="text-xs text-emerald-100">Online</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => dispatch({ type: 'MINIMIZE_WIDGET' })}
                className="text-emerald-100 hover:text-white transition-colors"
                aria-label="Minimize chat"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                </svg>
              </button>
              <button
                onClick={() => dispatch({ type: 'TOGGLE_WIDGET' })}
                className="text-emerald-100 hover:text-white transition-colors"
                aria-label="Close chat"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Chat Content */}
          {!state.isMinimized && (
            <>
              {/* Messages */}
              <div className="flex-1 p-4 space-y-4 h-64 overflow-y-auto">
                {state.messages.map((message) => (
                  <ChatBubble key={message.id} message={message} />
                ))}
                {state.isTyping && <TypingIndicator />}
              </div>

              {/* Input */}
              <div className="border-t border-gray-200 p-4">
                <ChatInput onSendMessage={handleUserMessage} disabled={state.isTyping} />
              </div>
            </>
          )}
        </div>
      )}
    </div>
  )
}

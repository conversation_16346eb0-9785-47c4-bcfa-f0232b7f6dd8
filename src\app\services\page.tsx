import { Metadata } from 'next'
import Link from 'next/link'

export const metadata: Metadata = {
  title: 'Our Services – Investment, Purchase & Consultation | Bali Property Scout',
  description: 'Expert Bali real estate services: AI-powered investment consultation, property purchase assistance, and personalized viewing arrangements. Trusted by 500+ clients since 2019.',
  keywords: 'bali property services, investment consultation bali, property purchase assistance, bali real estate agent, bali viewing arrangement, bali property consultation, buying property in bali guide, bali property investment tips, foreign ownership bali property, leasehold vs freehold bali, AI property matching',
}

export default function ServicesPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="relative min-h-[70vh] flex items-center justify-center overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <div
            className="w-full h-full bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: `url('https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?w=1920&auto=format&fit=crop&q=80')`
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-900/90 via-teal-800/80 to-emerald-700/90" />
        </div>

        <div className="relative z-10 max-w-5xl mx-auto px-4 text-center text-white">
          <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 md:p-12 border border-white/20 shadow-2xl">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Our <span className="bg-gradient-to-r from-emerald-200 to-teal-100 bg-clip-text text-transparent">Services</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-emerald-100 max-w-3xl mx-auto leading-relaxed">
              AI-powered property matching meets local expertise.
              Investment, Purchase & Consultation services trusted by 500+ clients since 2019.
            </p>
          </div>
        </div>
      </section>

      {/* Services Content */}
      <section className="py-16">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Investment, Purchase & Consultation
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Our AI-powered platform combines cutting-edge technology with local market expertise.
              Trusted by 500+ clients for investment consultation, property purchase assistance, and personalized viewing arrangements.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-16">
            {/* Investment Consultation */}
            <div className="relative overflow-hidden bg-white/10 backdrop-blur-xl border border-white/20 shadow-2xl rounded-2xl transition-all duration-500 ease-out hover:bg-white/20 hover:border-white/30 hover:shadow-3xl hover:scale-[1.02] hover:-translate-y-2">
              {/* Image */}
              <div className="relative w-full h-48 overflow-hidden rounded-t-2xl">
                <img
                  src="https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=800&auto=format&fit=crop&q=80"
                  alt="Investment consultation"
                  className="w-full h-full object-cover transition-transform duration-700 hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
              </div>

              {/* Content */}
              <div className="relative z-10 p-6 text-center space-y-6">
                <div className="w-16 h-16 bg-emerald-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto border border-emerald-500/30">
                  <svg className="w-8 h-8 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-gray-900">Investment Consultation</h3>
                <p className="text-gray-700 leading-relaxed">
                  Market analysis, ROI calculations, and due diligence for investors. Average yield of 12% in prime locations like Canggu.
                </p>
                <ul className="text-sm text-gray-600 space-y-2 text-left">
                  <li>• Market trend analysis and forecasting</li>
                  <li>• ROI calculations and yield projections</li>
                  <li>• Due diligence and property verification</li>
                  <li>• Investment strategy development</li>
                </ul>
                <a
                  href="https://app.bali-realestate.com/chatbot?query=investment-consultation"
                  className="inline-block bg-emerald-600/90 backdrop-blur-sm hover:bg-emerald-700/90 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 border border-emerald-500/30"
                >
                  Start Investment Conversation
                </a>
              </div>
            </div>

            {/* Property Purchase Assistance */}
            <div className="relative overflow-hidden bg-white/10 backdrop-blur-xl border border-white/20 shadow-2xl rounded-2xl transition-all duration-500 ease-out hover:bg-white/20 hover:border-white/30 hover:shadow-3xl hover:scale-[1.02] hover:-translate-y-2">
              {/* Image */}
              <div className="relative w-full h-48 overflow-hidden rounded-t-2xl">
                <img
                  src="https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=800&auto=format&fit=crop&q=80"
                  alt="Property purchase assistance"
                  className="w-full h-full object-cover transition-transform duration-700 hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
              </div>

              {/* Content */}
              <div className="relative z-10 p-6 text-center space-y-6">
                <div className="w-16 h-16 bg-blue-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto border border-blue-500/30">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-gray-900">Property Purchase Assistance</h3>
                <p className="text-gray-700 leading-relaxed">
                  Complete support from AI-powered property matching to closing. Navigate legal requirements and secure your dream property with confidence.
                </p>
                <ul className="text-sm text-gray-600 space-y-2 text-left">
                  <li>• AI-powered property selection and evaluation</li>
                  <li>• Price negotiation and deal structuring</li>
                  <li>• Legal documentation and compliance</li>
                  <li>• Closing coordination and handover</li>
                </ul>
                <a
                  href="https://app.bali-realestate.com/chatbot?query=buy-property-in-bali"
                  className="inline-block bg-blue-600/90 backdrop-blur-sm hover:bg-blue-700/90 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 border border-blue-500/30"
                >
                  Find Your Dream Home
                </a>
              </div>
            </div>

            {/* Consultation & Viewing */}
            <div className="relative overflow-hidden bg-white/10 backdrop-blur-xl border border-white/20 shadow-2xl rounded-2xl transition-all duration-500 ease-out hover:bg-white/20 hover:border-white/30 hover:shadow-3xl hover:scale-[1.02] hover:-translate-y-2">
              {/* Image */}
              <div className="relative w-full h-48 overflow-hidden rounded-t-2xl">
                <img
                  src="https://images.unsplash.com/photo-1600607687644-c7171b42498b?w=800&auto=format&fit=crop&q=80"
                  alt="Property consultation and viewing"
                  className="w-full h-full object-cover transition-transform duration-700 hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
              </div>

              {/* Content */}
              <div className="relative z-10 p-6 text-center space-y-6">
                <div className="w-16 h-16 bg-purple-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto border border-purple-500/30">
                  <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-gray-900">Consultation & Viewing</h3>
                <p className="text-gray-700 leading-relaxed">
                  Personalized consultations and seamless viewing experiences. Virtual tours, in-person visits, and expert market insights available 24/7.
                </p>
                <ul className="text-sm text-gray-600 space-y-2 text-left">
                  <li>• Virtual property tours and presentations</li>
                  <li>• In-person viewing arrangements</li>
                  <li>• Personal consultation sessions</li>
                  <li>• AI-powered market insights and recommendations</li>
                </ul>
                <a
                  href="https://app.bali-realestate.com/chatbot?query=schedule-viewing"
                  className="inline-block bg-purple-600/90 backdrop-blur-sm hover:bg-purple-700/90 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 border border-purple-500/30"
                >
                  Schedule a Viewing
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Navigation */}
      <section className="py-12 bg-emerald-50">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">
            Explore What's Available Now
          </h3>
          <div className="grid md:grid-cols-3 gap-6">
            <Link href="/locations" className="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Locations</h4>
              <p className="text-gray-600 text-sm">Browse all available locations</p>
            </Link>

            <Link href="/property-types" className="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 8h1m-1-4h1m4 4h1m-1-4h1" />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Property Types</h4>
              <p className="text-gray-600 text-sm">Explore different property options</p>
            </Link>

            <Link href="/" className="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Homepage</h4>
              <p className="text-gray-600 text-sm">Return to main page</p>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}

/**
 * Performance E2E Tests
 * 
 * End-to-end performance testing with Core Web Vitals
 */

import { test, expect } from '@playwright/test';

test.describe('Performance Tests', () => {
  test.describe('Core Web Vitals', () => {
    test('measures Largest Contentful Paint (LCP)', async ({ page }) => {
      await page.goto('/');
      
      // Measure LCP
      const lcp = await page.evaluate(() => {
        return new Promise((resolve) => {
          new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            resolve(lastEntry.startTime);
          }).observe({ entryTypes: ['largest-contentful-paint'] });
          
          // Fallback timeout
          setTimeout(() => resolve(0), 5000);
        });
      });
      
      // LCP should be under 2.5 seconds (good threshold)
      expect(lcp).toBeLessThan(2500);
    });

    test('measures First Contentful Paint (FCP)', async ({ page }) => {
      await page.goto('/');
      
      const fcp = await page.evaluate(() => {
        return new Promise((resolve) => {
          new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
            resolve(fcpEntry ? fcpEntry.startTime : 0);
          }).observe({ entryTypes: ['paint'] });
          
          setTimeout(() => resolve(0), 5000);
        });
      });
      
      // FCP should be under 1.8 seconds (good threshold)
      expect(fcp).toBeLessThan(1800);
    });

    test('measures Cumulative Layout Shift (CLS)', async ({ page }) => {
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      const cls = await page.evaluate(() => {
        return new Promise((resolve) => {
          let clsValue = 0;
          
          new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              if (!entry.hadRecentInput) {
                clsValue += entry.value;
              }
            }
          }).observe({ entryTypes: ['layout-shift'] });
          
          // Wait for layout shifts to settle
          setTimeout(() => resolve(clsValue), 3000);
        });
      });
      
      // CLS should be under 0.1 (good threshold)
      expect(cls).toBeLessThan(0.1);
    });

    test('measures Time to Interactive (TTI)', async ({ page }) => {
      const startTime = Date.now();
      await page.goto('/');
      
      // Wait for page to be interactive
      await page.waitForLoadState('networkidle');
      
      // Test interactivity by clicking a button
      const button = page.getByRole('button').first();
      if (await button.count() > 0) {
        await button.click();
      }
      
      const tti = Date.now() - startTime;
      
      // TTI should be under 3.8 seconds (good threshold)
      expect(tti).toBeLessThan(3800);
    });
  });

  test.describe('Resource Loading', () => {
    test('loads critical resources quickly', async ({ page }) => {
      const response = await page.goto('/');
      
      // Main document should load quickly
      expect(response?.status()).toBe(200);
      
      // Check critical CSS loads
      const criticalCSS = page.locator('style, link[rel="stylesheet"]');
      await expect(criticalCSS.first()).toBeAttached();
    });

    test('lazy loads non-critical images', async ({ page }) => {
      await page.goto('/');
      
      const lazyImages = page.locator('img[loading="lazy"]');
      const lazyImageCount = await lazyImages.count();
      
      // Should have lazy loaded images
      expect(lazyImageCount).toBeGreaterThan(0);
      
      // Check that lazy images load when scrolled into view
      if (lazyImageCount > 0) {
        const firstLazyImage = lazyImages.first();
        await firstLazyImage.scrollIntoViewIfNeeded();
        await expect(firstLazyImage).toHaveJSProperty('complete', true);
      }
    });

    test('preloads critical resources', async ({ page }) => {
      await page.goto('/');
      
      // Check for preload links
      const preloadLinks = page.locator('link[rel="preload"]');
      const preloadCount = await preloadLinks.count();
      
      // Should have some preloaded resources
      expect(preloadCount).toBeGreaterThan(0);
    });

    test('uses efficient image formats', async ({ page }) => {
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      const images = page.locator('img[src]');
      const imageCount = await images.count();
      
      let webpCount = 0;
      let avifCount = 0;
      
      for (let i = 0; i < Math.min(imageCount, 10); i++) {
        const src = await images.nth(i).getAttribute('src');
        if (src) {
          if (src.includes('webp') || src.includes('f=webp')) webpCount++;
          if (src.includes('avif') || src.includes('f=avif')) avifCount++;
        }
      }
      
      // Should use modern image formats
      expect(webpCount + avifCount).toBeGreaterThan(0);
    });
  });

  test.describe('Bundle Size', () => {
    test('keeps JavaScript bundle size reasonable', async ({ page }) => {
      const responses: any[] = [];
      
      page.on('response', (response) => {
        if (response.url().includes('.js') && response.status() === 200) {
          responses.push(response);
        }
      });
      
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      let totalJSSize = 0;
      for (const response of responses) {
        const headers = response.headers();
        const contentLength = headers['content-length'];
        if (contentLength) {
          totalJSSize += parseInt(contentLength, 10);
        }
      }
      
      // Total JS should be under 500KB (adjust based on your needs)
      expect(totalJSSize).toBeLessThan(500 * 1024);
    });

    test('keeps CSS bundle size reasonable', async ({ page }) => {
      const responses: any[] = [];
      
      page.on('response', (response) => {
        if (response.url().includes('.css') && response.status() === 200) {
          responses.push(response);
        }
      });
      
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      let totalCSSSize = 0;
      for (const response of responses) {
        const headers = response.headers();
        const contentLength = headers['content-length'];
        if (contentLength) {
          totalCSSSize += parseInt(contentLength, 10);
        }
      }
      
      // Total CSS should be under 100KB
      expect(totalCSSSize).toBeLessThan(100 * 1024);
    });
  });

  test.describe('Caching', () => {
    test('serves static assets with proper cache headers', async ({ page }) => {
      const responses: any[] = [];
      
      page.on('response', (response) => {
        if (response.url().includes('/_next/static/')) {
          responses.push(response);
        }
      });
      
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      for (const response of responses) {
        const headers = response.headers();
        
        // Static assets should have long cache headers
        expect(headers['cache-control']).toMatch(/max-age=\d+/);
      }
    });

    test('implements service worker caching', async ({ page }) => {
      await page.goto('/');
      
      // Check if service worker is registered
      const swRegistration = await page.evaluate(() => {
        return navigator.serviceWorker.getRegistration();
      });
      
      expect(swRegistration).toBeTruthy();
    });

    test('caches API responses appropriately', async ({ page }) => {
      const apiResponses: any[] = [];
      
      page.on('response', (response) => {
        if (response.url().includes('/api/')) {
          apiResponses.push(response);
        }
      });
      
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      for (const response of apiResponses) {
        const headers = response.headers();
        
        // API responses should have appropriate cache headers
        expect(headers).toHaveProperty('cache-control');
      }
    });
  });

  test.describe('Network Efficiency', () => {
    test('minimizes number of requests', async ({ page }) => {
      const requests: any[] = [];
      
      page.on('request', (request) => {
        requests.push(request);
      });
      
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      // Should keep requests under reasonable limit
      expect(requests.length).toBeLessThan(50);
    });

    test('uses HTTP/2 server push or preload hints', async ({ page }) => {
      await page.goto('/');
      
      // Check for preload hints
      const preloadLinks = page.locator('link[rel="preload"], link[rel="prefetch"]');
      const preloadCount = await preloadLinks.count();
      
      expect(preloadCount).toBeGreaterThan(0);
    });

    test('compresses responses', async ({ page }) => {
      const responses: any[] = [];
      
      page.on('response', (response) => {
        if (response.url().includes(page.url()) && response.status() === 200) {
          responses.push(response);
        }
      });
      
      await page.goto('/');
      
      for (const response of responses) {
        const headers = response.headers();
        
        // Responses should be compressed
        expect(headers['content-encoding']).toMatch(/gzip|br|deflate/);
      }
    });
  });

  test.describe('Memory Usage', () => {
    test('does not cause memory leaks', async ({ page }) => {
      await page.goto('/');
      
      const initialMemory = await page.evaluate(() => {
        return (performance as any).memory?.usedJSHeapSize || 0;
      });
      
      // Navigate around the site
      await page.goto('/contact');
      await page.goto('/locations/canggu');
      await page.goto('/');
      
      // Force garbage collection if available
      await page.evaluate(() => {
        if ((window as any).gc) {
          (window as any).gc();
        }
      });
      
      const finalMemory = await page.evaluate(() => {
        return (performance as any).memory?.usedJSHeapSize || 0;
      });
      
      // Memory usage should not increase dramatically
      if (initialMemory > 0 && finalMemory > 0) {
        const memoryIncrease = finalMemory - initialMemory;
        const memoryIncreasePercent = (memoryIncrease / initialMemory) * 100;
        
        // Memory increase should be reasonable (less than 50%)
        expect(memoryIncreasePercent).toBeLessThan(50);
      }
    });
  });

  test.describe('Performance Monitoring', () => {
    test('tracks performance metrics', async ({ page }) => {
      await page.goto('/');
      
      // Check if performance monitoring is active
      const hasPerformanceObserver = await page.evaluate(() => {
        return typeof PerformanceObserver !== 'undefined';
      });
      
      expect(hasPerformanceObserver).toBe(true);
    });

    test('sends performance data to analytics', async ({ page }) => {
      const analyticsRequests: any[] = [];
      
      page.on('request', (request) => {
        if (request.url().includes('google-analytics.com') || 
            request.url().includes('googletagmanager.com')) {
          analyticsRequests.push(request);
        }
      });
      
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      // Should send analytics data
      expect(analyticsRequests.length).toBeGreaterThan(0);
    });
  });
});

/**
 * Breadcrumbs Component
 * PRD Requirement: Add breadcrumbs component on all location and property type pages
 * Format: Home > Location > Property Type > Purpose
 */

'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface BreadcrumbsProps {
  items?: BreadcrumbItem[];
  className?: string;
}

export function Breadcrumbs({ items, className = '' }: BreadcrumbsProps) {
  const pathname = usePathname();
  
  // Auto-generate breadcrumbs if not provided
  const breadcrumbItems = items || generateBreadcrumbs(pathname);

  if (breadcrumbItems.length <= 1) {
    return null;
  }

  return (
    <nav 
      className={`flex items-center space-x-2 text-sm ${className}`}
      aria-label="Breadcrumb"
    >
      <ol className="flex items-center space-x-2">
        {breadcrumbItems.map((item, index) => {
          const isLast = index === breadcrumbItems.length - 1;
          
          return (
            <li key={index} className="flex items-center">
              {index > 0 && (
                <svg 
                  className="w-4 h-4 text-gray-400 mx-2" 
                  fill="currentColor" 
                  viewBox="0 0 20 20"
                >
                  <path 
                    fillRule="evenodd" 
                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" 
                    clipRule="evenodd" 
                  />
                </svg>
              )}
              
              {isLast || !item.href ? (
                <span className="text-gray-500 font-medium capitalize">
                  {item.label}
                </span>
              ) : (
                <Link 
                  href={item.href}
                  className="text-emerald-600 hover:text-emerald-700 transition-colors capitalize"
                >
                  {item.label}
                </Link>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
}

// Auto-generate breadcrumbs from pathname
function generateBreadcrumbs(pathname: string): BreadcrumbItem[] {
  const segments = pathname.split('/').filter(Boolean);
  const breadcrumbs: BreadcrumbItem[] = [
    { label: 'Home', href: '/' }
  ];

  // Handle different page types
  if (segments.length === 0) {
    return [{ label: 'Home' }];
  }

  // Location pages: /locations/[slug]
  if (segments[0] === 'locations' && segments[1]) {
    breadcrumbs.push(
      { label: 'Locations', href: '/locations' },
      { label: formatLabel(segments[1]) }
    );
  }
  
  // Property type pages: /property-types/[slug]
  else if (segments[0] === 'property-types' && segments[1]) {
    breadcrumbs.push(
      { label: 'Property Types', href: '/property-types' },
      { label: formatLabel(segments[1]) }
    );
  }
  
  // Location + Property + Transaction pages: /[location]/[property-type]/[transaction-type]
  else if (segments.length === 3) {
    const [location, propertyType, transactionType] = segments;
    breadcrumbs.push(
      { label: formatLabel(location), href: `/locations/${location}` },
      { label: formatLabel(propertyType), href: `/property-types/${propertyType}` },
      { label: formatTransactionType(transactionType) }
    );
  }
  
  // Generic fallback
  else {
    segments.forEach((segment, index) => {
      const href = index === segments.length - 1 ? undefined : `/${segments.slice(0, index + 1).join('/')}`;
      breadcrumbs.push({
        label: formatLabel(segment),
        href
      });
    });
  }

  return breadcrumbs;
}

// Format labels for display
function formatLabel(segment: string): string {
  return segment
    .replace(/-/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());
}

// Format transaction types for better display
function formatTransactionType(transactionType: string): string {
  const typeMap: Record<string, string> = {
    'long-term-rental': 'Long-term Rental',
    'for-sale-freehold': 'For Sale Freehold',
    'for-sale-leasehold': 'For Sale Leasehold',
    'short-term-rental': 'Short-term Rental',
  };
  
  return typeMap[transactionType] || formatLabel(transactionType);
}

export default Breadcrumbs;

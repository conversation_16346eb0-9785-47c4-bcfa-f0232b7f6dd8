import { CollectionConfig } from 'payload'
import { lexicalEditor } from '@payloadcms/richtext-lexical'

export const Content: CollectionConfig = {
  slug: 'content',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'category', 'status', 'publishDate', 'updatedAt'],
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      label: 'Article Title',
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      label: 'URL Slug',
      admin: {
        description: 'Used in URLs (e.g., /guides/buying-property-bali/)',
      },
    },
    {
      name: 'excerpt',
      type: 'textarea',
      label: 'Article Excerpt',
      admin: {
        description: 'Brief summary for listings and meta descriptions',
      },
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        { label: 'Regulatory Guides', value: 'regulatory' },
        { label: 'Location Comparisons', value: 'location-comparison' },
        { label: 'Process & Financial', value: 'process-financial' },
        { label: 'Lifestyle & Practical', value: 'lifestyle-practical' },
        { label: 'Market Analysis', value: 'market-analysis' },
        { label: 'Legal Advice', value: 'legal-advice' },
      ],
    },
    {
      name: 'priority',
      type: 'select',
      required: true,
      options: [
        { label: 'Zeer Hoog', value: 'zeer-hoog' },
        { label: 'Hoog', value: 'hoog' },
        { label: 'Middel', value: 'middel' },
        { label: 'Laag', value: 'laag' },
      ],
      defaultValue: 'middel',
      admin: {
        description: 'Content creation priority based on keyword research',
      },
    },
    {
      name: 'content',
      type: 'richText',
      editor: lexicalEditor({}),
      required: true,
      label: 'Article Content',
      admin: {
        description: 'Main article content (1000+ words for educational guides)',
      },
    },
    {
      name: 'tableOfContents',
      type: 'array',
      label: 'Table of Contents',
      fields: [
        {
          name: 'heading',
          type: 'text',
          required: true,
        },
        {
          name: 'anchor',
          type: 'text',
          required: true,
          admin: {
            description: 'URL anchor (e.g., #freehold-vs-leasehold)',
          },
        },
      ],
      admin: {
        description: 'Auto-generated or manual table of contents',
      },
    },
    {
      name: 'author',
      type: 'text',
      defaultValue: 'Bali Real Estate Team',
      label: 'Author Name',
    },
    {
      name: 'publishDate',
      type: 'date',
      required: true,
      defaultValue: () => new Date().toISOString(),
      label: 'Publish Date',
    },
    {
      name: 'lastUpdated',
      type: 'date',
      label: 'Last Updated',
      admin: {
        description: 'When content was last reviewed/updated',
      },
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Published', value: 'published' },
        { label: 'Archived', value: 'archived' },
      ],
      defaultValue: 'draft',
    },
    {
      name: 'readingTime',
      type: 'number',
      label: 'Reading Time (minutes)',
      admin: {
        description: 'Estimated reading time',
      },
    },
    {
      name: 'relatedLocations',
      type: 'relationship',
      relationTo: 'locations',
      hasMany: true,
      label: 'Related Locations',
      admin: {
        description: 'Locations mentioned or relevant to this content',
      },
    },
    {
      name: 'relatedPropertyTypes',
      type: 'relationship',
      relationTo: 'property-types',
      hasMany: true,
      label: 'Related Property Types',
    },
    {
      name: 'relatedContent',
      type: 'relationship',
      relationTo: 'content',
      hasMany: true,
      label: 'Related Articles',
      admin: {
        description: 'Other articles to suggest to readers',
      },
    },
    {
      name: 'featuredImage',
      type: 'upload',
      relationTo: 'media',
      label: 'Featured Image',
    },
    {
      name: 'gallery',
      type: 'array',
      label: 'Content Images',
      fields: [
        {
          name: 'image',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'caption',
          type: 'text',
        },
        {
          name: 'altText',
          type: 'text',
          required: true,
        },
      ],
    },
    {
      name: 'keyTakeaways',
      type: 'array',
      label: 'Key Takeaways',
      fields: [
        {
          name: 'takeaway',
          type: 'text',
          required: true,
        },
      ],
      admin: {
        description: 'Main points readers should remember',
      },
    },
    {
      name: 'faq',
      type: 'array',
      label: 'Article FAQ',
      fields: [
        {
          name: 'question',
          type: 'text',
          required: true,
        },
        {
          name: 'answer',
          type: 'richText',
          editor: lexicalEditor({}),
          required: true,
        },
      ],
    },
    {
      name: 'targetKeywords',
      type: 'array',
      label: 'Target Keywords',
      fields: [
        {
          name: 'keyword',
          type: 'text',
          required: true,
        },
        {
          name: 'priority',
          type: 'select',
          options: [
            { label: 'Primary', value: 'primary' },
            { label: 'Secondary', value: 'secondary' },
            { label: 'Long-tail', value: 'long-tail' },
          ],
          required: true,
        },
      ],
      admin: {
        description: 'SEO keywords this content targets',
      },
    },
    // SEO fields will be added by the SEO plugin
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Auto-generate slug from title if not provided
        if (data.title && !data.slug) {
          data.slug = data.title
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '')
        }
        
        // Auto-calculate reading time based on content length
        if (data.content && !data.readingTime) {
          const wordCount = data.content.toString().split(/\s+/).length
          data.readingTime = Math.ceil(wordCount / 200) // Average reading speed
        }
        
        return data
      },
    ],
  },
}

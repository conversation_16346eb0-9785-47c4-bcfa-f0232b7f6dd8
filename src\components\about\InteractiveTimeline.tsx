/**
 * Interactive Timeline Component
 * Bali Property Scout Website
 * 
 * Interactive stepper/timeline for company milestones with scroll animations
 * and key achievements (founded 2019, AI launch 2024, 500+ deals).
 */

'use client';

import React, { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';

export interface TimelineMilestone {
  id: string;
  year: string;
  title: string;
  description: string;
  achievement?: string;
  icon: React.ReactNode;
  color: string;
}

export interface InteractiveTimelineProps {
  milestones: TimelineMilestone[];
  className?: string;
}

export const InteractiveTimeline: React.FC<InteractiveTimelineProps> = ({
  milestones,
  className,
}) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [visibleItems, setVisibleItems] = useState<Set<number>>(new Set());
  const timelineRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<(HTMLDivElement | null)[]>([]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const index = parseInt(entry.target.getAttribute('data-index') || '0');
          if (entry.isIntersecting) {
            setVisibleItems(prev => new Set([...prev, index]));
            setActiveIndex(index);
          }
        });
      },
      {
        threshold: 0.6,
        rootMargin: '-20% 0px -20% 0px'
      }
    );

    itemRefs.current.forEach((ref) => {
      if (ref) observer.observe(ref);
    });

    return () => observer.disconnect();
  }, []);

  const scrollToMilestone = (index: number) => {
    itemRefs.current[index]?.scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    });
  };

  return (
    <div className={cn('relative', className)} ref={timelineRef}>
      {/* Timeline Navigation */}
      <div className="flex justify-center mb-12">
        <div className="flex space-x-2 bg-gray-100 rounded-full p-2">
          {milestones.map((milestone, index) => (
            <button
              key={milestone.id}
              onClick={() => scrollToMilestone(index)}
              className={cn(
                'px-4 py-2 rounded-full text-sm font-medium transition-all duration-300',
                activeIndex === index
                  ? 'bg-white text-gray-900 shadow-md'
                  : 'text-gray-600 hover:text-gray-900'
              )}
            >
              {milestone.year}
            </button>
          ))}
        </div>
      </div>

      {/* Timeline Content */}
      <div className="relative">
        {/* Vertical Line */}
        <div className="absolute left-1/2 transform -translate-x-1/2 w-1 bg-gray-200 h-full hidden md:block">
          <div 
            className="bg-gradient-to-b from-emerald-500 to-blue-500 w-full transition-all duration-1000 ease-out"
            style={{
              height: `${Math.min(((activeIndex + 1) / milestones.length) * 100, 100)}%`
            }}
          />
        </div>

        {/* Timeline Items */}
        <div className="space-y-16">
          {milestones.map((milestone, index) => (
            <div
              key={milestone.id}
              ref={(el) => (itemRefs.current[index] = el)}
              data-index={index}
              className={cn(
                'relative transition-all duration-700 ease-out',
                visibleItems.has(index) 
                  ? 'opacity-100 translate-y-0' 
                  : 'opacity-0 translate-y-8'
              )}
            >
              <div className={cn(
                'grid md:grid-cols-2 gap-8 items-center',
                index % 2 === 0 ? 'md:text-right' : 'md:text-left'
              )}>
                {/* Content */}
                <div className={cn(
                  'order-2',
                  index % 2 === 0 ? 'md:order-1' : 'md:order-2'
                )}>
                  <div className={cn(
                    'bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300',
                    visibleItems.has(index) && 'hover:scale-105'
                  )}>
                    <div className="flex items-center gap-3 mb-4">
                      <div 
                        className={cn(
                          'w-12 h-12 rounded-full flex items-center justify-center text-white',
                          milestone.color
                        )}
                      >
                        {milestone.icon}
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-gray-900">
                          {milestone.year}
                        </div>
                        {milestone.achievement && (
                          <div className="text-sm font-medium text-emerald-600">
                            {milestone.achievement}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <h3 className="text-xl font-bold text-gray-900 mb-3">
                      {milestone.title}
                    </h3>
                    
                    <p className="text-gray-600 leading-relaxed">
                      {milestone.description}
                    </p>
                  </div>
                </div>

                {/* Timeline Dot */}
                <div className={cn(
                  'order-1 flex justify-center',
                  index % 2 === 0 ? 'md:order-2' : 'md:order-1'
                )}>
                  <div className="relative">
                    <div 
                      className={cn(
                        'w-16 h-16 rounded-full flex items-center justify-center text-white shadow-lg transition-all duration-500',
                        milestone.color,
                        visibleItems.has(index) 
                          ? 'scale-100 shadow-2xl' 
                          : 'scale-75 shadow-lg'
                      )}
                    >
                      {milestone.icon}
                    </div>
                    
                    {/* Pulse Animation for Active Item */}
                    {activeIndex === index && (
                      <div 
                        className={cn(
                          'absolute inset-0 rounded-full animate-ping opacity-75',
                          milestone.color
                        )}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Progress Indicator */}
      <div className="mt-16 text-center">
        <div className="inline-flex items-center gap-2 bg-gray-100 rounded-full px-6 py-3">
          <div className="flex space-x-2">
            {milestones.map((_, index) => (
              <div
                key={index}
                className={cn(
                  'w-2 h-2 rounded-full transition-all duration-300',
                  index <= activeIndex 
                    ? 'bg-emerald-500' 
                    : 'bg-gray-300'
                )}
              />
            ))}
          </div>
          <span className="text-sm font-medium text-gray-600 ml-2">
            {activeIndex + 1} of {milestones.length}
          </span>
        </div>
      </div>
    </div>
  );
};

// Default milestones for Bali Property Scout
export const defaultMilestones: TimelineMilestone[] = [
  {
    id: 'founded',
    year: '2019',
    title: 'Bali Property Scout Founded',
    description: 'Started as a local real estate service helping expats and digital nomads find their perfect home in Bali. Founded by Indonesian and international partners with deep local knowledge.',
    achievement: 'Company Founded',
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 8h1m-1-4h1m4 4h1m-1-4h1" />
      </svg>
    ),
    color: 'bg-gradient-to-br from-emerald-500 to-emerald-600'
  },
  {
    id: 'growth',
    year: '2021',
    title: 'Rapid Growth & Expansion',
    description: 'Expanded our services across all major Bali locations. Built strong relationships with local property owners and established our reputation for transparent, reliable service.',
    achievement: '100+ Properties',
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
      </svg>
    ),
    color: 'bg-gradient-to-br from-blue-500 to-blue-600'
  },
  {
    id: 'milestone',
    year: '2023',
    title: '500+ Successful Deals',
    description: 'Reached a major milestone of 500+ successful property transactions. Became the trusted choice for expats, digital nomads, and investors seeking quality properties in Bali.',
    achievement: '500+ Deals',
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
    color: 'bg-gradient-to-br from-purple-500 to-purple-600'
  },
  {
    id: 'ai-launch',
    year: '2024',
    title: 'AI-Powered Platform Launch',
    description: 'Launched our revolutionary AI-powered property matching system. Combining cutting-edge technology with human expertise to provide personalized property recommendations in seconds.',
    achievement: 'AI Technology',
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
      </svg>
    ),
    color: 'bg-gradient-to-br from-orange-500 to-red-500'
  }
];

export default InteractiveTimeline;

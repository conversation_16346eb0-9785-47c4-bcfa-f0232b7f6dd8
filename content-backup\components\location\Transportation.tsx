interface TransportationProps {
  location: string
  locationInfo: { name: string }
}

// Detailed transportation content based on PRD requirements
const transportationContent = {
  'canggu': {
    overview: `Canggu offers excellent transportation connectivity, making it easy to navigate locally and access other parts of Bali. The area's infrastructure has significantly improved in recent years, with new roads and bypasses reducing traffic congestion. Most daily necessities are within walking or short scooter ride distance, while the international airport is just 45 minutes away.`,

    airportAccess: {
      distance: '25 kilometers',
      driveTime: '45-60 minutes',
      cost: '$8-15 USD',
      options: [
        'Private car/taxi (most comfortable)',
        'Ride-sharing apps (Grab, Gojek)',
        'Airport shuttle services',
        'Motorbike taxi for solo travelers'
      ]
    },

    localTransport: [
      {
        type: 'Scooter Rental',
        cost: '$3-5 per day',
        description: 'Most popular option for daily transportation',
        pros: ['Flexible', 'Affordable', 'Easy parking'],
        cons: ['Requires license', 'Weather dependent', 'Traffic safety']
      },
      {
        type: 'Car Rental',
        cost: '$15-25 per day',
        description: 'Ideal for families or longer trips',
        pros: ['Comfortable', 'Air conditioning', 'Storage space'],
        cons: ['Parking challenges', 'Traffic congestion', 'Higher cost']
      },
      {
        type: 'Driver Services',
        cost: '$25-35 per day',
        description: 'Private driver with car for full day',
        pros: ['No driving stress', 'Local knowledge', 'Door-to-door'],
        cons: ['Most expensive', 'Less flexibility', 'Language barriers']
      },
      {
        type: 'Ride-sharing',
        cost: '$2-8 per trip',
        description: 'Grab and Gojek available throughout area',
        pros: ['Convenient', 'No parking needed', 'Fixed pricing'],
        cons: ['Surge pricing', 'Wait times', 'Limited availability']
      }
    ],

    walkability: {
      score: '6/10',
      description: 'Moderately walkable with some pedestrian-friendly areas',
      walkableAreas: [
        'Batu Bolong main strip (cafes, coworking)',
        'Echo Beach area (restaurants, surf shops)',
        'Berawa beach road (beach clubs, dining)',
        'Local markets and shopping areas'
      ],
      challenges: [
        'Limited sidewalks on main roads',
        'Heavy traffic during peak hours',
        'Monsoon season flooding',
        'Uneven road surfaces'
      ]
    },

    bikeFriendly: {
      score: '8/10',
      description: 'Excellent for cycling with scenic routes',
      routes: [
        'Rice paddy cycling tours (2-3 hours)',
        'Coastal road to Tanah Lot (45 minutes)',
        'Berawa to Echo Beach coastal ride',
        'Traditional village exploration routes'
      ],
      rentals: [
        'Standard bikes: $2-3 per day',
        'Electric bikes: $8-12 per day',
        'Mountain bikes: $5-8 per day'
      ]
    },

    distances: [
      { destination: 'Seminyak', distance: '8 km', time: '15-20 minutes', transport: 'Scooter/Car' },
      { destination: 'Ubud', distance: '35 km', time: '45-60 minutes', transport: 'Car/Driver' },
      { destination: 'Kuta/Airport', distance: '25 km', time: '45-60 minutes', transport: 'Car/Taxi' },
      { destination: 'Sanur', distance: '30 km', time: '50-70 minutes', transport: 'Car/Driver' },
      { destination: 'Uluwatu', distance: '40 km', time: '60-80 minutes', transport: 'Car/Driver' }
    ]
  },

  'ubud': {
    overview: `Transportation in Ubud requires more planning due to its mountainous location and winding roads. While the scenic journey through rice terraces is beautiful, travel times can be longer than expected. Most visitors and residents rely on scooters or private drivers for transportation, as public transport is limited.`,

    airportAccess: {
      distance: '35 kilometers',
      driveTime: '60-90 minutes',
      cost: '$12-20 USD',
      options: [
        'Private car/taxi (recommended)',
        'Shared shuttle services',
        'Private driver (most comfortable)',
        'Ride-sharing (limited availability)'
      ]
    },

    localTransport: [
      {
        type: 'Scooter Rental',
        cost: '$3-5 per day',
        description: 'Essential for exploring Ubud and surroundings',
        pros: ['Navigate narrow roads', 'Affordable', 'Scenic routes'],
        cons: ['Hilly terrain', 'Rainy season challenges', 'Requires experience']
      },
      {
        type: 'Private Driver',
        cost: '$30-40 per day',
        description: 'Most popular choice for visitors',
        pros: ['Local knowledge', 'No driving stress', 'Cultural insights'],
        cons: ['Higher cost', 'Less spontaneous', 'Booking required']
      },
      {
        type: 'Car Rental',
        cost: '$20-30 per day',
        description: 'For experienced drivers only',
        pros: ['Independence', 'Storage space', 'Air conditioning'],
        cons: ['Narrow winding roads', 'Parking limitations', 'Stress factor']
      },
      {
        type: 'Bicycle',
        cost: '$2-4 per day',
        description: 'Great for short distances and exercise',
        pros: ['Eco-friendly', 'Healthy', 'Quiet exploration'],
        cons: ['Hilly terrain', 'Limited range', 'Weather dependent']
      }
    ],

    walkability: {
      score: '7/10',
      description: 'Central Ubud is quite walkable',
      walkableAreas: [
        'Ubud center (market, palace, restaurants)',
        'Monkey Forest Road (shops, cafes)',
        'Jalan Raya (main road attractions)',
        'Campuhan Ridge Walk (nature trail)'
      ],
      challenges: [
        'Steep hills and uneven terrain',
        'Limited street lighting',
        'Monsoon season conditions',
        'Traffic on main roads'
      ]
    },

    bikeFriendly: {
      score: '9/10',
      description: 'Excellent cycling destination with stunning scenery',
      routes: [
        'Tegallalang Rice Terraces (30 minutes)',
        'Traditional village cycling tours',
        'Campuhan Ridge to Keliki (2 hours)',
        'Sacred temple cycling routes'
      ],
      rentals: [
        'Standard bikes: $3-4 per day',
        'Electric bikes: $10-15 per day',
        'Mountain bikes: $6-10 per day'
      ]
    },

    distances: [
      { destination: 'Tegallalang Rice Terraces', distance: '10 km', time: '20-30 minutes', transport: 'Scooter/Car' },
      { destination: 'Canggu', distance: '35 km', time: '45-60 minutes', transport: 'Car/Driver' },
      { destination: 'Seminyak', distance: '30 km', time: '45-60 minutes', transport: 'Car/Driver' },
      { destination: 'Sanur', distance: '25 km', time: '40-50 minutes', transport: 'Car/Driver' },
      { destination: 'Mount Batur', distance: '45 km', time: '60-75 minutes', transport: 'Car/Driver' }
    ]
  },

  'seminyak': {
    overview: `Seminyak offers excellent transportation infrastructure with well-maintained roads, reliable ride-sharing services, and easy access to other parts of Bali. The area's proximity to the airport and central location make it highly convenient for both daily transportation and travel to other destinations.`,

    airportAccess: {
      distance: '15 kilometers',
      driveTime: '20-30 minutes',
      cost: '$6-12 USD',
      options: [
        'Private taxi (most reliable)',
        'Ride-sharing apps (Grab, Gojek)',
        'Hotel shuttle services',
        'Airport transfer services'
      ]
    },

    localTransport: [
      {
        type: 'Ride-sharing',
        cost: '$2-6 per trip',
        description: 'Most convenient option for short trips',
        pros: ['Reliable', 'Air conditioned', 'No parking needed'],
        cons: ['Surge pricing', 'Peak hour delays', 'App dependency']
      },
      {
        type: 'Scooter Rental',
        cost: '$4-6 per day',
        description: 'Popular for beach access and short trips',
        pros: ['Quick beach access', 'Easy parking', 'Affordable'],
        cons: ['Traffic congestion', 'Safety concerns', 'Weather exposure']
      },
      {
        type: 'Private Driver',
        cost: '$35-45 per day',
        description: 'Premium option for comfort and convenience',
        pros: ['Luxury experience', 'Professional service', 'Local expertise'],
        cons: ['Highest cost', 'Advance booking', 'Less flexibility']
      },
      {
        type: 'Car Rental',
        cost: '$20-35 per day',
        description: 'Good for families and longer stays',
        pros: ['Privacy', 'Storage space', 'Flexibility'],
        cons: ['Parking fees', 'Traffic stress', 'Fuel costs']
      }
    ],

    walkability: {
      score: '8/10',
      description: 'Highly walkable with good pedestrian infrastructure',
      walkableAreas: [
        'Seminyak Beach area (beach clubs, restaurants)',
        'Jalan Laksmana (shopping, dining)',
        'Jalan Oberoi (boutiques, cafes)',
        'Seminyak Village (shopping complex)'
      ],
      challenges: [
        'Busy traffic on main roads',
        'Limited crosswalks',
        'Monsoon season flooding',
        'Crowded sidewalks'
      ]
    },

    bikeFriendly: {
      score: '6/10',
      description: 'Moderate cycling conditions due to traffic',
      routes: [
        'Beach road cycling (early morning)',
        'Quiet residential streets',
        'Seminyak to Canggu coastal route',
        'Traditional village back roads'
      ],
      rentals: [
        'Standard bikes: $3-5 per day',
        'Electric bikes: $8-12 per day',
        'Beach cruisers: $4-6 per day'
      ]
    },

    distances: [
      { destination: 'Kuta/Airport', distance: '15 km', time: '20-30 minutes', transport: 'Car/Taxi' },
      { destination: 'Canggu', distance: '8 km', time: '15-20 minutes', transport: 'Scooter/Car' },
      { destination: 'Ubud', distance: '30 km', time: '45-60 minutes', transport: 'Car/Driver' },
      { destination: 'Sanur', distance: '20 km', time: '30-40 minutes', transport: 'Car/Taxi' },
      { destination: 'Uluwatu', distance: '25 km', time: '35-45 minutes', transport: 'Car/Driver' }
    ]
  }
}

const Transportation = ({ location, locationInfo }: TransportationProps) => {
  const content = transportationContent[location as keyof typeof transportationContent]

  if (!content) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">
            Transportation & Accessibility in {locationInfo.name}
          </h2>
          <p className="text-gray-600">Transportation information for {locationInfo.name}</p>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            Transportation & Accessibility
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Complete guide to getting around {locationInfo.name} and traveling to other destinations
          </p>
        </div>

        {/* Overview */}
        <div className="prose prose-lg max-w-none mb-16">
          <p className="text-gray-700 leading-relaxed">{content.overview}</p>
        </div>

        {/* Airport Access */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">Airport Access</h3>
          <div className="bg-white rounded-lg p-8 shadow-sm">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-emerald-600 mb-2">{content.airportAccess.distance}</div>
                <div className="text-gray-600">Distance</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-emerald-600 mb-2">{content.airportAccess.driveTime}</div>
                <div className="text-gray-600">Drive Time</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-emerald-600 mb-2">{content.airportAccess.cost}</div>
                <div className="text-gray-600">Typical Cost</div>
              </div>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Transportation Options:</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {content.airportAccess.options.map((option, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                    <span className="text-gray-700">{option}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Local Transportation */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">Local Transportation Options</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {content.localTransport.map((transport, index) => (
              <div key={index} className="bg-white rounded-lg p-6 shadow-sm">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-semibold text-gray-900">{transport.type}</h4>
                  <span className="text-emerald-600 font-semibold">{transport.cost}</span>
                </div>
                <p className="text-gray-600 mb-4">{transport.description}</p>
                <div className="space-y-3">
                  <div>
                    <div className="text-sm font-medium text-green-700 mb-1">Pros:</div>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {transport.pros.map((pro, i) => (
                        <li key={i} className="flex items-center gap-2">
                          <div className="w-1 h-1 bg-green-500 rounded-full"></div>
                          {pro}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-red-700 mb-1">Cons:</div>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {transport.cons.map((con, i) => (
                        <li key={i} className="flex items-center gap-2">
                          <div className="w-1 h-1 bg-red-500 rounded-full"></div>
                          {con}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Walkability & Cycling */}
        <div className="mb-16">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Walkability */}
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-bold text-gray-900">Walkability</h3>
                <div className="text-2xl font-bold text-emerald-600">{content.walkability.score}</div>
              </div>
              <p className="text-gray-600 mb-4">{content.walkability.description}</p>

              <div className="mb-4">
                <h4 className="font-semibold text-gray-900 mb-2">Walkable Areas:</h4>
                <ul className="space-y-1">
                  {content.walkability.walkableAreas.map((area, index) => (
                    <li key={index} className="flex items-center gap-2 text-sm text-gray-600">
                      <div className="w-1 h-1 bg-emerald-500 rounded-full"></div>
                      {area}
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Challenges:</h4>
                <ul className="space-y-1">
                  {content.walkability.challenges.map((challenge, index) => (
                    <li key={index} className="flex items-center gap-2 text-sm text-gray-600">
                      <div className="w-1 h-1 bg-orange-500 rounded-full"></div>
                      {challenge}
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Bike Friendly */}
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-bold text-gray-900">Bike Friendly</h3>
                <div className="text-2xl font-bold text-emerald-600">{content.bikeFriendly.score}</div>
              </div>
              <p className="text-gray-600 mb-4">{content.bikeFriendly.description}</p>

              <div className="mb-4">
                <h4 className="font-semibold text-gray-900 mb-2">Popular Routes:</h4>
                <ul className="space-y-1">
                  {content.bikeFriendly.routes.map((route, index) => (
                    <li key={index} className="flex items-center gap-2 text-sm text-gray-600">
                      <div className="w-1 h-1 bg-emerald-500 rounded-full"></div>
                      {route}
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Rental Prices:</h4>
                <ul className="space-y-1">
                  {content.bikeFriendly.rentals.map((rental, index) => (
                    <li key={index} className="flex items-center gap-2 text-sm text-gray-600">
                      <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                      {rental}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Distance to Popular Destinations */}
        <div>
          <h3 className="text-2xl font-bold text-gray-900 mb-8">Distance to Popular Destinations</h3>
          <div className="bg-white rounded-lg overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Destination</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Distance</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Travel Time</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Best Transport</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {content.distances.map((destination, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {destination.destination}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {destination.distance}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {destination.time}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-emerald-600 font-medium">
                        {destination.transport}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Transportation
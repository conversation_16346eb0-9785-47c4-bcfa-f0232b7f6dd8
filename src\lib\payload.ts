import { getPayload } from 'payload'
import config from '../../payload.config'

let payload: any = null
let payloadInitialized = false
let payloadError: Error | null = null

export const getPayloadClient = async () => {
  // Return cached payload if already initialized
  if (payload && payloadInitialized) {
    return payload
  }

  // If previous initialization failed, throw the cached error
  if (payloadError) {
    throw payloadError
  }

  try {
    console.log('🔄 Initializing Payload CMS client...')
    const startTime = Date.now()

    payload = await getPayload({ config })
    payloadInitialized = true

    const initTime = Date.now() - startTime
    console.log(`✅ Payload CMS initialized successfully in ${initTime}ms`)

    return payload
  } catch (error) {
    console.error('❌ Failed to initialize Payload CMS:', error)
    payloadError = error as Error
    payloadInitialized = false
    throw error
  }
}

// In-memory cache for locations and property types
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

const cache = new Map<string, CacheEntry<any>>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes cache

function getCachedData<T>(key: string): T | null {
  const entry = cache.get(key);
  if (!entry) return null;

  const now = Date.now();
  if (now - entry.timestamp > entry.ttl) {
    cache.delete(key);
    return null;
  }

  console.log(`📦 Using cached data for: ${key}`);
  return entry.data;
}

function setCachedData<T>(key: string, data: T, ttl: number = CACHE_TTL): void {
  cache.set(key, {
    data,
    timestamp: Date.now(),
    ttl
  });
}

export const getLocations = async () => {
  // Check cache first
  const cacheKey = 'locations';
  const cachedLocations = getCachedData(cacheKey);
  if (cachedLocations) {
    return cachedLocations;
  }
  // Check if we're in build environment or database is not available
  const isBuildTime = process.env.BUILD_MODE === 'static' || (process.env.VERCEL_ENV && !process.env.DATABASE_URI);

  if (isBuildTime) {
    // Return static locations for build-time
    return [
      { slug: 'canggu', name: 'Canggu', updatedAt: new Date().toISOString() },
      { slug: 'ubud', name: 'Ubud', updatedAt: new Date().toISOString() },
      { slug: 'seminyak', name: 'Seminyak', updatedAt: new Date().toISOString() },
      { slug: 'sanur', name: 'Sanur', updatedAt: new Date().toISOString() },
      { slug: 'jimbaran', name: 'Jimbaran', updatedAt: new Date().toISOString() },
    ]
  }

  try {
    console.log('🔍 Fetching locations from database...');
    const startTime = Date.now();

    const payload = await getPayloadClient()
    const locations = await payload.find({
      collection: 'locations',
      limit: 100,
      sort: 'name', // Sort alphabetically for consistent ordering
    })

    const fetchTime = Date.now() - startTime;
    console.log(`✅ Fetched ${locations.docs.length} locations in ${fetchTime}ms`);

    // Cache the result
    setCachedData(cacheKey, locations.docs);

    return locations.docs
  } catch (error) {
    console.warn('Database not available, using static locations:', error)
    // Fallback to static locations
    const staticLocations = getStaticLocations();
    // Cache static locations with shorter TTL
    setCachedData(cacheKey, staticLocations, 60000); // 1 minute for static data
    return staticLocations;
  }
}

// Static locations fallback helper
function getStaticLocations() {
  return [
    { slug: 'canggu', name: 'Canggu', updatedAt: new Date().toISOString() },
    { slug: 'ubud', name: 'Ubud', updatedAt: new Date().toISOString() },
    { slug: 'seminyak', name: 'Seminyak', updatedAt: new Date().toISOString() },
    { slug: 'sanur', name: 'Sanur', updatedAt: new Date().toISOString() },
    { slug: 'jimbaran', name: 'Jimbaran', updatedAt: new Date().toISOString() },
  ];
}

// Static property types fallback helper
function getStaticPropertyTypes() {
  return [
    { slug: 'villa', name: 'Villa', updatedAt: new Date().toISOString() },
    { slug: 'apartment', name: 'Apartment', updatedAt: new Date().toISOString() },
    { slug: 'guesthouse', name: 'Guesthouse', updatedAt: new Date().toISOString() },
    { slug: 'land', name: 'Land', updatedAt: new Date().toISOString() },
  ];
}

export const getLocationBySlug = async (slug: string) => {
  // Check if we're in build environment or database is not available
  const isBuildTime = process.env.BUILD_MODE === 'static' || (process.env.VERCEL_ENV && !process.env.DATABASE_URI);

  if (isBuildTime) {
    // Return static location data for build-time
    const staticLocations: any = {
      'canggu': { slug: 'canggu', name: 'Canggu', updatedAt: new Date().toISOString() },
      'ubud': { slug: 'ubud', name: 'Ubud', updatedAt: new Date().toISOString() },
      'seminyak': { slug: 'seminyak', name: 'Seminyak', updatedAt: new Date().toISOString() },
      'sanur': { slug: 'sanur', name: 'Sanur', updatedAt: new Date().toISOString() },
      'jimbaran': { slug: 'jimbaran', name: 'Jimbaran', updatedAt: new Date().toISOString() },
    }
    return staticLocations[slug] || null
  }

  try {
    const payload = await getPayloadClient()
    const locations = await payload.find({
      collection: 'locations',
      where: {
        slug: {
          equals: slug,
        },
      },
      limit: 1,
    })
    return locations.docs[0] || null
  } catch (error) {
    console.warn('Database not available, using static location data:', error)
    // Fallback to static location data
    const staticLocations: any = {
      'canggu': { slug: 'canggu', name: 'Canggu', updatedAt: new Date().toISOString() },
      'ubud': { slug: 'ubud', name: 'Ubud', updatedAt: new Date().toISOString() },
      'seminyak': { slug: 'seminyak', name: 'Seminyak', updatedAt: new Date().toISOString() },
      'sanur': { slug: 'sanur', name: 'Sanur', updatedAt: new Date().toISOString() },
      'jimbaran': { slug: 'jimbaran', name: 'Jimbaran', updatedAt: new Date().toISOString() },
    }
    return staticLocations[slug] || null
  }
}

export const getPropertyTypes = async () => {
  // Check cache first
  const cacheKey = 'property-types';
  const cachedPropertyTypes = getCachedData(cacheKey);
  if (cachedPropertyTypes) {
    return cachedPropertyTypes;
  }

  // Check if we're in build environment or database is not available
  const isBuildTime = process.env.BUILD_MODE === 'static' || (process.env.VERCEL_ENV && !process.env.DATABASE_URI);

  if (isBuildTime) {
    // Return static property types for build-time
    const staticPropertyTypes = [
      { slug: 'villa', name: 'Villa', updatedAt: new Date().toISOString() },
      { slug: 'apartment', name: 'Apartment', updatedAt: new Date().toISOString() },
      { slug: 'guesthouse', name: 'Guesthouse', updatedAt: new Date().toISOString() },
      { slug: 'land', name: 'Land', updatedAt: new Date().toISOString() },
    ];
    // Cache static data
    setCachedData(cacheKey, staticPropertyTypes, 60000); // 1 minute for static data
    return staticPropertyTypes;
  }

  try {
    console.log('🔍 Fetching property types from database...');
    const startTime = Date.now();

    const payload = await getPayloadClient()
    const propertyTypes = await payload.find({
      collection: 'property-types',
      limit: 100,
      sort: 'name', // Sort alphabetically for consistent ordering
    })

    const fetchTime = Date.now() - startTime;
    console.log(`✅ Fetched ${propertyTypes.docs.length} property types in ${fetchTime}ms`);

    // Cache the result
    setCachedData(cacheKey, propertyTypes.docs);

    return propertyTypes.docs
  } catch (error) {
    console.warn('Database not available, using static property types:', error)
    const staticPropertyTypes = getStaticPropertyTypes();
    // Cache static property types with shorter TTL
    setCachedData(cacheKey, staticPropertyTypes, 60000); // 1 minute for static data
    return staticPropertyTypes;
  }
}

export const getPropertyTypeBySlug = async (slug: string) => {
  try {
    const payload = await getPayloadClient()
    const propertyTypes = await payload.find({
      collection: 'property-types',
      where: {
        slug: {
          equals: slug,
        },
      },
      limit: 1,
    })
    return propertyTypes.docs[0] || null
  } catch (error) {
    console.warn('Database not available, using static property type data:', error)
    // Fallback to static property type data
    const staticPropertyTypes: any = {
      'villa': { slug: 'villa', name: 'Villa', updatedAt: new Date().toISOString() },
      'apartment': { slug: 'apartment', name: 'Apartment', updatedAt: new Date().toISOString() },
      'guesthouse': { slug: 'guesthouse', name: 'Guesthouse', updatedAt: new Date().toISOString() },
      'land': { slug: 'land', name: 'Land', updatedAt: new Date().toISOString() },
    }
    return staticPropertyTypes[slug] || null
  }
}

/**
 * Testimonial Component Tests
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { Testimonial, TestimonialCard, TestimonialQuote, TestimonialCarousel } from '../Testimonial';
import type { TestimonialData } from '../Testimonial';

// Mock testimonial data
const mockTestimonial: TestimonialData = {
  id: '1',
  name: '<PERSON>',
  title: 'Digital Nomad',
  company: 'Tech Corp',
  avatar: '/avatar1.jpg',
  content: 'Amazing service and beautiful properties. Highly recommended!',
  rating: 5,
  location: 'Canggu, Bali',
  date: '2024-01-15',
  propertyType: 'Villa',
  featured: false,
};

const mockTestimonials: TestimonialData[] = [
  mockTestimonial,
  {
    id: '2',
    name: '<PERSON>',
    title: 'Entrepreneur',
    avatar: '/avatar2.jpg',
    content: 'Found the perfect villa for my family. Great experience!',
    rating: 4,
    location: 'Ubud, Bali',
    date: '2024-01-10',
  },
  {
    id: '3',
    name: '<PERSON>',
    company: 'Remote Inc',
    content: 'Professional service and excellent property selection.',
    rating: 5,
    location: 'Seminyak, Bali',
  },
];

describe('Testimonial Component', () => {
  describe('Single Testimonial Rendering', () => {
    it('renders testimonial content correctly', () => {
      render(<Testimonial testimonial={mockTestimonial} />);
      
      expect(screen.getByText('"Amazing service and beautiful properties. Highly recommended!"')).toBeInTheDocument();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Digital Nomad at Tech Corp')).toBeInTheDocument();
      expect(screen.getByText('Canggu, Bali')).toBeInTheDocument();
    });

    it('renders testimonial without optional fields', () => {
      const minimalTestimonial: TestimonialData = {
        id: '1',
        name: 'John Doe',
        content: 'Great service!',
      };
      
      render(<Testimonial testimonial={minimalTestimonial} />);
      
      expect(screen.getByText('"Great service!"')).toBeInTheDocument();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    it('shows rating when provided and showRating is true', () => {
      const { container } = render(<Testimonial testimonial={mockTestimonial} showRating={true} />);

      // Check that all 5 stars are filled (yellow)
      const stars = container.querySelectorAll('svg.text-yellow-400');
      expect(stars).toHaveLength(5);
    });

    it('hides rating when showRating is false', () => {
      const { container } = render(<Testimonial testimonial={mockTestimonial} showRating={false} />);

      // Check that no stars exist
      const stars = container.querySelectorAll('svg.text-yellow-400, svg.text-neutral-300');
      expect(stars).toHaveLength(0);
    });

    it('shows avatar when provided and showAvatar is true', () => {
      render(<Testimonial testimonial={mockTestimonial} showAvatar={true} />);
      
      const avatar = screen.getByAltText('John Doe');
      expect(avatar).toBeInTheDocument();
      expect(avatar).toHaveAttribute('src', '/avatar1.jpg');
    });

    it('hides avatar when showAvatar is false', () => {
      render(<Testimonial testimonial={mockTestimonial} showAvatar={false} />);
      
      expect(screen.queryByAltText('John Doe')).not.toBeInTheDocument();
    });

    it('shows date when showDate is true', () => {
      render(<Testimonial testimonial={mockTestimonial} showDate={true} />);
      
      expect(screen.getByText('2024-01-15')).toBeInTheDocument();
    });

    it('hides date when showDate is false', () => {
      render(<Testimonial testimonial={mockTestimonial} showDate={false} />);
      
      expect(screen.queryByText('2024-01-15')).not.toBeInTheDocument();
    });
  });

  describe('Multiple Testimonials Grid', () => {
    it('renders multiple testimonials in grid', () => {
      render(<Testimonial testimonials={mockTestimonials} />);
      
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      expect(screen.getByText('Mike Johnson')).toBeInTheDocument();
    });

    it('handles empty testimonials array', () => {
      const { container } = render(<Testimonial testimonials={[]} />);
      expect(container.firstChild).toBeNull();
    });
  });

  describe('Carousel Mode', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('renders carousel with navigation buttons', () => {
      render(<Testimonial testimonials={mockTestimonials} carousel={true} />);
      
      expect(screen.getByLabelText('Previous testimonial')).toBeInTheDocument();
      expect(screen.getByLabelText('Next testimonial')).toBeInTheDocument();
    });

    it('shows only current testimonial in carousel', () => {
      render(<Testimonial testimonials={mockTestimonials} carousel={true} />);
      
      // Should show first testimonial
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
      expect(screen.queryByText('Mike Johnson')).not.toBeInTheDocument();
    });

    it('navigates to next testimonial', () => {
      render(<Testimonial testimonials={mockTestimonials} carousel={true} />);
      
      const nextButton = screen.getByLabelText('Next testimonial');
      fireEvent.click(nextButton);
      
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
    });

    it('navigates to previous testimonial', () => {
      render(<Testimonial testimonials={mockTestimonials} carousel={true} />);
      
      const prevButton = screen.getByLabelText('Previous testimonial');
      fireEvent.click(prevButton);
      
      // Should wrap to last testimonial
      expect(screen.getByText('Mike Johnson')).toBeInTheDocument();
      expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
    });

    it('wraps around when navigating past last testimonial', () => {
      render(<Testimonial testimonials={mockTestimonials} carousel={true} />);
      
      const nextButton = screen.getByLabelText('Next testimonial');
      
      // Navigate to last testimonial
      fireEvent.click(nextButton); // Jane
      fireEvent.click(nextButton); // Mike
      fireEvent.click(nextButton); // Should wrap to John
      
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    it('shows dots indicator', () => {
      render(<Testimonial testimonials={mockTestimonials} carousel={true} />);
      
      const dots = screen.getAllByLabelText(/Go to testimonial/);
      expect(dots).toHaveLength(mockTestimonials.length);
    });

    it('navigates when dot is clicked', () => {
      render(<Testimonial testimonials={mockTestimonials} carousel={true} />);
      
      const thirdDot = screen.getByLabelText('Go to testimonial 3');
      fireEvent.click(thirdDot);
      
      expect(screen.getByText('Mike Johnson')).toBeInTheDocument();
    });

    it('auto-plays when autoPlay is set', () => {
      render(<Testimonial testimonials={mockTestimonials} carousel={true} autoPlay={1000} />);

      expect(screen.getByText('John Doe')).toBeInTheDocument();

      // Fast-forward time
      act(() => {
        vi.advanceTimersByTime(1000);
      });

      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    });

    it('does not auto-play with single testimonial', () => {
      render(<Testimonial testimonials={[mockTestimonial]} carousel={true} autoPlay={1000} />);
      
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      
      // Fast-forward time
      vi.advanceTimersByTime(1000);
      
      // Should still show the same testimonial
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    it('hides navigation buttons for single testimonial', () => {
      render(<Testimonial testimonials={[mockTestimonial]} carousel={true} />);
      
      expect(screen.queryByLabelText('Previous testimonial')).not.toBeInTheDocument();
      expect(screen.queryByLabelText('Next testimonial')).not.toBeInTheDocument();
    });
  });

  describe('Variant Styles', () => {
    it('applies card variant styles by default', () => {
      const { container } = render(<Testimonial testimonial={mockTestimonial} />);
      const testimonialContainer = container.querySelector('.bg-white.border.border-neutral-200.rounded-lg');
      expect(testimonialContainer).toBeInTheDocument();
    });

    it('applies quote variant styles', () => {
      const { container } = render(<Testimonial testimonial={mockTestimonial} variant="quote" />);
      const testimonialContainer = container.querySelector('.bg-neutral-50.rounded-lg');
      expect(testimonialContainer).toBeInTheDocument();
    });

    it('applies minimal variant styles', () => {
      const { container } = render(<Testimonial testimonial={mockTestimonial} variant="minimal" />);
      const testimonialContainer = container.querySelector('.text-center');
      expect(testimonialContainer).toBeInTheDocument();
    });

    it('applies featured variant styles', () => {
      const { container } = render(<Testimonial testimonial={mockTestimonial} variant="featured" />);
      const testimonialContainer = container.querySelector('.bg-primary-lightest.border-l-4.border-primary');
      expect(testimonialContainer).toBeInTheDocument();
    });
  });

  describe('Star Rating', () => {
    it('renders correct number of filled stars', () => {
      const { container } = render(<Testimonial testimonial={{ ...mockTestimonial, rating: 3 }} />);

      const filledStars = container.querySelectorAll('svg.text-yellow-400');
      const emptyStars = container.querySelectorAll('svg.text-neutral-300');

      expect(filledStars).toHaveLength(3);
      expect(emptyStars).toHaveLength(2);
    });

    it('handles zero rating by not showing stars', () => {
      const { container } = render(<Testimonial testimonial={{ ...mockTestimonial, rating: 0 }} />);

      // When rating is 0, no stars should be rendered at all
      const allStars = container.querySelectorAll('svg.text-yellow-400, svg.text-neutral-300');

      expect(allStars).toHaveLength(0);
    });
  });

  describe('Custom ClassName', () => {
    it('applies custom className', () => {
      const { container } = render(
        <Testimonial testimonial={mockTestimonial} className="custom-testimonial" />
      );
      
      expect(container.firstChild).toHaveClass('custom-testimonial');
    });
  });

  describe('Error Handling', () => {
    it('returns null when no testimonial or testimonials provided', () => {
      const { container } = render(<Testimonial />);
      expect(container.firstChild).toBeNull();
    });
  });
});

describe('Testimonial Variants', () => {
  it('TestimonialCard applies card variant', () => {
    const { container } = render(<TestimonialCard testimonial={mockTestimonial} />);
    const testimonialContainer = container.querySelector('.bg-white.border.border-neutral-200');
    expect(testimonialContainer).toBeInTheDocument();
  });

  it('TestimonialQuote applies quote variant', () => {
    const { container } = render(<TestimonialQuote testimonial={mockTestimonial} />);
    const testimonialContainer = container.querySelector('.bg-neutral-50');
    expect(testimonialContainer).toBeInTheDocument();
  });

  it('TestimonialCarousel enables carousel mode', () => {
    render(<TestimonialCarousel testimonials={mockTestimonials} />);

    expect(screen.getByLabelText('Previous testimonial')).toBeInTheDocument();
    expect(screen.getByLabelText('Next testimonial')).toBeInTheDocument();
  });
});

/**
 * Advanced SEO Meta Tags Component
 * STORY-003: Advanced SEO & Content Optimization
 */

import Head from 'next/head';

interface AdvancedSEOProps {
  title: string;
  description: string;
  keywords?: string;
  canonical?: string;
  ogImage?: string;
  ogType?: 'website' | 'article' | 'product';
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';
  structuredData?: any;
  alternateLanguages?: Array<{ hreflang: string; href: string }>;
  breadcrumbs?: Array<{ name: string; url: string }>;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  noIndex?: boolean;
  noFollow?: boolean;
}

export function AdvancedSEO({
  title,
  description,
  keywords,
  canonical,
  ogImage = '/images/og-default.jpg',
  ogType = 'website',
  twitterCard = 'summary_large_image',
  structuredData,
  alternateLanguages,
  breadcrumbs,
  author,
  publishedTime,
  modifiedTime,
  noIndex = false,
  noFollow = false
}: AdvancedSEOProps) {
  const fullTitle = title.includes('Bali Real Estate') ? title : `${title} | Bali Real Estate`;
  const fullCanonical = canonical || (typeof window !== 'undefined' ? window.location.href : '');
  const fullOgImage = ogImage.startsWith('http') ? ogImage : `https://bali-real-estate.com${ogImage}`;

  // Generate robots meta content
  const robotsContent = [
    noIndex ? 'noindex' : 'index',
    noFollow ? 'nofollow' : 'follow',
    'max-snippet:-1',
    'max-image-preview:large',
    'max-video-preview:-1'
  ].join(', ');

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      {keywords && <meta name="keywords" content={keywords} />}
      <meta name="robots" content={robotsContent} />
      <meta name="googlebot" content={robotsContent} />
      
      {/* Canonical URL */}
      {canonical && <link rel="canonical" href={canonical} />}
      
      {/* Author and Publication Info */}
      {author && <meta name="author" content={author} />}
      {publishedTime && <meta name="article:published_time" content={publishedTime} />}
      {modifiedTime && <meta name="article:modified_time" content={modifiedTime} />}
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={fullCanonical} />
      <meta property="og:image" content={fullOgImage} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content={title} />
      <meta property="og:site_name" content="Bali Real Estate" />
      <meta property="og:locale" content="en_US" />
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullOgImage} />
      <meta name="twitter:image:alt" content={title} />
      <meta name="twitter:site" content="@balirealestate" />
      <meta name="twitter:creator" content="@balirealestate" />
      
      {/* Additional SEO Meta Tags */}
      <meta name="theme-color" content="#059669" />
      <meta name="msapplication-TileColor" content="#059669" />
      <meta name="application-name" content="Bali Real Estate" />
      <meta name="apple-mobile-web-app-title" content="Bali Real Estate" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="mobile-web-app-capable" content="yes" />
      
      {/* Geographic Meta Tags */}
      <meta name="geo.region" content="ID-BA" />
      <meta name="geo.placename" content="Bali, Indonesia" />
      <meta name="geo.position" content="-8.3405;115.0920" />
      <meta name="ICBM" content="-8.3405, 115.0920" />
      
      {/* Language Alternatives */}
      {alternateLanguages?.map((lang) => (
        <link
          key={lang.hreflang}
          rel="alternate"
          hrefLang={lang.hreflang}
          href={lang.href}
        />
      ))}
      
      {/* Preconnect to External Domains */}
      <link rel="preconnect" href="https://www.google-analytics.com" />
      <link rel="preconnect" href="https://www.googletagmanager.com" />
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* DNS Prefetch */}
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//www.googletagmanager.com" />
      
      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      )}
      
      {/* Breadcrumb Structured Data */}
      {breadcrumbs && breadcrumbs.length > 1 && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'BreadcrumbList',
              itemListElement: breadcrumbs.map((crumb, index) => ({
                '@type': 'ListItem',
                position: index + 1,
                name: crumb.name,
                item: crumb.url
              }))
            })
          }}
        />
      )}
    </Head>
  );
}

// Hook for dynamic SEO updates
export function useSEO(seoData: Partial<AdvancedSEOProps>) {
  // This can be used to dynamically update SEO data
  // Implementation would depend on your state management solution
  return seoData;
}

// Utility functions for SEO optimization
export const seoUtils = {
  // Generate optimized title
  generateTitle: (base: string, location?: string, propertyType?: string) => {
    let title = base;
    if (propertyType) title = `${propertyType} ${title}`;
    if (location) title = `${title} in ${location}`;
    return `${title} | Bali Real Estate`;
  },

  // Generate optimized description
  generateDescription: (base: string, location?: string, propertyType?: string, priceRange?: string) => {
    let description = base;
    if (location) description = description.replace('[location]', location);
    if (propertyType) description = description.replace('[property-type]', propertyType);
    if (priceRange) description = description.replace('[price-range]', priceRange);
    return description;
  },

  // Generate keywords
  generateKeywords: (base: string[], location?: string, propertyType?: string) => {
    const keywords = [...base];
    if (location) {
      keywords.push(`${location} real estate`, `property in ${location}`, `${location} rentals`);
    }
    if (propertyType) {
      keywords.push(`${propertyType} bali`, `${propertyType} rental`, `buy ${propertyType}`);
    }
    return keywords.join(', ');
  },

  // Truncate description to optimal length
  truncateDescription: (description: string, maxLength: number = 160) => {
    if (description.length <= maxLength) return description;
    return description.substring(0, maxLength - 3).trim() + '...';
  },

  // Generate canonical URL
  generateCanonical: (path: string) => {
    return `https://bali-real-estate.com${path}`;
  }
};

export default AdvancedSEO;

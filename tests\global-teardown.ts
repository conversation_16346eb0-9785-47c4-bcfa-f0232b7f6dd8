/**
 * Playwright Global Teardown
 * 
 * Global teardown for E2E tests
 */

import { FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global test teardown...');
  
  // Clean up any global resources
  // For example, close database connections, clean up test data, etc.
  
  console.log('✅ Global teardown completed');
}

export default globalTeardown;

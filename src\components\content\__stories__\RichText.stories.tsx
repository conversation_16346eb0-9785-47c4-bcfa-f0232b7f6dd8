/**
 * RichText Component Stories
 */

import type { Meta, StoryObj } from '@storybook/react';
import { RichText, RichTextLead, RichTextSmall, Markdown, HtmlContent } from '../RichText';

const meta: Meta<typeof RichText> = {
  title: 'Content/RichText',
  component: RichText,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'A flexible rich text component that supports markdown, HTML, and custom styling with typography variants and responsive design.',
      },
    },
  },
  argTypes: {
    content: {
      control: 'text',
      description: 'Content to render - can be HTML string, markdown, or React nodes',
    },
    variant: {
      control: 'select',
      options: ['body', 'lead', 'small', 'caption'],
      description: 'Typography variant',
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg', 'xl'],
      description: 'Text size (overrides variant)',
    },
    color: {
      control: 'select',
      options: ['default', 'muted', 'primary', 'secondary'],
      description: 'Text color',
    },
    markdown: {
      control: 'boolean',
      description: 'Whether to enable markdown parsing',
    },
    sanitize: {
      control: 'boolean',
      description: 'Whether to sanitize HTML content',
    },
    maxLines: {
      control: 'number',
      description: 'Maximum number of lines to show (truncation)',
    },
    expandable: {
      control: 'boolean',
      description: 'Whether to show read more/less toggle',
    },
  },
};

export default meta;
type Story = StoryObj<typeof RichText>;

// Sample content
const sampleText = `
This is a sample paragraph with some text content. It demonstrates how the RichText component handles regular text content with proper typography and spacing.

This is another paragraph to show how multiple paragraphs are handled with appropriate spacing between them.
`;

const sampleMarkdown = `
# Main Heading

This is a paragraph with **bold text** and *italic text*. You can also include [links](https://example.com) and \`inline code\`.

## Subheading

- List item one
- List item two
- List item three

> This is a blockquote with some important information that stands out from the regular text.

### Code Example

\`\`\`javascript
const example = "This is a code block";
console.log(example);
\`\`\`
`;

const sampleHTML = `
<h2>HTML Content Example</h2>
<p>This content contains <strong>HTML tags</strong> that will be rendered properly.</p>
<ul>
  <li>First item</li>
  <li>Second item with <em>emphasis</em></li>
  <li>Third item</li>
</ul>
<blockquote>
  <p>This is a blockquote with some important information.</p>
</blockquote>
`;

const longContent = `
Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.

Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.
`;

// Basic Stories
export const Default: Story = {
  args: {
    content: sampleText,
  },
};

export const WithMarkdown: Story = {
  args: {
    content: sampleMarkdown,
    markdown: true,
  },
};

export const WithHTML: Story = {
  args: {
    content: sampleHTML,
  },
};

// Typography Variants
export const LeadVariant: Story = {
  args: {
    content: "This is lead text that stands out with larger size and medium font weight.",
    variant: 'lead',
  },
};

export const SmallVariant: Story = {
  args: {
    content: "This is small text for captions, footnotes, or secondary information.",
    variant: 'small',
  },
};

export const CaptionVariant: Story = {
  args: {
    content: "This is caption text, even smaller for image captions or fine print.",
    variant: 'caption',
  },
};

// Size Overrides
export const SizeOverrides: Story = {
  render: () => (
    <div className="space-y-4">
      <RichText content="Small size text" size="sm" />
      <RichText content="Medium size text" size="md" />
      <RichText content="Large size text" size="lg" />
      <RichText content="Extra large size text" size="xl" />
    </div>
  ),
};

// Color Variants
export const ColorVariants: Story = {
  render: () => (
    <div className="space-y-4">
      <RichText content="Default color text" color="default" />
      <RichText content="Muted color text" color="muted" />
      <RichText content="Primary color text" color="primary" />
      <RichText content="Secondary color text" color="secondary" />
    </div>
  ),
};

// Expandable Content
export const ExpandableContent: Story = {
  args: {
    content: longContent,
    maxLines: 3,
    expandable: true,
  },
};

export const TruncatedContent: Story = {
  args: {
    content: longContent,
    maxLines: 2,
    expandable: false,
  },
};

// Custom Element Styles
export const CustomElementStyles: Story = {
  args: {
    content: sampleHTML,
    elementStyles: {
      h2: 'text-2xl font-bold text-blue-600 mb-4',
      p: 'text-gray-700 mb-3',
      ul: 'list-disc list-inside text-green-600 mb-4',
      blockquote: 'border-l-4 border-purple-500 pl-4 py-2 bg-purple-50 italic',
    },
  },
};

// Specialized Components
export const LeadComponent: Story = {
  render: () => (
    <RichTextLead content="This uses the specialized RichTextLead component for important introductory text." />
  ),
};

export const SmallComponent: Story = {
  render: () => (
    <RichTextSmall content="This uses the specialized RichTextSmall component for secondary information." />
  ),
};

export const MarkdownComponent: Story = {
  render: () => (
    <Markdown content="This uses the **Markdown** component with *automatic* markdown parsing enabled." />
  ),
};

export const HtmlContentComponent: Story = {
  render: () => (
    <HtmlContent content="<p>This uses the <strong>HtmlContent</strong> component with <em>sanitization</em> enabled.</p>" />
  ),
};

// Real Estate Content Examples
export const PropertyDescription: Story = {
  args: {
    content: `
# Luxury Villa in Canggu

This stunning **4-bedroom villa** offers the perfect blend of modern luxury and traditional Balinese charm. Located in the heart of Canggu, just *5 minutes* from the beach.

## Features

- Private swimming pool
- Fully equipped kitchen
- Air conditioning throughout
- High-speed WiFi
- Daily housekeeping included

> Perfect for digital nomads, families, or groups looking for an authentic Bali experience.

[Contact us](mailto:<EMAIL>) for availability and booking.
    `,
    markdown: true,
    variant: 'body',
  },
};

export const TestimonialQuote: Story = {
  args: {
    content: "The villa exceeded all our expectations. The location was perfect, the amenities were top-notch, and the staff was incredibly helpful. We'll definitely be back!",
    variant: 'lead',
    color: 'muted',
    className: 'italic',
  },
};

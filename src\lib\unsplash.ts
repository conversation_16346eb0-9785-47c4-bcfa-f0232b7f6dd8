/**
 * Unsplash API Integration for Real Estate Photography
 * Epic 6: UI Modernization - Professional photography integration
 */

export interface UnsplashPhoto {
  id: string;
  created_at: string;
  width: number;
  height: number;
  color: string;
  blur_hash: string;
  description: string | null;
  alt_description: string | null;
  urls: {
    raw: string;
    full: string;
    regular: string;
    small: string;
    thumb: string;
  };
  links: {
    self: string;
    html: string;
    download: string;
    download_location: string;
  };
  user: {
    id: string;
    username: string;
    name: string;
    profile_image: {
      small: string;
      medium: string;
      large: string;
    };
  };
}

export interface UnsplashSearchResponse {
  total: number;
  total_pages: number;
  results: UnsplashPhoto[];
}

// Fallback images for when Unsplash is unavailable
const FALLBACK_IMAGES = {
  villa: 'https://images.unsplash.com/photo-1613490493576-7fde63acd811?w=800&auto=format&fit=crop&q=80',
  guesthouse: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&auto=format&fit=crop&q=80',
  apartment: 'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&auto=format&fit=crop&q=80',
  land: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?w=800&auto=format&fit=crop&q=80',
  canggu: 'https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?w=800&auto=format&fit=crop&q=80',
  ubud: 'https://images.unsplash.com/photo-1518548419970-58e3b4079ab2?w=800&auto=format&fit=crop&q=80',
  seminyak: 'https://images.unsplash.com/photo-1540541338287-41700207dee6?w=800&auto=format&fit=crop&q=80',
  sanur: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=800&auto=format&fit=crop&q=80',
  jimbaran: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&auto=format&fit=crop&q=80',
  hero: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=1200&auto=format&fit=crop&q=80'
};

class UnsplashService {
  private readonly accessKey: string;
  private readonly baseUrl = 'https://api.unsplash.com';

  constructor() {
    this.accessKey = process.env.NEXT_PUBLIC_UNSPLASH_ACCESS_KEY || '';
  }

  private async fetchWithFallback<T>(
    url: string,
    fallbackKey: keyof typeof FALLBACK_IMAGES
  ): Promise<T | { urls: { regular: string } }> {
    if (!this.accessKey) {
      console.warn('Unsplash API key not configured, using fallback image');
      return { urls: { regular: FALLBACK_IMAGES[fallbackKey] } } as T;
    }

    try {
      const response = await fetch(url, {
        headers: {
          'Authorization': `Client-ID ${this.accessKey}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Unsplash API error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.warn('Unsplash API failed, using fallback:', error);
      return { urls: { regular: FALLBACK_IMAGES[fallbackKey] } } as T;
    }
  }

  async searchPhotos(query: string, count: number = 12): Promise<UnsplashPhoto[]> {
    const url = `${this.baseUrl}/search/photos?query=${encodeURIComponent(query)}&per_page=${count}&orientation=landscape`;
    const fallbackKey = query.toLowerCase().includes('villa') ? 'villa' : 
                       query.toLowerCase().includes('apartment') ? 'apartment' :
                       query.toLowerCase().includes('land') ? 'land' : 'villa';

    const response = await this.fetchWithFallback<UnsplashSearchResponse>(url, fallbackKey);
    
    if ('results' in response) {
      return response.results;
    }
    
    // Return fallback as single photo
    return [{
      id: 'fallback',
      created_at: new Date().toISOString(),
      width: 800,
      height: 600,
      color: '#60544D',
      blur_hash: 'LFC$yHwc8^$yIAS$%M%00KxukYIp',
      description: `Beautiful ${query} in Bali`,
      alt_description: `${query} property`,
      urls: response.urls,
      links: {
        self: '',
        html: '',
        download: '',
        download_location: ''
      },
      user: {
        id: 'unsplash',
        username: 'unsplash',
        name: 'Unsplash',
        profile_image: {
          small: '',
          medium: '',
          large: ''
        }
      }
    }] as UnsplashPhoto[];
  }

  async getRandomPhoto(query: string): Promise<UnsplashPhoto> {
    const url = `${this.baseUrl}/photos/random?query=${encodeURIComponent(query)}&orientation=landscape`;
    const fallbackKey = query.toLowerCase().includes('villa') ? 'villa' : 
                       query.toLowerCase().includes('apartment') ? 'apartment' :
                       query.toLowerCase().includes('land') ? 'land' : 'hero';

    const response = await this.fetchWithFallback<UnsplashPhoto>(url, fallbackKey);
    
    if ('id' in response) {
      return response;
    }

    // Return fallback as photo object
    return {
      id: 'fallback',
      created_at: new Date().toISOString(),
      width: 1200,
      height: 800,
      color: '#60544D',
      blur_hash: 'LFC$yHwc8^$yIAS$%M%00KxukYIp',
      description: `Beautiful ${query} in Bali`,
      alt_description: `${query} property`,
      urls: response.urls,
      links: {
        self: '',
        html: '',
        download: '',
        download_location: ''
      },
      user: {
        id: 'unsplash',
        username: 'unsplash',
        name: 'Unsplash',
        profile_image: {
          small: '',
          medium: '',
          large: ''
        }
      }
    } as UnsplashPhoto;
  }

  // Get location-specific photos
  async getLocationPhotos(location: string): Promise<UnsplashPhoto[]> {
    const queries = [
      `${location} bali villa`,
      `${location} bali property`,
      `${location} bali architecture`,
      `bali ${location} landscape`
    ];

    const photos: UnsplashPhoto[] = [];
    
    for (const query of queries) {
      const results = await this.searchPhotos(query, 3);
      photos.push(...results);
    }

    return photos.slice(0, 12); // Return max 12 photos
  }

  // Get property type specific photos
  async getPropertyTypePhotos(propertyType: string): Promise<UnsplashPhoto[]> {
    const queries = [
      `bali ${propertyType}`,
      `tropical ${propertyType}`,
      `luxury ${propertyType} bali`,
      `modern ${propertyType} indonesia`
    ];

    const photos: UnsplashPhoto[] = [];
    
    for (const query of queries) {
      const results = await this.searchPhotos(query, 3);
      photos.push(...results);
    }

    return photos.slice(0, 12);
  }

  // Optimize image URL for specific dimensions
  optimizeImageUrl(photo: UnsplashPhoto, width: number, height?: number): string {
    const baseUrl = photo.urls.raw;
    const params = new URLSearchParams({
      'auto': 'format',
      'fit': 'crop',
      'w': width.toString(),
      'q': '80'
    });

    if (height) {
      params.set('h', height.toString());
    }

    return `${baseUrl}&${params.toString()}`;
  }
}

export const unsplashService = new UnsplashService();

// Utility functions for common use cases
export const getHeroImage = () => unsplashService.getRandomPhoto('bali luxury villa tropical');
export const getLocationImage = (location: string) => unsplashService.getRandomPhoto(`${location} bali`);
export const getPropertyImage = (type: string) => unsplashService.getRandomPhoto(`bali ${type}`);

export default unsplashService;

'use client'

import { useState, useEffect, useCallback } from 'react'

interface SessionManager {
  sessionId: string | null
  isExpired: boolean
  timeRemaining: number
  
  // Actions
  createSession: () => string
  clearSession: () => void
  refreshSession: () => void
  checkExpiration: () => boolean
}

const SESSION_DURATION = 24 * 60 * 60 * 1000 // 24 hours in milliseconds
const SESSION_KEY = 'ai_session_id'
const SESSION_TIMESTAMP_KEY = 'ai_session_timestamp'

export const useSessionManager = (): SessionManager => {
  const [sessionId, setSessionId] = useState<string | null>(null)
  const [isExpired, setIsExpired] = useState(false)
  const [timeRemaining, setTimeRemaining] = useState(0)

  // Generate new session ID - use crypto for better randomness and avoid hydration issues
  const generateSessionId = () => {
    if (typeof window === 'undefined') {
      // Server-side fallback
      return `session_server_${Math.floor(Math.random() * 1000000)}`
    }

    // Client-side with crypto API
    const timestamp = Date.now()
    const randomBytes = crypto.getRandomValues(new Uint8Array(4))
    const randomString = Array.from(randomBytes, byte => byte.toString(36)).join('')
    return `session_${timestamp}_${randomString}`
  }

  // Create new session
  const createSession = useCallback(() => {
    const newSessionId = generateSessionId()
    const timestamp = Date.now()
    
    localStorage.setItem(SESSION_KEY, newSessionId)
    localStorage.setItem(SESSION_TIMESTAMP_KEY, timestamp.toString())
    
    setSessionId(newSessionId)
    setIsExpired(false)
    setTimeRemaining(SESSION_DURATION)
    
    return newSessionId
  }, [])

  // Clear session
  const clearSession = useCallback(() => {
    localStorage.removeItem(SESSION_KEY)
    localStorage.removeItem(SESSION_TIMESTAMP_KEY)
    
    setSessionId(null)
    setIsExpired(false)
    setTimeRemaining(0)
  }, [])

  // Refresh session (extend expiration)
  const refreshSession = useCallback(() => {
    if (sessionId) {
      const timestamp = Date.now()
      localStorage.setItem(SESSION_TIMESTAMP_KEY, timestamp.toString())
      setTimeRemaining(SESSION_DURATION)
      setIsExpired(false)
    }
  }, [sessionId])

  // Check if session is expired
  const checkExpiration = useCallback(() => {
    const savedSessionId = localStorage.getItem(SESSION_KEY)
    const savedTimestamp = localStorage.getItem(SESSION_TIMESTAMP_KEY)
    
    if (!savedSessionId || !savedTimestamp) {
      return true
    }
    
    const timestamp = parseInt(savedTimestamp, 10)
    const now = Date.now()
    const elapsed = now - timestamp
    
    return elapsed >= SESSION_DURATION
  }, [])

  // Load session from localStorage
  const loadSession = useCallback(() => {
    const savedSessionId = localStorage.getItem(SESSION_KEY)
    const savedTimestamp = localStorage.getItem(SESSION_TIMESTAMP_KEY)
    
    if (!savedSessionId || !savedTimestamp) {
      return
    }
    
    const timestamp = parseInt(savedTimestamp, 10)
    const now = Date.now()
    const elapsed = now - timestamp
    const remaining = SESSION_DURATION - elapsed
    
    if (remaining <= 0) {
      // Session expired
      clearSession()
      setIsExpired(true)
    } else {
      // Session still valid
      setSessionId(savedSessionId)
      setTimeRemaining(remaining)
      setIsExpired(false)
    }
  }, [clearSession])

  // Update time remaining every minute
  useEffect(() => {
    const interval = setInterval(() => {
      const savedTimestamp = localStorage.getItem(SESSION_TIMESTAMP_KEY)
      
      if (savedTimestamp) {
        const timestamp = parseInt(savedTimestamp, 10)
        const now = Date.now()
        const elapsed = now - timestamp
        const remaining = SESSION_DURATION - elapsed
        
        if (remaining <= 0) {
          clearSession()
          setIsExpired(true)
        } else {
          setTimeRemaining(remaining)
        }
      }
    }, 60000) // Check every minute
    
    return () => clearInterval(interval)
  }, [clearSession])

  // Load session on mount
  useEffect(() => {
    loadSession()
  }, [loadSession])

  // Auto-cleanup expired sessions
  useEffect(() => {
    const cleanup = () => {
      if (checkExpiration()) {
        clearSession()
        setIsExpired(true)
      }
    }
    
    // Check on page focus
    window.addEventListener('focus', cleanup)
    
    return () => {
      window.removeEventListener('focus', cleanup)
    }
  }, [checkExpiration, clearSession])

  return {
    sessionId,
    isExpired,
    timeRemaining,
    createSession,
    clearSession,
    refreshSession,
    checkExpiration,
  }
}

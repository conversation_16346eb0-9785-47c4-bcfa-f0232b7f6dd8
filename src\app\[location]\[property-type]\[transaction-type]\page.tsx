import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { getLocationBySlug, getPropertyTypes } from '@/lib/payload'
import LocationHero from '@/components/location/LocationHero'
import NeighborhoodOverview from '@/components/location/NeighborhoodOverview'
import PropertyTypesAvailable from '@/components/location/PropertyTypesAvailable'
import PriceRanges from '@/components/location/PriceRanges'
import LifestyleInformation from '@/components/location/LifestyleInformation'
import ExpatCommunity from '@/components/location/ExpatCommunity'
import Transportation from '@/components/location/Transportation'
import RegulatoryInformation from '@/components/location/RegulatoryInformation'
import LocationFAQ from '@/components/location/LocationFAQ'
import ContactIntegration from '@/components/location/ContactIntegration'

// Valid combinations based on PRD - Updated with all 5 locations
const validCombinations = {
  'canggu': {
    'villa': ['long-term-rental', 'for-sale-freehold'],
    'guesthouse': ['long-term-rental']
  },
  'ubud': {
    'villa': ['long-term-rental', 'for-sale-freehold'],
    'guesthouse': ['long-term-rental']
  },
  'seminyak': {
    'villa': ['long-term-rental', 'for-sale-freehold'],
    'guesthouse': ['long-term-rental']
  },
  'sanur': {
    'villa': ['long-term-rental', 'for-sale-freehold'],
    'guesthouse': ['long-term-rental']
  },
  'jimbaran': {
    'villa': ['long-term-rental', 'for-sale-freehold'],
    'guesthouse': ['long-term-rental']
  }
}



interface PageProps {
  params: Promise<{
    location: string
    'property-type': string
    'transaction-type': string
  }>
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { location, 'property-type': propertyType, 'transaction-type': transactionType } = await params

  // Validate combination
  if (!validCombinations[location as keyof typeof validCombinations]?.[propertyType as keyof typeof validCombinations[keyof typeof validCombinations]]?.includes(transactionType)) {
    return {
      title: 'Page Not Found | Bali Real Estate',
      description: 'The requested page could not be found.'
    }
  }

  // Get location data from CMS
  const locationInfo = await getLocationBySlug(location)

  if (!locationInfo) {
    return {
      title: 'Location Not Found | Bali Real Estate',
      description: 'The requested location could not be found.'
    }
  }

  // Enhanced SEO metadata generation
  const locationName = locationInfo.name || location.charAt(0).toUpperCase() + location.slice(1)
  const propertyTypeFormatted = propertyType.charAt(0).toUpperCase() + propertyType.slice(1)
  const transactionFormatted = transactionType === 'long-term-rental' ? 'Long-term Rental' :
                              transactionType === 'for-sale-freehold' ? 'For Sale (Freehold)' :
                              transactionType.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')

  // Priority-specific SEO optimization
  const isHighPriority = (
    (location === 'canggu' && propertyType === 'villa' && (transactionType === 'long-term-rental' || transactionType === 'for-sale-freehold')) ||
    (location === 'ubud' && propertyType === 'villa' && (transactionType === 'long-term-rental' || transactionType === 'for-sale-freehold')) ||
    (location === 'seminyak' && propertyType === 'villa' && transactionType === 'long-term-rental')
  )

  // High-priority SEO titles and descriptions
  let title: string
  let description: string
  let keywords: string

  if (isHighPriority) {
    // Optimized titles for high-priority pages
    if (location === 'canggu' && propertyType === 'villa' && transactionType === 'long-term-rental') {
      title = `Rent Villa in Canggu | Long Term Rental Canggu | Canggu Villa Rental 2025`
      description = `Rent villa in Canggu from $1,500/month. Long term rental Canggu with beachfront access, rice field views. Premium villas for digital nomads & expats. Schedule villa viewing Bali today.`
      keywords = `rent villa in canggu, long term rental canggu, canggu villa rental, villa rent canggu, canggu property investment, schedule villa viewing bali, bali expat housing`
    } else if (location === 'ubud' && propertyType === 'villa' && transactionType === 'long-term-rental') {
      title = `Rent Villa in Ubud | Long Term Rental Ubud | Bali Digital Nomad Community`
      description = `Rent villa in Ubud from $1,200/month. Long term rental Ubud with jungle views, rice fields. Perfect for bali digital nomad community & wellness seekers. Bali coworking spaces nearby.`
      keywords = `rent villa in ubud, long term rental ubud, bali digital nomad community, bali coworking spaces, ubud villa rental, jungle villa ubud, wellness retreat ubud`
    } else if (location === 'seminyak' && propertyType === 'villa' && transactionType === 'long-term-rental') {
      title = `Rent Villa in Seminyak | Beach Villa Bali for Rent | Luxury Villa Bali for Sale`
      description = `Rent villa in Seminyak from $2,500/month. Beach villa bali for rent with luxury amenities. Premium location near beach. Luxury villa bali for sale options available.`
      keywords = `rent villa in seminyak, beach villa bali for rent, luxury villa bali for sale, seminyak villa rental, luxury villa seminyak, beach villa seminyak`
    } else if (location === 'canggu' && propertyType === 'villa' && transactionType === 'for-sale-freehold') {
      title = `Buy Villa in Bali | Canggu Property Investment | Investment Property Bali`
      description = `Buy villa in bali Canggu from $350K freehold. Canggu property investment with high yields. Investment property bali for foreigners. Schedule villa viewing bali today.`
      keywords = `buy villa in bali, canggu property investment, investment property bali, buy villa canggu, canggu villa for sale, schedule villa viewing bali`
    } else if (location === 'ubud' && propertyType === 'villa' && transactionType === 'for-sale-freehold') {
      title = `Buy Villa in Bali Ubud | Buying Land in Bali | Bali Investment Hotspots`
      description = `Buy villa in bali Ubud from $280K freehold. Buying land in bali with jungle views. Bali investment hotspots for wellness tourism. Invest in bali property today.`
      keywords = `buy villa in bali, buying land in bali, bali investment hotspots, invest in bali property, ubud villa for sale, jungle villa for sale`
    } else {
      // Fallback for other combinations
      title = `${locationName} ${propertyTypeFormatted} ${transactionFormatted} | Bali Real Estate`
      description = `Find the perfect ${propertyType} for ${transactionType.replace('-', ' ')} in ${locationName}, Bali. Expert guidance for expats, digital nomads, and investors.`
      keywords = `${locationName} ${propertyType}, ${locationName} ${transactionType.replace('-', ' ')}, Bali ${propertyType}, ${locationName} property`
    }
  } else {
    // Standard SEO for non-priority pages
    const propertyTypeFormatted = propertyType.charAt(0).toUpperCase() + propertyType.slice(1)
    const transactionFormatted = transactionType === 'long-term-rental' ? 'Long-term Rental' :
                                transactionType === 'for-sale-freehold' ? 'For Sale Freehold' :
                                'For Sale Leasehold'

    title = `${locationName} ${propertyTypeFormatted} ${transactionFormatted} | Bali Real Estate`
    description = `Find your perfect ${propertyType} ${transactionType.replace('-', ' ')} in ${locationName}, Bali. ${locationInfo.description || 'Expert guidance for expats, digital nomads, and investors.'}`
    keywords = `${location} ${propertyType}, ${location} ${transactionType}, Bali real estate, ${location} property, expat housing Bali`
  }

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      locale: 'en_US',
      url: `https://bali-real-estate.com/${location}/${propertyType}/${transactionType}`,
      siteName: 'Bali Real Estate',
      images: [
        {
          url: locationInfo.featuredImage?.url || '/images/og-image.jpg',
          width: 1200,
          height: 630,
          alt: `${locationInfo.name} ${propertyType} ${transactionType}`
        }
      ]
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [locationInfo.featuredImage?.url || '/images/og-image.jpg'],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    alternates: {
      canonical: `https://bali-real-estate.com/${location}/${propertyType}/${transactionType}`,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [locationInfo.featuredImage?.url || '/images/twitter-image.jpg']
    },
    alternates: {
      canonical: `https://bali-real-estate.com/${location}/${propertyType}/${transactionType}`
    }
  }
}

export default async function LocationPropertyPage({ params }: PageProps) {
  const { location, 'property-type': propertyType, 'transaction-type': transactionType } = await params

  // Validate combination exists
  if (!validCombinations[location as keyof typeof validCombinations]?.[propertyType as keyof typeof validCombinations[keyof typeof validCombinations]]?.includes(transactionType)) {
    notFound()
  }

  // Get location data from CMS
  const locationInfo = await getLocationBySlug(location)

  // For locations not found in CMS, show a coming soon page
  if (!locationInfo) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {location.charAt(0).toUpperCase() + location.slice(1)} Coming Soon
          </h1>
          <p className="text-gray-600 mb-6">
            We&apos;re working on detailed information for {location.charAt(0).toUpperCase() + location.slice(1)}.
            Check back soon for comprehensive property listings and local insights.
          </p>
          <a
            href="/"
            className="inline-flex items-center bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
          >
            ← Back to Home
          </a>
        </div>
      </div>
    )
  }

  // Calculate price range from CMS data
  const priceRange = locationInfo.priceRange ? {
    min: transactionType === 'long-term-rental' ? locationInfo.priceRange.rentalMin : locationInfo.priceRange.saleMin,
    max: transactionType === 'long-term-rental' ? locationInfo.priceRange.rentalMax : locationInfo.priceRange.saleMax,
    currency: 'USD',
    period: transactionType === 'long-term-rental' ? 'month' : 'purchase'
  } : null

  return (
    <main className="min-h-screen">
      {/* Enhanced Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": ["RealEstateListing", "Place"],
            "name": `${locationInfo.name} ${propertyType.charAt(0).toUpperCase() + propertyType.slice(1)} ${transactionType === 'long-term-rental' ? 'Long-term Rental' : 'For Sale Freehold'}`,
            "description": locationInfo.description,
            "url": `https://bali-real-estate.com/${location}/${propertyType}/${transactionType}`,
            "address": {
              "@type": "PostalAddress",
              "addressLocality": locationInfo.name,
              "addressRegion": "Bali",
              "addressCountry": "Indonesia"
            },
            "geo": locationInfo.coordinates ? {
              "@type": "GeoCoordinates",
              "latitude": locationInfo.coordinates.lat,
              "longitude": locationInfo.coordinates.lng
            } : undefined,
            "offers": priceRange ? {
              "@type": "Offer",
              "price": `${priceRange.min}-${priceRange.max}`,
              "priceCurrency": priceRange.currency,
              "availability": "https://schema.org/InStock",
              "priceValidUntil": new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 90 days from now
            } : undefined,
            "amenityFeature": locationInfo.amenities?.map((amenity: string) => ({
              "@type": "LocationFeatureSpecification",
              "name": amenity
            })),
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "4.5",
              "reviewCount": "127",
              "bestRating": "5",
              "worstRating": "1"
            }
          })
        }}
      />

      {/* Hero Section */}
      <LocationHero 
        location={location}
        propertyType={propertyType}
        transactionType={transactionType}
        locationInfo={locationInfo}
      />
      
      {/* Neighborhood Overview Section (500+ words) */}
      <NeighborhoodOverview 
        location={location}
        locationInfo={locationInfo}
      />
      
      {/* Property Types Available Section */}
      <PropertyTypesAvailable 
        location={location}
        propertyType={propertyType}
        locationInfo={locationInfo}
      />
      
      {/* Price Ranges Section */}
      <PriceRanges 
        location={location}
        propertyType={propertyType}
        transactionType={transactionType}
        priceRange={priceRange}
      />
      
      {/* Lifestyle Information Section (400+ words) */}
      <LifestyleInformation 
        location={location}
        locationInfo={locationInfo}
      />
      
      {/* Expat Community Section */}
      <ExpatCommunity 
        location={location}
        locationInfo={locationInfo}
      />
      
      {/* Transportation & Accessibility */}
      <Transportation 
        location={location}
        locationInfo={locationInfo}
      />
      
      {/* Regulatory Information Section */}
      <RegulatoryInformation 
        location={location}
        propertyType={propertyType}
        transactionType={transactionType}
      />
      
      {/* FAQ Section (8-10 questions) */}
      <LocationFAQ 
        location={location}
        propertyType={propertyType}
        transactionType={transactionType}
      />
      
      {/* Contact Integration */}
      <ContactIntegration 
        location={location}
        propertyType={propertyType}
        transactionType={transactionType}
      />
    </main>
  )
}

// Generate static params for known combinations
export async function generateStaticParams() {
  const params = []
  
  for (const [location, propertyTypes] of Object.entries(validCombinations)) {
    for (const [propertyType, transactionTypes] of Object.entries(propertyTypes)) {
      for (const transactionType of transactionTypes) {
        params.push({
          location,
          'property-type': propertyType,
          'transaction-type': transactionType
        })
      }
    }
  }
  
  return params
}

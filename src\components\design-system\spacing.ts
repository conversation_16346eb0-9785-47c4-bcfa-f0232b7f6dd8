/**
 * Design System - Spacing
 * Bali Property Scout Website
 *
 * This file defines the complete spacing system including
 * margins, padding, gaps, and layout dimensions.
 */

// Base spacing unit (4px)
const BASE_UNIT = 4;

// Spacing scale (based on 4px grid)
export const spacing = {
  0: '0px',
  px: '1px',
  0.5: `${BASE_UNIT * 0.5}px`,   // 2px
  1: `${BASE_UNIT * 1}px`,       // 4px
  1.5: `${BASE_UNIT * 1.5}px`,   // 6px
  2: `${BASE_UNIT * 2}px`,       // 8px
  2.5: `${BASE_UNIT * 2.5}px`,   // 10px
  3: `${BASE_UNIT * 3}px`,       // 12px
  3.5: `${BASE_UNIT * 3.5}px`,   // 14px
  4: `${BASE_UNIT * 4}px`,       // 16px
  5: `${BASE_UNIT * 5}px`,       // 20px
  6: `${BASE_UNIT * 6}px`,       // 24px
  7: `${BASE_UNIT * 7}px`,       // 28px
  8: `${BASE_UNIT * 8}px`,       // 32px
  9: `${BASE_UNIT * 9}px`,       // 36px
  10: `${BASE_UNIT * 10}px`,     // 40px
  11: `${BASE_UNIT * 11}px`,     // 44px
  12: `${BASE_UNIT * 12}px`,     // 48px
  14: `${BASE_UNIT * 14}px`,     // 56px
  16: `${BASE_UNIT * 16}px`,     // 64px
  20: `${BASE_UNIT * 20}px`,     // 80px
  24: `${BASE_UNIT * 24}px`,     // 96px
  28: `${BASE_UNIT * 28}px`,     // 112px
  32: `${BASE_UNIT * 32}px`,     // 128px
  36: `${BASE_UNIT * 36}px`,     // 144px
  40: `${BASE_UNIT * 40}px`,     // 160px
  44: `${BASE_UNIT * 44}px`,     // 176px
  48: `${BASE_UNIT * 48}px`,     // 192px
  52: `${BASE_UNIT * 52}px`,     // 208px
  56: `${BASE_UNIT * 56}px`,     // 224px
  60: `${BASE_UNIT * 60}px`,     // 240px
  64: `${BASE_UNIT * 64}px`,     // 256px
  72: `${BASE_UNIT * 72}px`,     // 288px
  80: `${BASE_UNIT * 80}px`,     // 320px
  96: `${BASE_UNIT * 96}px`,     // 384px
} as const;

// Semantic spacing tokens
export const semanticSpacing = {
  // Component internal spacing
  component: {
    xs: spacing[1],      // 4px - very tight spacing
    sm: spacing[2],      // 8px - tight spacing
    md: spacing[4],      // 16px - default spacing
    lg: spacing[6],      // 24px - loose spacing
    xl: spacing[8],      // 32px - very loose spacing
  },

  // Layout spacing
  layout: {
    xs: spacing[4],      // 16px - minimal section spacing
    sm: spacing[8],      // 32px - small section spacing
    md: spacing[12],     // 48px - default section spacing
    lg: spacing[16],     // 64px - large section spacing
    xl: spacing[24],     // 96px - extra large section spacing
    '2xl': spacing[32],  // 128px - huge section spacing
  },

  // Container spacing
  container: {
    xs: spacing[4],      // 16px - mobile padding
    sm: spacing[6],      // 24px - small screen padding
    md: spacing[8],      // 32px - medium screen padding
    lg: spacing[12],     // 48px - large screen padding
    xl: spacing[16],     // 64px - extra large screen padding
  },

  // Form spacing
  form: {
    fieldGap: spacing[4],     // 16px - gap between form fields
    labelGap: spacing[2],     // 8px - gap between label and input
    buttonGap: spacing[3],    // 12px - gap between buttons
    sectionGap: spacing[6],   // 24px - gap between form sections
  },

  // Card spacing
  card: {
    padding: {
      sm: spacing[4],    // 16px - small card padding
      md: spacing[6],    // 24px - medium card padding
      lg: spacing[8],    // 32px - large card padding
    },
    gap: {
      sm: spacing[4],    // 16px - small gap between cards
      md: spacing[6],    // 24px - medium gap between cards
      lg: spacing[8],    // 32px - large gap between cards
    },
  },

  // Navigation spacing
  navigation: {
    itemGap: spacing[6],      // 24px - gap between nav items
    dropdownGap: spacing[2],  // 8px - gap in dropdown menus
    padding: spacing[4],      // 16px - nav item padding
  },
} as const;

// Breakpoint-specific spacing
export const responsiveSpacing = {
  mobile: {
    container: spacing[4],    // 16px
    section: spacing[8],      // 32px
    component: spacing[4],    // 16px
  },
  tablet: {
    container: spacing[6],    // 24px
    section: spacing[12],     // 48px
    component: spacing[6],    // 24px
  },
  desktop: {
    container: spacing[8],    // 32px
    section: spacing[16],     // 64px
    component: spacing[8],    // 32px
  },
  wide: {
    container: spacing[12],   // 48px
    section: spacing[24],     // 96px
    component: spacing[8],    // 32px
  },
} as const;

// Layout dimensions
export const dimensions = {
  // Container max widths
  container: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
    full: '100%',
  },

  // Component dimensions
  component: {
    // Button heights
    button: {
      sm: spacing[8],     // 32px
      md: spacing[10],    // 40px
      lg: spacing[12],    // 48px
      xl: spacing[14],    // 56px
    },

    // Input heights
    input: {
      sm: spacing[8],     // 32px
      md: spacing[10],    // 40px
      lg: spacing[12],    // 48px
    },

    // Card dimensions
    card: {
      minHeight: spacing[24],  // 96px
      maxWidth: spacing[96],   // 384px
    },

    // Modal dimensions
    modal: {
      sm: '320px',
      md: '480px',
      lg: '640px',
      xl: '768px',
      full: '100%',
    },
  },

  // Touch targets (accessibility)
  touch: {
    minimum: spacing[11],     // 44px - minimum touch target size
    comfortable: spacing[12], // 48px - comfortable touch target size
  },
} as const;

// Border radius scale
export const borderRadius = {
  none: '0px',
  sm: '2px',
  DEFAULT: '4px',
  md: '6px',
  lg: '8px',
  xl: '12px',
  '2xl': '16px',
  '3xl': '24px',
  full: '9999px',
} as const;

// Shadow scale
export const shadows = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  DEFAULT: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
  inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
  none: '0 0 #0000',
} as const;

// Z-index scale
export const zIndex = {
  auto: 'auto',
  0: '0',
  10: '10',
  20: '20',
  30: '30',
  40: '40',
  50: '50',
  dropdown: '1000',
  sticky: '1020',
  fixed: '1030',
  modal: '1040',
  popover: '1050',
  tooltip: '1060',
  toast: '1070',
} as const;

// Spacing utility functions
export const spacingUtils = {
  /**
   * Get spacing value by key
   */
  getSpacing: (key: keyof typeof spacing) => spacing[key],

  /**
   * Get semantic spacing value
   */
  getSemanticSpacing: (category: keyof typeof semanticSpacing, size: string) => {
    const categorySpacing = semanticSpacing[category];
    return categorySpacing[size as keyof typeof categorySpacing] || spacing[4];
  },

  /**
   * Get responsive spacing for breakpoint
   */
  getResponsiveSpacing: (breakpoint: keyof typeof responsiveSpacing, type: string) => {
    const breakpointSpacing = responsiveSpacing[breakpoint];
    return breakpointSpacing[type as keyof typeof breakpointSpacing] || spacing[4];
  },

  /**
   * Convert spacing to CSS custom properties
   */
  toCSSCustomProperties: () => {
    const cssVars: Record<string, string> = {};
    Object.entries(spacing).forEach(([key, value]) => {
      cssVars[`--spacing-${key}`] = value;
    });
    return cssVars;
  },
} as const;

// Export for Tailwind CSS configuration
export const tailwindSpacing = {
  spacing,
  borderRadius,
  boxShadow: shadows,
  zIndex,
  maxWidth: dimensions.container,
} as const;

export type SpacingKey = keyof typeof spacing;
export type SemanticSpacingCategory = keyof typeof semanticSpacing;
export type ResponsiveBreakpoint = keyof typeof responsiveSpacing;

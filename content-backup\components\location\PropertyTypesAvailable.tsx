interface PropertyTypesAvailableProps {
  location: string
  propertyType: string
  locationInfo: {
    name: string
  }
}

const PropertyTypesAvailable = ({ propertyType, locationInfo }: PropertyTypesAvailableProps) => {
  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-6xl mx-auto px-4">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">
          {propertyType.charAt(0).toUpperCase() + propertyType.slice(1)} Types Available in {locationInfo.name}
        </h2>
        <p className="text-gray-600">
          Detailed information about {propertyType} options in {locationInfo.name} will be displayed here.
        </p>
      </div>
    </section>
  )
}

export default PropertyTypesAvailable

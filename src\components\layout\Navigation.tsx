/**
 * Navigation Component
 * 
 * Reusable navigation component that can be used in different contexts.
 * Supports both horizontal and vertical layouts with responsive design.
 */

'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';

export interface NavigationItem {
  label: string;
  href: string;
  children?: NavigationItem[];
  icon?: React.ReactNode;
  badge?: string;
  disabled?: boolean;
}

export interface NavigationProps {
  /** Navigation items */
  items: NavigationItem[];
  /** Navigation orientation */
  orientation?: 'horizontal' | 'vertical';
  /** Navigation size */
  size?: 'sm' | 'md' | 'lg';
  /** Custom className */
  className?: string;
  /** Whether to show icons */
  showIcons?: boolean;
  /** Whether to show badges */
  showBadges?: boolean;
  /** Callback when item is clicked */
  onItemClick?: (item: NavigationItem) => void;
}

// Navigation size styles
const navigationSizes = {
  sm: 'text-sm px-2 py-1',
  md: 'text-sm px-3 py-2',
  lg: 'text-base px-4 py-3',
};

export const Navigation: React.FC<NavigationProps> = ({
  items,
  orientation = 'horizontal',
  size = 'md',
  className,
  showIcons = false,
  showBadges = false,
  onItemClick,
}) => {
  const pathname = usePathname();

  // Check if link is active
  const isActiveLink = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  // Handle item click
  const handleItemClick = (item: NavigationItem) => {
    if (onItemClick) {
      onItemClick(item);
    }
  };

  // Render navigation item
  const renderNavigationItem = (item: NavigationItem, level: number = 0) => {
    const isActive = isActiveLink(item.href);
    const hasChildren = item.children && item.children.length > 0;

    return (
      <div key={item.href} className={cn(level > 0 && 'ml-4')}>
        <Link
          href={item.href}
          className={cn(
            // Base styles
            'flex items-center gap-2 font-medium transition-colors rounded-md',
            'hover:bg-neutral-100 focus:outline-none focus:ring-2 focus:ring-primary-500',
            
            // Size styles
            navigationSizes[size],
            
            // Active styles
            isActive
              ? 'text-primary bg-primary-lightest'
              : 'text-neutral-700 hover:text-primary',
            
            // Disabled styles
            item.disabled && 'opacity-50 cursor-not-allowed pointer-events-none'
          )}
          onClick={() => handleItemClick(item)}
        >
          {/* Icon */}
          {showIcons && item.icon && (
            <span className="flex-shrink-0">
              {item.icon}
            </span>
          )}
          
          {/* Label */}
          <span className="flex-1">{item.label}</span>
          
          {/* Badge */}
          {showBadges && item.badge && (
            <span className="px-2 py-0.5 text-xs font-medium bg-primary-light text-primary-foreground rounded-full">
              {item.badge}
            </span>
          )}
          
          {/* Dropdown indicator */}
          {hasChildren && (
            <svg
              className="w-4 h-4 flex-shrink-0"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          )}
        </Link>
        
        {/* Children */}
        {hasChildren && (
          <div className="mt-1 space-y-1">
            {item.children!.map((child) => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <nav
      className={cn(
        'flex',
        orientation === 'horizontal' ? 'flex-row space-x-1' : 'flex-col space-y-1',
        className
      )}
    >
      {items.map((item) => renderNavigationItem(item))}
    </nav>
  );
};

// Breadcrumbs component
export interface BreadcrumbItem {
  label: string;
  href?: string;
}

export interface BreadcrumbsProps {
  /** Breadcrumb items */
  items: BreadcrumbItem[];
  /** Custom className */
  className?: string;
  /** Separator between items */
  separator?: React.ReactNode;
}

export const Breadcrumbs: React.FC<BreadcrumbsProps> = ({
  items,
  className,
  separator = (
    <svg className="w-4 h-4 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
    </svg>
  ),
}) => {
  return (
    <nav className={cn('flex items-center space-x-2 text-sm', className)} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2">
        {items.map((item, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && (
              <span className="mx-2 flex-shrink-0">
                {separator}
              </span>
            )}
            
            {item.href ? (
              <Link
                href={item.href}
                className="text-neutral-600 hover:text-primary transition-colors"
              >
                {item.label}
              </Link>
            ) : (
              <span className="text-neutral-900 font-medium">
                {item.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

// Sidebar Navigation component
export interface SidebarNavigationProps extends NavigationProps {
  /** Whether sidebar is collapsed */
  collapsed?: boolean;
  /** Sidebar header content */
  header?: React.ReactNode;
  /** Sidebar footer content */
  footer?: React.ReactNode;
}

export const SidebarNavigation: React.FC<SidebarNavigationProps> = ({
  items,
  collapsed = false,
  header,
  footer,
  className,
  ...navigationProps
}) => {
  return (
    <div
      className={cn(
        'flex flex-col h-full bg-white border-r border-neutral-200',
        collapsed ? 'w-16' : 'w-64',
        'transition-all duration-300',
        className
      )}
    >
      {/* Header */}
      {header && (
        <div className="flex-shrink-0 p-4 border-b border-neutral-200">
          {header}
        </div>
      )}
      
      {/* Navigation */}
      <div className="flex-1 overflow-y-auto p-4">
        <Navigation
          items={items}
          orientation="vertical"
          showIcons={true}
          showBadges={!collapsed}
          {...navigationProps}
        />
      </div>
      
      {/* Footer */}
      {footer && (
        <div className="flex-shrink-0 p-4 border-t border-neutral-200">
          {footer}
        </div>
      )}
    </div>
  );
};

// Tab Navigation component
export interface TabItem {
  id: string;
  label: string;
  content?: React.ReactNode;
  disabled?: boolean;
  badge?: string;
}

export interface TabNavigationProps {
  /** Tab items */
  items: TabItem[];
  /** Active tab ID */
  activeTab: string;
  /** Callback when tab changes */
  onTabChange: (tabId: string) => void;
  /** Tab variant */
  variant?: 'default' | 'pills' | 'underline';
  /** Custom className */
  className?: string;
}

export const TabNavigation: React.FC<TabNavigationProps> = ({
  items,
  activeTab,
  onTabChange,
  variant = 'default',
  className,
}) => {
  const variantStyles = {
    default: {
      container: 'border-b border-neutral-200',
      tab: 'px-4 py-2 border-b-2 font-medium text-sm transition-colors',
      active: 'border-primary text-primary',
      inactive: 'border-transparent text-neutral-600 hover:text-neutral-900 hover:border-neutral-300',
    },
    pills: {
      container: 'bg-neutral-100 p-1 rounded-lg',
      tab: 'px-4 py-2 rounded-md font-medium text-sm transition-colors',
      active: 'bg-white text-primary shadow-sm',
      inactive: 'text-neutral-600 hover:text-neutral-900',
    },
    underline: {
      container: '',
      tab: 'px-4 py-2 font-medium text-sm transition-colors relative',
      active: 'text-primary after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-primary',
      inactive: 'text-neutral-600 hover:text-neutral-900',
    },
  };

  const styles = variantStyles[variant];

  return (
    <div className={cn(styles.container, className)}>
      <nav className="flex space-x-1">
        {items.map((item) => (
          <button
            key={item.id}
            onClick={() => !item.disabled && onTabChange(item.id)}
            className={cn(
              styles.tab,
              activeTab === item.id ? styles.active : styles.inactive,
              item.disabled && 'opacity-50 cursor-not-allowed'
            )}
            disabled={item.disabled}
          >
            <span className="flex items-center gap-2">
              {item.label}
              {item.badge && (
                <span className="px-2 py-0.5 text-xs font-medium bg-primary-light text-primary-foreground rounded-full">
                  {item.badge}
                </span>
              )}
            </span>
          </button>
        ))}
      </nav>
    </div>
  );
};

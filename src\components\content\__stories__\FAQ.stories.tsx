/**
 * FAQ Component Stories
 */

import type { Meta, StoryObj } from '@storybook/react';
import { FAQ, FAQBordered, FAQMinimal } from '../FAQ';
import type { FAQItem } from '../FAQ';

const meta: Meta<typeof FAQ> = {
  title: 'Content/FAQ',
  component: FAQ,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'Expandable FAQ component with search, categorization, and smooth animations. Supports multiple layouts and interaction modes.',
      },
    },
  },
  argTypes: {
    searchable: {
      control: 'boolean',
      description: 'Whether to enable search',
    },
    showCategories: {
      control: 'boolean',
      description: 'Whether to show categories',
    },
    allowMultiple: {
      control: 'boolean',
      description: 'Whether to allow multiple items expanded at once',
    },
    variant: {
      control: 'select',
      options: ['default', 'bordered', 'minimal'],
      description: 'Layout variant',
    },
    markdown: {
      control: 'boolean',
      description: 'Whether answers support markdown',
    },
    searchPlaceholder: {
      control: 'text',
      description: 'Search placeholder text',
    },
    noResultsMessage: {
      control: 'text',
      description: 'No results message',
    },
  },
};

export default meta;
type Story = StoryObj<typeof FAQ>;

// Sample FAQ data
const sampleFAQs: FAQItem[] = [
  {
    id: '1',
    question: 'What is the rental process?',
    answer: 'The rental process involves viewing properties, submitting applications, and signing contracts. We guide you through each step to ensure a smooth experience.',
    category: 'Rentals',
    tags: ['rental', 'process', 'application'],
    defaultExpanded: false,
  },
  {
    id: '2',
    question: 'How much is the security deposit?',
    answer: 'Security deposits typically range from 1-3 months rent depending on the property type and rental duration. This is refundable at the end of your lease.',
    category: 'Rentals',
    tags: ['deposit', 'security', 'payment'],
    defaultExpanded: true,
  },
  {
    id: '3',
    question: 'Can foreigners buy property in Bali?',
    answer: 'Foreigners can buy property through various legal structures including leasehold agreements and nominee arrangements. We recommend consulting with our legal team for the best approach.',
    category: 'Buying',
    tags: ['foreigner', 'legal', 'buying', 'ownership'],
    defaultExpanded: false,
  },
  {
    id: '4',
    question: 'What are the best areas to invest?',
    answer: 'Popular investment areas include Canggu, Seminyak, and Ubud due to their tourism appeal and rental potential. Each area offers different advantages for investors.',
    category: 'Investment',
    tags: ['investment', 'areas', 'location', 'ROI'],
    defaultExpanded: false,
  },
  {
    id: '5',
    question: 'Do you provide property management services?',
    answer: 'Yes, we offer comprehensive property management including maintenance, guest relations, cleaning, and rental management for investment properties.',
    category: 'Services',
    tags: ['management', 'services', 'maintenance'],
    defaultExpanded: false,
  },
  {
    id: '6',
    question: 'What documents do I need for renting?',
    answer: 'Required documents include passport copy, visa information, proof of income, and references. Additional documents may be required for longer-term rentals.',
    category: 'Rentals',
    tags: ['documents', 'requirements', 'visa'],
    defaultExpanded: false,
  },
  {
    id: '7',
    question: 'Are utilities included in the rent?',
    answer: 'Utility inclusion varies by property. Some include electricity and water, while others charge separately. Internet is typically included in most modern properties.',
    category: 'Rentals',
    tags: ['utilities', 'electricity', 'water', 'internet'],
    defaultExpanded: false,
  },
  {
    id: '8',
    question: 'What is the average ROI for rental properties?',
    answer: 'Average ROI ranges from 8-15% annually depending on location, property type, and management quality. Prime locations typically offer higher returns.',
    category: 'Investment',
    tags: ['ROI', 'returns', 'investment', 'profit'],
    defaultExpanded: false,
  },
];

const markdownFAQs: FAQItem[] = [
  {
    id: '1',
    question: 'What amenities are included?',
    answer: `
Most of our properties include:

- **Swimming pool** (private or shared)
- **High-speed WiFi** throughout the property
- **Air conditioning** in bedrooms and living areas
- **Fully equipped kitchen** with modern appliances
- **Daily housekeeping** (for short-term rentals)

*Additional amenities vary by property.*
    `,
    category: 'Amenities',
    tags: ['amenities', 'features'],
  },
  {
    id: '2',
    question: 'How do I make a booking?',
    answer: `
## Booking Process

1. **Browse** our available properties
2. **Contact** us with your preferred dates
3. **Submit** required documents
4. **Pay** deposit to secure booking
5. **Receive** confirmation and property details

> We typically respond within 24 hours to all inquiries.

[Contact us](mailto:<EMAIL>) to get started!
    `,
    category: 'Booking',
    tags: ['booking', 'process', 'reservation'],
  },
];

const fewFAQs = sampleFAQs.slice(0, 3);

// Basic Stories
export const Default: Story = {
  args: {
    items: sampleFAQs,
  },
};

export const WithoutSearch: Story = {
  args: {
    items: sampleFAQs,
    searchable: false,
  },
};

export const WithoutCategories: Story = {
  args: {
    items: sampleFAQs,
    showCategories: false,
  },
};

export const AllowMultipleExpanded: Story = {
  args: {
    items: sampleFAQs,
    allowMultiple: true,
  },
};

// Variant Styles
export const DefaultVariant: Story = {
  args: {
    items: fewFAQs,
    variant: 'default',
  },
};

export const BorderedVariant: Story = {
  args: {
    items: fewFAQs,
    variant: 'bordered',
  },
};

export const MinimalVariant: Story = {
  args: {
    items: fewFAQs,
    variant: 'minimal',
  },
};

// Markdown Support
export const WithMarkdown: Story = {
  args: {
    items: markdownFAQs,
    markdown: true,
  },
};

// Specialized Components
export const BorderedComponent: Story = {
  render: () => (
    <FAQBordered items={fewFAQs} />
  ),
};

export const MinimalComponent: Story = {
  render: () => (
    <FAQMinimal items={fewFAQs} />
  ),
};

// Interactive Examples
export const WithToggleCallback: Story = {
  args: {
    items: fewFAQs,
    onToggle: (itemId: string, isExpanded: boolean) => {
      console.log(`FAQ ${itemId} ${isExpanded ? 'expanded' : 'collapsed'}`);
    },
  },
};

// Custom Messages
export const CustomMessages: Story = {
  args: {
    items: sampleFAQs,
    searchPlaceholder: 'Search our knowledge base...',
    noResultsMessage: 'No answers found. Please try different keywords.',
  },
};

// Real Estate Examples
export const RentalFAQs: Story = {
  args: {
    items: sampleFAQs.filter(item => item.category === 'Rentals'),
    showCategories: false,
    searchPlaceholder: 'Search rental questions...',
  },
  parameters: {
    docs: {
      description: {
        story: 'FAQ section focused on rental-related questions.',
      },
    },
  },
};

export const InvestmentFAQs: Story = {
  args: {
    items: sampleFAQs.filter(item => item.category === 'Investment'),
    showCategories: false,
    variant: 'bordered',
    searchPlaceholder: 'Search investment questions...',
  },
  parameters: {
    docs: {
      description: {
        story: 'FAQ section focused on investment-related questions.',
      },
    },
  },
};

export const ComprehensiveFAQs: Story = {
  args: {
    items: sampleFAQs,
    allowMultiple: true,
    markdown: false,
    searchPlaceholder: 'Search all frequently asked questions...',
  },
  parameters: {
    docs: {
      description: {
        story: 'Complete FAQ section with all categories and search functionality.',
      },
    },
  },
};

// Edge Cases
export const EmptyFAQs: Story = {
  args: {
    items: [],
  },
};

export const SingleFAQ: Story = {
  args: {
    items: [sampleFAQs[0]],
    showCategories: false,
    searchable: false,
  },
};

// Layout Examples
export const CompactLayout: Story = {
  args: {
    items: sampleFAQs,
    variant: 'minimal',
    showCategories: false,
    searchable: false,
  },
};

export const FullFeaturedLayout: Story = {
  args: {
    items: sampleFAQs,
    variant: 'default',
    allowMultiple: true,
    markdown: true,
    searchable: true,
    showCategories: true,
  },
};

import { getPayload } from 'payload'
import config from '../../payload.config'

const addMissingLocations = async (): Promise<void> => {
  try {
    const payload = await getPayload({ config })

    console.log('🏝️ Adding missing locations...')

    // Check existing locations
    const existing = await payload.find({
      collection: 'locations',
      limit: 100,
    })
    
    const existingSlugs = existing.docs.map((doc: any) => doc.slug)
    console.log('Existing locations:', existingSlugs)

    // Add missing locations with simple text descriptions
    const missingLocations = [
      {
        name: 'Sanur',
        slug: 'sanur',
        description: 'Family-friendly beach town with traditional Balinese culture and international schools',
        priority: 'hoog',
        priceRange: {
          rentalMin: 800,
          rentalMax: 2500,
          saleMin: 150000,
          saleMax: 600000,
        },
        amenities: [
          { amenity: 'International schools' },
          { amenity: 'Family-friendly beaches' },
          { amenity: 'Traditional Balinese culture' },
          { amenity: 'Quality healthcare' },
          { amenity: 'Safe neighborhoods' },
          { amenity: 'Expat family community' },
        ],
      },
      {
        name: '<PERSON><PERSON><PERSON>',
        slug: 'jimbaran',
        description: 'Luxury beachfront destination famous for seafood dining and airport proximity',
        priority: 'hoog',
        priceRange: {
          rentalMin: 1200,
          rentalMax: 5000,
          saleMin: 250000,
          saleMax: 1500000,
        },
        amenities: [
          { amenity: 'Famous seafood restaurants' },
          { amenity: 'Luxury beachfront properties' },
          { amenity: 'Airport proximity' },
          { amenity: 'Premium golf courses' },
          { amenity: 'Luxury resorts and spas' },
          { amenity: 'Upscale expat community' },
        ],
      },
    ]

    for (const location of missingLocations) {
      if (!existingSlugs.includes(location.slug)) {
        try {
          const created = await payload.create({
            collection: 'locations',
            data: location,
          })
          console.log(`✅ Created location: ${location.name} (ID: ${created.id})`)
        } catch (error) {
          console.error(`❌ Error creating location ${location.name}:`, error)
        }
      } else {
        console.log(`ℹ️ Location ${location.name} already exists`)
      }
    }

    console.log('🎉 Missing locations added successfully!')
    process.exit(0)
  } catch (error) {
    console.error('❌ Error adding missing locations:', error)
    process.exit(1)
  }
}

addMissingLocations()

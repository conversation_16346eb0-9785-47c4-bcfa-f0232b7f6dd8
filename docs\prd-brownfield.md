# 🏗️ **Bali Property Scout Brownfield Enhancement PRD**

## Intro Project Analysis and Context

### Analysis Source
**Document-project output available at:** Brownfield Architecture Document completed by Architect Agent

### Current Project State
**Extracted from High Level Architecture and Technical Summary:**

The Bali Property Scout website is a modern Next.js 15 application with Payload CMS backend, currently branded as "Bali Real Estate" and requiring comprehensive UI/UX transformation. The system is well-structured with:

- **Technical Foundation**: Next.js 15.5.0 (App Router) + React 19.1.0 + Tailwind CSS 4.0
- **CMS Integration**: Payload CMS 3.52.0 with PostgreSQL/MongoDB dual support  
- **Performance**: Vercel-hosted with Core Web Vitals optimization
- **Current State**: Functional but needs branding update, modern UI enhancements, and chatbot integration fixes

### Available Documentation Analysis
**Document-project analysis available - using existing technical documentation**

**Key documents created by document-project:**
✅ Tech Stack Documentation  
✅ Source Tree/Architecture  
✅ API Documentation  
✅ External API Documentation  
✅ Technical Debt Documentation  
⚠️ UX/UI Guidelines (needs enhancement)

### Enhancement Scope Definition

#### Enhancement Type
☑️ **UI/UX Overhaul** - Primary focus  
☑️ **Major Feature Modification** - Chatbot integration fixes  
☑️ **New Feature Addition** - Interactive elements, animations  
☐ Integration with New Systems  
☐ Performance/Scalability Improvements  
☐ Technology Stack Upgrade  
☐ Bug Fix and Stability Improvements

#### Enhancement Description
Transform the existing "Bali Real Estate" website into a modern "Bali Property Scout" platform with floating navigation, cinematic hero sections, interactive elements, and proper chatbot integration routing to app.balipropertyscout.com subdomain.

#### Impact Assessment
☑️ **Significant Impact** (substantial existing code changes)
- Systematic branding updates across all components
- UI component enhancements and new interactive features
- CTA routing changes throughout the application
- Service page restructuring and content cleanup

### Goals and Background Context

#### Goals
• **Rebrand** from "Bali Real Estate" to "Bali Property Scout" across all touchpoints
• **Modernize UI/UX** with floating header, cinematic hero sections, and micro-animations
• **Fix chatbot integration** by routing all CTAs to app.balipropertyscout.com with proper query strings
• **Remove outdated services** (Property Management, Legal Services) from all pages
• **Enhance user experience** with interactive elements, hover effects, and smooth transitions
• **Maintain SEO performance** and existing Lighthouse scores (85+)
• **Preserve existing functionality** while adding modern UI enhancements

#### Background Context
The current Bali Property Scout website has a solid technical foundation but suffers from outdated branding, broken chatbot links, and static UI elements that don't meet modern user expectations. The site needs to evolve from an information-driven platform to an interactive, conversion-focused experience that guides users to the AI chatbot for property searches. This enhancement will transform the user journey while maintaining the existing CMS structure and SEO performance.

#### Change Log
| Change | Date | Version | Description | Author |
|--------|------|---------|-------------|---------|
| Initial PRD | 2025-01-28 | 1.0 | Comprehensive UI/UX enhancement PRD | PM Agent |

## Requirements

### Functional

**FR1**: The website branding must be systematically updated from "Bali Real Estate" to "Bali Property Scout" across all pages, components, metadata, and configuration files.

**FR2**: All call-to-action buttons and links must route to app.balipropertyscout.com with appropriate query strings (e.g., ?query=rent-villa-in-canggu) instead of current broken routing.

**FR3**: The header component must be enhanced to a modern floating design with semi-transparent background, rounded corners, subtle shadow, and scroll-responsive behavior.

**FR4**: The homepage hero section must feature cinematic background imagery/video with translucent overlay cards and prominent CTAs for "Start Chat" and "Browse Locations".

**FR5**: Location pages must implement sticky anchor navigation (Overview, Neighborhoods, Property Types, Lifestyle, Practical Info, FAQ) with smooth scrolling and active section highlighting.

**FR6**: All location and property type cards must include hover animations (scale, shadow effects) and interactive elements for enhanced user engagement.

**FR7**: The services page must be completely restructured to remove "Property Management" and "Legal Services" while highlighting only "Investment Consultation", "Property Purchase Assistance", and "Consultation & Viewing".

**FR8**: Interactive elements must be added including collapsible accordions for FAQs, progress indicators for forms, and micro-animations for user feedback.

**FR9**: All forms must include real-time validation, success/error feedback, and enhanced user experience patterns.

**FR10**: The about page must be redesigned with AI-focused messaging, interactive timeline/stepper components, and team section with hover micro-interactions.

### Non Functional

**NFR1**: Enhancement must maintain existing Lighthouse performance scores of 85+ for both mobile and desktop while adding new interactive features.

**NFR2**: All new UI components must comply with WCAG 2.1 AA accessibility guidelines including keyboard navigation, screen reader support, and proper contrast ratios.

**NFR3**: The website must remain fully responsive across all device sizes (mobile, tablet, desktop) with enhanced mobile experience for floating header and interactive elements.

**NFR4**: All animations and transitions must use hardware acceleration and follow performance best practices to avoid layout thrashing or janky animations.

**NFR5**: The enhancement must preserve existing SEO performance including structured data, meta tags, and search engine rankings.

**NFR6**: New interactive features must degrade gracefully for users with reduced motion preferences or older browsers.

**NFR7**: All chatbot integrations must handle network failures gracefully and provide fallback options for users.

### Compatibility Requirements

**CR1**: **Existing API Compatibility**: All Payload CMS API endpoints and data structures must remain unchanged to preserve content management workflows.

**CR2**: **Database Schema Compatibility**: No changes to existing CMS collections or database schema to maintain data integrity and admin functionality.

**CR3**: **UI/UX Consistency**: New UI components must integrate seamlessly with existing design system tokens and component library patterns.

**CR4**: **Integration Compatibility**: Existing integrations with Unsplash API, Google Analytics, and Vercel deployment pipeline must remain functional.

## User Interface Enhancement Goals

### Integration with Existing UI
New UI elements will extend the existing design system in `src/components/design-system/` by adding new animation tokens, interactive component variants, and enhanced spacing/shadow utilities. The floating header will build upon the current Header component structure while adding new responsive behaviors and visual enhancements.

### Modified/New Screens and Views
**Modified Screens:**
- Homepage (`src/app/page.tsx`) - Cinematic hero, enhanced location cards
- All location pages (`src/app/locations/[slug]/page.tsx`) - Anchor navigation, interactive elements
- Property type pages (`src/app/property-types/[slug]/page.tsx`) - Enhanced cards, CTAs
- Services page (`src/app/services/page.tsx`) - Complete restructure
- About page (`src/app/about/page.tsx`) - AI-focused redesign
- Contact page (`src/app/contact/page.tsx`) - Enhanced forms, interactive map

**New Components:**
- Enhanced floating header with scroll behavior
- Interactive anchor navigation for long pages
- Animated cards with hover effects
- Progress indicators for multi-step forms
- Interactive timeline/stepper components

### UI Consistency Requirements
All new interactive elements must use existing Tailwind CSS classes and design system tokens. Animations must follow the established duration and easing patterns. Color schemes must maintain the current emerald/green primary palette while adding new accent colors for interactive states.

## Technical Constraints and Integration Requirements

### Existing Technology Stack
**Languages**: TypeScript, JavaScript  
**Frameworks**: Next.js 15.5.0 (App Router), React 19.1.0, Tailwind CSS 4.0  
**Database**: Payload CMS 3.52.0 with PostgreSQL/MongoDB dual support  
**Infrastructure**: Vercel hosting with Edge Functions and CDN optimization  
**External Dependencies**: Unsplash API, Google Analytics, Sharp image optimization

### Integration Approach
**Database Integration Strategy**: No database changes required - all enhancements are UI/frontend focused using existing CMS data  
**API Integration Strategy**: Maintain existing Payload CMS API calls, add new chatbot routing logic  
**Frontend Integration Strategy**: Extend existing component library, enhance current pages without breaking existing functionality  
**Testing Integration Strategy**: Utilize existing Vitest, Storybook, and Playwright setup for new component testing

### Code Organization and Standards
**File Structure Approach**: Follow existing `src/components/` organization, add new interactive components in appropriate subdirectories  
**Naming Conventions**: Maintain current PascalCase for components, camelCase for utilities, kebab-case for CSS classes  
**Coding Standards**: Continue using existing ESLint configuration, TypeScript strict mode, and component documentation patterns  
**Documentation Standards**: Update existing Storybook stories, maintain inline component documentation

### Deployment and Operations
**Build Process Integration**: Use existing Next.js build process with Vercel optimization  
**Deployment Strategy**: Maintain current Vercel automatic deployment from Git with preview environments  
**Monitoring and Logging**: Preserve existing performance monitoring and analytics tracking  
**Configuration Management**: Extend current environment variable approach for domain configuration

### Risk Assessment and Mitigation
**Technical Risks**: 
- Branding updates across multiple files could introduce inconsistencies
- New animations might impact performance on lower-end devices
- Chatbot routing changes could break existing user flows

**Integration Risks**:
- Floating header changes might conflict with existing scroll behaviors
- Interactive elements could interfere with existing form functionality
- CTA routing updates might miss some instances

**Deployment Risks**:
- Domain configuration changes could cause routing issues
- Large UI changes might temporarily impact SEO rankings

**Mitigation Strategies**:
- Systematic testing of all branding updates using search/replace verification
- Performance budgets and monitoring for animation impact
- Gradual rollout of interactive features with fallback options
- Comprehensive QA testing of all CTA routing changes

## Epic and Story Structure

### Epic Approach
**Epic Structure Decision**: Single comprehensive epic with sequential story implementation to minimize risk to existing system functionality while delivering cohesive UI/UX transformation.

**Rationale**: The enhancement involves interconnected UI changes that build upon each other (branding → navigation → interactive elements → forms). A single epic ensures consistent implementation and reduces integration complexity.

## Epic 1: Bali Property Scout UI/UX Transformation

**Epic Goal**: Transform the existing Bali Real Estate website into a modern, interactive Bali Property Scout platform with enhanced UI/UX, proper chatbot integration, and removal of outdated services while maintaining existing functionality and performance.

**Integration Requirements**: All UI enhancements must integrate seamlessly with existing Payload CMS collections, maintain current page performance, preserve SEO rankings, and enhance rather than replace existing user workflows.

### Story 1.1: Branding and Domain Foundation Update

As a website administrator,
I want to systematically update all branding from "Bali Real Estate" to "Bali Property Scout" and configure proper domain routing,
so that the website reflects the correct brand identity and chatbot links function properly.

#### Acceptance Criteria
1. All instances of "Bali Real Estate" in code, metadata, and configuration files are updated to "Bali Property Scout"
2. Package.json name, description, and URLs are updated to reflect new branding
3. All chatbot CTA links route to app.balipropertyscout.com with appropriate query strings
4. Environment variables are configured for flexible domain management
5. Logo and brand assets are updated in header and footer components

#### Integration Verification
**IV1**: Existing page functionality verification - All pages load correctly with new branding without broken links or missing assets
**IV2**: CMS integration verification - Payload CMS admin interface remains functional and content displays correctly
**IV3**: SEO impact verification - Meta tags, structured data, and sitemap reflect new branding without losing search rankings

### Story 1.2: Enhanced Floating Header Implementation

As a website visitor,
I want to see a modern floating header that responds to scroll behavior and provides intuitive navigation,
so that I can easily navigate the site with an enhanced visual experience.

#### Acceptance Criteria
1. Header component is enhanced with semi-transparent background, rounded corners, and subtle shadow
2. Header shrinks slightly on scroll and maintains floating behavior
3. Mobile hamburger menu includes slide-in panel with smooth animations
4. "Find My Property" CTA button is prominently displayed and routes to chatbot
5. Dropdown menus for Locations and Property Types function smoothly with hover effects

#### Integration Verification
**IV1**: Navigation functionality verification - All existing navigation links work correctly with new header design
**IV2**: Mobile responsiveness verification - Header functions properly across all device sizes
**IV3**: Performance verification - Header animations don't impact page scroll performance or Core Web Vitals

### Story 1.3: Cinematic Homepage Hero Enhancement

As a website visitor,
I want to see an engaging cinematic hero section that immediately communicates the value proposition,
so that I'm motivated to explore properties and start a conversation with the chatbot.

#### Acceptance Criteria
1. Hero section features high-resolution background image/video of Bali with overlay effects
2. Translucent card contains compelling headline "Find Your Perfect Bali Home" with sub-headline
3. Two prominent CTAs: "Start Chat" (routes to chatbot) and "Browse Locations" (smooth scroll)
4. Floating elements and subtle animations enhance visual appeal
5. Hero section is fully responsive and maintains performance standards

#### Integration Verification
**IV1**: CTA functionality verification - Both hero CTAs route correctly and existing page flow is maintained
**IV2**: Content integration verification - Hero content integrates with existing CMS data and SEO metadata
**IV3**: Performance verification - Hero imagery/video doesn't negatively impact page load times

### Story 1.4: Interactive Location Cards and Carousel

As a website visitor,
I want to see visually appealing location cards with interactive elements,
so that I can easily explore different areas and understand their unique characteristics.

#### Acceptance Criteria
1. High priority location cards feature real photography with hover animations (scale & shadow)
2. Cards display priority tags, price ranges, and key features with improved typography
3. "Learn More" and "Find Properties" CTAs are clearly differentiated and functional
4. Hover effects provide visual feedback without impacting accessibility
5. Cards maintain responsive design across all device sizes

#### Integration Verification
**IV1**: Data integration verification - Location cards display correct CMS data and maintain existing data flow
**IV2**: Navigation verification - Card CTAs route correctly to location pages and chatbot
**IV3**: Accessibility verification - Interactive elements remain keyboard navigable and screen reader friendly

### Story 1.5: Sticky Anchor Navigation for Location Pages

As a website visitor on location pages,
I want to see a sticky anchor navigation that helps me jump to different sections,
so that I can easily find specific information about locations.

#### Acceptance Criteria
1. Horizontal anchor navigation (Overview, Neighborhoods, Property Types, Lifestyle, Practical Info, FAQ) is implemented
2. Navigation becomes sticky below the floating header when scrolling
3. Active section is highlighted based on scroll position
4. Smooth scrolling behavior when clicking anchor links
5. Navigation is responsive and collapses appropriately on mobile

#### Integration Verification
**IV1**: Content structure verification - Anchor navigation works with existing location page content structure
**IV2**: Scroll behavior verification - Sticky navigation doesn't conflict with existing scroll behaviors
**IV3**: Mobile experience verification - Navigation functions properly on all device sizes

### Story 1.6: Interactive Neighborhood Cards and Maps

As a website visitor exploring locations,
I want to see interactive neighborhood cards with visual elements,
so that I can understand different areas and their characteristics.

#### Acceptance Criteria
1. Neighborhood cards feature photos, descriptions, and "Best For" tags
2. Cards have hover lift effects and smooth transitions
3. Mini-map or static map with pins shows neighborhood locations
4. Interactive elements provide visual feedback without impacting performance
5. Cards maintain consistent design with overall site aesthetic

#### Integration Verification
**IV1**: Content integration verification - Neighborhood data displays correctly from existing CMS structure
**IV2**: Map functionality verification - Maps load properly and don't impact page performance
**IV3**: Interactive behavior verification - Hover effects work consistently across different browsers

### Story 1.7: Services Page Restructuring and Cleanup

As a website visitor interested in services,
I want to see a clean, focused services page that clearly presents available offerings,
so that I understand what services are available and can easily request consultation.

#### Acceptance Criteria
1. Page is retitled to "Our Services – Investment, Purchase & Consultation"
2. All references to "Property Management" and "Legal Services" are completely removed
3. Three-column layout showcases Investment Consultation, Property Purchase Assistance, and Consultation & Viewing
4. Each service includes bullet points, statistics, and chatbot CTA buttons
5. Cross-links section includes cards linking to locations, property types, and homepage

#### Integration Verification
**IV1**: Content cleanup verification - All outdated service references are removed from navigation, footer, and content
**IV2**: CTA functionality verification - Service-specific chatbot links route correctly with appropriate query strings
**IV3**: SEO preservation verification - Page restructuring maintains search engine optimization

### Story 1.8: Enhanced Forms with Validation and Feedback

As a website visitor filling out forms,
I want to see real-time validation and clear feedback,
so that I can successfully submit my information without frustration.

#### Acceptance Criteria
1. Contact forms include real-time validation with clear error messages
2. Success and error states are visually distinct and accessible
3. Progress indicators are shown for multi-step forms
4. Form submissions integrate with existing lead capture system
5. Forms maintain responsive design and accessibility standards

#### Integration Verification
**IV1**: Data flow verification - Form submissions continue to work with existing CMS lead capture
**IV2**: Validation logic verification - New validation doesn't break existing form functionality
**IV3**: Accessibility verification - Enhanced forms remain fully accessible with screen readers

### Story 1.9: About Page AI-Focused Redesign

As a website visitor learning about the company,
I want to see an engaging about page that highlights AI capabilities and team expertise,
so that I understand the unique value proposition and feel confident in the service.

#### Acceptance Criteria
1. Hero section features "AI-Powered Real Estate, Human-Centered Service" messaging
2. Interactive timeline/stepper shows company milestones with scroll animations
3. "Meet Your AI-Enhanced Team" section includes photos/avatars with hover micro-interactions
4. Content is organized in clear sections with icons and visual hierarchy
5. CTAs encourage visitors to try the AI assistant or contact the team

#### Integration Verification
**IV1**: Content integration verification - New about content integrates with existing site structure and navigation
**IV2**: Animation performance verification - Interactive elements don't impact page performance
**IV3**: Brand consistency verification - About page messaging aligns with overall site branding

### Story 1.10: Contact Page Enhancement with Interactive Elements

As a website visitor wanting to get in touch,
I want to see an enhanced contact page with multiple interaction options,
so that I can easily reach out in my preferred way.

#### Acceptance Criteria
1. Forms are organized using tabs or accordions to reduce visual clutter
2. Interactive map shows office location in Canggu with proper loading behavior
3. AI chat widget (floating bubble) is available for quick questions
4. FAQ section uses collapsible accordions for common questions
5. All contact methods are clearly presented with appropriate CTAs

#### Integration Verification
**IV1**: Form functionality verification - Contact forms continue to work with existing lead processing
**IV2**: Map integration verification - Interactive map loads properly without impacting page performance
**IV3**: Chat widget verification - AI chat widget integrates properly with app.balipropertyscout.com

---

**This story sequence is designed to minimize risk to your existing system by starting with foundational changes (branding, navigation) and building up to more complex interactive features. Each story maintains system integrity while adding value. Does this order make sense given your project's architecture and constraints?**

---

## 📋 **PRD Summary**

This comprehensive PRD outlines the transformation of the Bali Property Scout website from its current state to a modern, interactive platform. The enhancement maintains the solid technical foundation while adding contemporary UI/UX elements, proper chatbot integration, and streamlined service offerings.

**Key Success Metrics:**
- Maintain Lighthouse scores 85+
- Preserve existing SEO rankings
- Achieve WCAG 2.1 AA compliance
- Complete branding transformation
- Functional chatbot routing to app.balipropertyscout.com

**Ready for handoff to UX Expert for detailed frontend specification.**

'use client'

import { useState, useEffect, useCallback } from 'react'
import { useAIContext } from '../AIContext'
import { ChatMessage, UserPreferences } from '../AIContext'

interface ConversationAPI {
  sessionId: string | null
  messages: ChatMessage[]
  isLoading: boolean
  error: string | null
  
  // Actions
  startConversation: (initialMessage?: string) => Promise<void>
  sendMessage: (content: string) => Promise<void>
  updatePreferences: (preferences: Partial<UserPreferences>) => Promise<void>
  clearConversation: () => Promise<void>
  loadConversation: (sessionId: string) => Promise<void>
}

export const useConversation = (): ConversationAPI => {
  const { state, dispatch } = useAIContext()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Generate session ID - avoid hydration issues
  const generateSessionId = () => {
    if (typeof window === 'undefined') {
      // Server-side fallback
      return `session_server_${Math.floor(Math.random() * 1000000)}`
    }

    // Client-side with crypto API
    const timestamp = Date.now()
    const randomBytes = crypto.getRandomValues(new Uint8Array(4))
    const randomString = Array.from(randomBytes, byte => byte.toString(36)).join('')
    return `session_${timestamp}_${randomString}`
  }

  // Start new conversation
  const startConversation = useCallback(async (initialMessage?: string) => {
    setIsLoading(true)
    setError(null)

    try {
      const sessionId = generateSessionId()
      
      const response = await fetch('/api/ai/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          initialMessage,
          context: state.context,
          userAgent: navigator.userAgent,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to start conversation')
      }

      const data = await response.json()
      
      dispatch({
        type: 'START_CONVERSATION',
        sessionId: data.sessionId,
      })

      // Add initial message if provided
      if (initialMessage) {
        const message: ChatMessage = {
          id: `msg_${Date.now()}`,
          role: 'user',
          content: initialMessage,
          timestamp: new Date(),
        }
        
        dispatch({
          type: 'ADD_MESSAGE',
          message,
        })
      }

      // Store session ID in localStorage for persistence
      localStorage.setItem('ai_session_id', data.sessionId)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsLoading(false)
    }
  }, [state.context, dispatch])

  // Load existing conversation
  const loadConversation = useCallback(async (sessionId: string) => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/ai/conversations?sessionId=${sessionId}`)
      
      if (!response.ok) {
        throw new Error('Failed to load conversation')
      }

      const data = await response.json()
      
      if (data.exists && data.conversation) {
        dispatch({
          type: 'START_CONVERSATION',
          sessionId: data.conversation.sessionId,
        })

        // Load messages
        data.conversation.messages.forEach((message: any) => {
          dispatch({
            type: 'ADD_MESSAGE',
            message: {
              id: message.id,
              role: message.role,
              content: message.content,
              timestamp: new Date(message.timestamp),
              metadata: message.metadata,
            },
          })
        })

        // Load preferences
        if (data.conversation.userPreferences) {
          dispatch({
            type: 'UPDATE_PREFERENCES',
            preferences: data.conversation.userPreferences,
          })
        }
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsLoading(false)
    }
  }, [dispatch])

  // Send message
  const sendMessage = useCallback(async (content: string) => {
    if (!state.currentConversation.sessionId) {
      await startConversation(content)
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // Add user message to UI immediately
      const userMessage: ChatMessage = {
        id: `msg_${Date.now()}_user`,
        role: 'user',
        content,
        timestamp: new Date(),
      }

      dispatch({
        type: 'ADD_MESSAGE',
        message: userMessage,
      })

      // Send to API
      const response = await fetch('/api/ai/conversations', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: state.currentConversation.sessionId,
          message: {
            role: 'user',
            content,
          },
          userPreferences: state.userPreferences,
          context: state.context,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to send message')
      }

      // Simulate AI response (placeholder)
      dispatch({
        type: 'SET_TYPING',
        isTyping: true,
      })

      setTimeout(() => {
        const aiMessage: ChatMessage = {
          id: `msg_${Date.now()}_ai`,
          role: 'assistant',
          content: "Thanks for your message! I'm processing your request and will provide property recommendations soon.",
          timestamp: new Date(),
        }

        dispatch({
          type: 'ADD_MESSAGE',
          message: aiMessage,
        })

        dispatch({
          type: 'SET_TYPING',
          isTyping: false,
        })
      }, 1500)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
      dispatch({
        type: 'SET_TYPING',
        isTyping: false,
      })
    } finally {
      setIsLoading(false)
    }
  }, [state.currentConversation.sessionId, state.userPreferences, state.context, dispatch, startConversation])

  // Update preferences
  const updatePreferences = useCallback(async (preferences: Partial<UserPreferences>) => {
    if (!state.currentConversation.sessionId) {
      dispatch({
        type: 'UPDATE_PREFERENCES',
        preferences,
      })
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/ai/preferences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: state.currentConversation.sessionId,
          preferences,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to update preferences')
      }

      dispatch({
        type: 'UPDATE_PREFERENCES',
        preferences,
      })

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsLoading(false)
    }
  }, [state.currentConversation.sessionId, dispatch])

  // Clear conversation
  const clearConversation = useCallback(async () => {
    if (!state.currentConversation.sessionId) {
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/ai/conversations?sessionId=${state.currentConversation.sessionId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to clear conversation')
      }

      dispatch({
        type: 'END_CONVERSATION',
      })

      // Clear from localStorage
      localStorage.removeItem('ai_session_id')

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsLoading(false)
    }
  }, [state.currentConversation.sessionId, dispatch])

  // Load conversation on mount if session exists
  useEffect(() => {
    const savedSessionId = localStorage.getItem('ai_session_id')
    if (savedSessionId && !state.currentConversation.sessionId) {
      loadConversation(savedSessionId)
    }
  }, [loadConversation, state.currentConversation.sessionId])

  return {
    sessionId: state.currentConversation.sessionId,
    messages: state.currentConversation.messages,
    isLoading,
    error,
    startConversation,
    sendMessage,
    updatePreferences,
    clearConversation,
    loadConversation,
  }
}

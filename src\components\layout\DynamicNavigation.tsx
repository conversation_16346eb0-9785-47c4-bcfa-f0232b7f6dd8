/**
 * Dynamic Navigation Component
 * Bali Property Scout Website
 * 
 * Provides navigation items with database fallback for locations and property types.
 * Optimized for performance with caching and error handling.
 */

'use client';

import React, { useState, useEffect, useMemo } from 'react';

export interface NavigationItem {
  label: string;
  href: string;
  children?: NavigationItem[];
}

interface DynamicNavigationProps {
  onNavigationReady?: (items: NavigationItem[]) => void;
  fallbackToStatic?: boolean;
}

// Static navigation fallback
const STATIC_NAVIGATION: NavigationItem[] = [
  {
    label: 'Home',
    href: '/',
  },
  {
    label: 'Locations',
    href: '/locations',
    children: [
      { label: 'All Locations', href: '/locations' },
      { label: 'Canggu', href: '/locations/canggu' },
      { label: 'Ubud', href: '/locations/ubud' },
      { label: 'Seminyak', href: '/locations/seminyak' },
      { label: 'Sanur', href: '/locations/sanur' },
      { label: 'Jimbaran', href: '/locations/jimbaran' },
    ],
  },
  {
    label: 'Property Types',
    href: '/property-types',
    children: [
      { label: 'All Property Types', href: '/property-types' },
      { label: 'Villa', href: '/property-types/villa' },
      { label: 'Apartment', href: '/property-types/apartment' },
      { label: 'Guesthouse', href: '/property-types/guesthouse' },
      { label: 'Land', href: '/property-types/land' },
    ],
  },
  {
    label: 'Contact',
    href: '/contact',
  },
];

export const DynamicNavigation: React.FC<DynamicNavigationProps> = ({
  onNavigationReady,
  fallbackToStatic = true,
}) => {
  const [navigationItems, setNavigationItems] = useState<NavigationItem[]>(STATIC_NAVIGATION);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch navigation from API
  const fetchNavigationFromAPI = useMemo(() => {
    return async (): Promise<NavigationItem[]> => {
      try {
        console.log('🔄 Fetching navigation from API...');
        const startTime = Date.now();

        const response = await fetch('/api/navigation', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`API request failed: ${response.status}`);
        }

        const result = await response.json();

        const fetchTime = Date.now() - startTime;
        console.log(`✅ Navigation fetched from API in ${fetchTime}ms`);

        return result.data || STATIC_NAVIGATION;
      } catch (error) {
        console.warn('Failed to fetch navigation from API:', error);
        throw error;
      }
    };
  }, []);

  // Load navigation data - ONLY ONCE on mount
  useEffect(() => {
    let isMounted = true;
    let hasLoaded = false; // Prevent multiple loads

    const loadNavigation = async () => {
      if (hasLoaded || isLoading) return;
      hasLoaded = true;

      console.log('🔄 DynamicNavigation: Starting navigation load...');
      setIsLoading(true);
      setError(null);

      try {
        const items = await fetchNavigationFromAPI();
        
        if (isMounted) {
          console.log('✅ DynamicNavigation: Navigation loaded successfully');
          setNavigationItems(items);
          onNavigationReady?.(items);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        console.warn('❌ DynamicNavigation: Navigation loading failed:', errorMessage);

        if (isMounted) {
          setError(errorMessage);

          // Use static fallback if enabled
          if (fallbackToStatic) {
            console.log('🔄 DynamicNavigation: Using static navigation fallback');
            setNavigationItems(STATIC_NAVIGATION);
            onNavigationReady?.(STATIC_NAVIGATION);
          }
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    loadNavigation();

    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array - only run once on mount to prevent infinite loop

  // Return navigation items for use by parent components
  return null; // This is a headless component
};

// Hook for using dynamic navigation
export function useDynamicNavigation(fallbackToStatic: boolean = true) {
  const [navigationItems, setNavigationItems] = useState<NavigationItem[]>(STATIC_NAVIGATION);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const handleNavigationReady = (items: NavigationItem[]) => {
    setNavigationItems(items);
    setIsLoading(false);
    setError(null);
  };

  return {
    navigationItems,
    isLoading,
    error,
    DynamicNavigationProvider: () => (
      <DynamicNavigation
        onNavigationReady={handleNavigationReady}
        fallbackToStatic={fallbackToStatic}
      />
    ),
  };
}

// Export static navigation for immediate use
export { STATIC_NAVIGATION };

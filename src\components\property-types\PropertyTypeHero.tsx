/**
 * Property Type Hero Component
 * Bali Property Scout Website
 * 
 * High-quality hero banners for property type pages with overlay headlines,
 * taglines, and professional imagery. Supports different property types
 * with customized styling and messaging.
 */

'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import ChatbotCTA from '@/components/ui/ChatbotCTA';

export interface PropertyTypeHeroProps {
  /** Property type slug (villa, guesthouse, apartment, etc.) */
  propertyType: string;
  /** Display name of the property type */
  name: string;
  /** Hero headline */
  headline: string;
  /** Supporting tagline */
  tagline: string;
  /** Price range display */
  priceRange: string;
  /** Key features to highlight */
  features: string[];
  /** Background image URL */
  backgroundImage?: string;
  /** Additional CSS classes */
  className?: string;
}

// Property type specific configurations
const propertyTypeConfigs = {
  villa: {
    backgroundImage: '/images/hero/villa-hero.jpg',
    gradientOverlay: 'from-emerald-900/80 via-emerald-800/70 to-emerald-700/80',
    accentColor: 'emerald',
    icon: (
      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
      </svg>
    )
  },
  guesthouse: {
    backgroundImage: '/images/hero/guesthouse-hero.jpg',
    gradientOverlay: 'from-amber-900/80 via-orange-800/70 to-amber-700/80',
    accentColor: 'amber',
    icon: (
      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
      </svg>
    )
  },
  apartment: {
    backgroundImage: '/images/hero/apartment-hero.jpg',
    gradientOverlay: 'from-blue-900/80 via-indigo-800/70 to-blue-700/80',
    accentColor: 'blue',
    icon: (
      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 8h1m-1-4h1m4 4h1m-1-4h1" />
      </svg>
    )
  },
  land: {
    backgroundImage: '/images/hero/land-hero.jpg',
    gradientOverlay: 'from-green-900/80 via-teal-800/70 to-green-700/80',
    accentColor: 'green',
    icon: (
      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    )
  }
};

export const PropertyTypeHero: React.FC<PropertyTypeHeroProps> = ({
  propertyType,
  name,
  headline,
  tagline,
  priceRange,
  features,
  backgroundImage,
  className,
}) => {
  const config = propertyTypeConfigs[propertyType as keyof typeof propertyTypeConfigs] || propertyTypeConfigs.villa;
  const heroBackground = backgroundImage || config.backgroundImage;

  return (
    <section 
      className={cn('relative min-h-[70vh] flex items-center justify-center overflow-hidden', className)}
    >
      {/* Background Image */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url(${heroBackground})`,
        }}
      />
      
      {/* Professional Gradient Overlay */}
      <div className={cn('absolute inset-0 bg-gradient-to-br', config.gradientOverlay)} />
      
      {/* Subtle Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-48 h-48 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-64 h-64 bg-white/8 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      {/* Content Container */}
      <div className="relative z-10 max-w-6xl mx-auto container-padding text-center text-white">
        <div className="max-w-4xl mx-auto">
          {/* Property Type Icon & Badge */}
          <div className="flex items-center justify-center mb-6">
            <div className="flex items-center gap-4 bg-white/10 backdrop-blur-md rounded-2xl px-6 py-3 border border-white/20">
              <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                {config.icon}
              </div>
              <div className="text-left">
                <div className="text-sm text-white/80 font-medium">Property Type</div>
                <div className="text-lg font-bold">{name}</div>
              </div>
            </div>
          </div>

          {/* Main Headline */}
          <h1 className="heading-1 text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight text-white">
            {headline}
          </h1>

          {/* Tagline */}
          <p className="body-text-large text-lg md:text-xl mb-8 text-white/90 max-w-3xl mx-auto leading-relaxed">
            {tagline}
          </p>

          {/* Price Range Highlight */}
          <div className="mb-8">
            <div className="inline-flex items-center gap-2 bg-white/15 backdrop-blur-md rounded-xl px-6 py-3 border border-white/20">
              <svg className="w-5 h-5 text-white/80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
              <span className="text-lg font-semibold">{priceRange}</span>
            </div>
          </div>

          {/* Key Features */}
          <div className="mb-10">
            <div className="flex flex-wrap justify-center gap-3 max-w-2xl mx-auto">
              {features.slice(0, 4).map((feature, index) => (
                <div
                  key={index}
                  className="flex items-center gap-2 bg-white/10 backdrop-blur-md rounded-lg px-4 py-2 border border-white/20"
                >
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                  <span className="text-sm font-medium">{feature}</span>
                </div>
              ))}
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <ChatbotCTA
              query={{ 
                type: 'property-type', 
                propertyType: propertyType,
                context: 'hero-search'
              }}
              variant="primary"
              size="lg"
              className="btn-primary text-lg font-semibold px-8 py-4 min-h-[56px] shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300"
              style={{
                backgroundColor: 'var(--brand-primary)',
                borderColor: 'var(--brand-primary)',
                color: 'var(--neutral-white)'
              }}
            >
              Find {name} Properties
            </ChatbotCTA>
            
            <button
              onClick={() => {
                const featuresSection = document.getElementById('property-features');
                if (featuresSection) {
                  featuresSection.scrollIntoView({ behavior: 'smooth' });
                }
              }}
              className="btn-secondary text-lg font-semibold px-8 py-4 min-h-[56px] bg-transparent border-2 border-white text-white hover:bg-white hover:text-gray-900 shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
            >
              Learn More
            </button>
          </div>

          {/* Trust Indicators */}
          <div className="mt-12 flex flex-wrap justify-center items-center gap-8 text-white/80">
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              <span className="text-sm font-medium">AI-Powered Matching</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              <span className="text-sm font-medium">Local Market Expertise</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              <span className="text-sm font-medium">500+ Properties Available</span>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/70 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};

export default PropertyTypeHero;

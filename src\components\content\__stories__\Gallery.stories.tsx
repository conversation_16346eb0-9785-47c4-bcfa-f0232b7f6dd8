/**
 * Gallery Component Stories
 */

import type { Meta, StoryObj } from '@storybook/react';
import { Gallery } from '../Gallery';
import type { GalleryImage } from '../Gallery';

const meta: Meta<typeof Gallery> = {
  title: 'Content/Gallery',
  component: Gallery,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'Image gallery with lightbox, thumbnails, and responsive design. Supports multiple layouts and interaction modes.',
      },
    },
  },
  argTypes: {
    layout: {
      control: 'select',
      options: ['grid', 'masonry', 'carousel', 'thumbnails'],
      description: 'Gallery layout',
    },
    columns: {
      control: 'select',
      options: [1, 2, 3, 4, 5, 6],
      description: 'Number of columns for grid layout',
    },
    gap: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'Gap between images',
    },
    lightbox: {
      control: 'boolean',
      description: 'Whether to enable lightbox',
    },
    showThumbnails: {
      control: 'boolean',
      description: 'Whether to show thumbnails in lightbox',
    },
    showCaptions: {
      control: 'boolean',
      description: 'Whether to show captions',
    },
    aspectRatio: {
      control: 'select',
      options: ['square', '4/3', '16/9', '3/2', 'auto'],
      description: 'Aspect ratio for images',
    },
    loading: {
      control: 'select',
      options: ['lazy', 'eager'],
      description: 'Loading behavior',
    },
    quality: {
      control: 'range',
      min: 1,
      max: 100,
      description: 'Image quality',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Gallery>;

// Sample images data
const sampleImages: GalleryImage[] = [
  {
    src: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&h=600&fit=crop',
    alt: 'Luxury villa exterior',
    title: 'Villa Exterior',
    caption: 'Beautiful modern villa with tropical garden',
    width: 800,
    height: 600,
  },
  {
    src: 'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=600&fit=crop',
    alt: 'Swimming pool',
    title: 'Private Pool',
    caption: 'Infinity pool overlooking rice fields',
    width: 800,
    height: 600,
  },
  {
    src: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop',
    alt: 'Living room',
    title: 'Living Area',
    caption: 'Spacious living room with modern furnishings',
    width: 800,
    height: 600,
  },
  {
    src: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop',
    alt: 'Bedroom',
    title: 'Master Bedroom',
    caption: 'Comfortable bedroom with garden view',
    width: 800,
    height: 600,
  },
  {
    src: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop',
    alt: 'Kitchen',
    title: 'Modern Kitchen',
    caption: 'Fully equipped kitchen with island',
    width: 800,
    height: 600,
  },
  {
    src: 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop',
    alt: 'Bathroom',
    title: 'Luxury Bathroom',
    caption: 'Spa-like bathroom with natural stone',
    width: 800,
    height: 600,
  },
];

const fewImages = sampleImages.slice(0, 3);
const manyImages = [
  ...sampleImages,
  {
    src: 'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&h=600&fit=crop',
    alt: 'Garden view',
    title: 'Tropical Garden',
    caption: 'Lush tropical landscaping',
    width: 800,
    height: 600,
  },
  {
    src: 'https://images.unsplash.com/photo-1520637836862-4d197d17c93a?w=800&h=600&fit=crop',
    alt: 'Dining area',
    title: 'Dining Room',
    caption: 'Elegant dining space for entertaining',
    width: 800,
    height: 600,
  },
];

// Basic Stories
export const Default: Story = {
  args: {
    images: sampleImages,
  },
};

export const WithoutCaptions: Story = {
  args: {
    images: sampleImages,
    showCaptions: false,
  },
};

export const WithoutLightbox: Story = {
  args: {
    images: sampleImages,
    lightbox: false,
  },
};

// Layout Variants
export const GridLayout: Story = {
  args: {
    images: sampleImages,
    layout: 'grid',
    columns: 3,
  },
};

export const MasonryLayout: Story = {
  args: {
    images: manyImages,
    layout: 'masonry',
  },
};

export const CarouselLayout: Story = {
  args: {
    images: sampleImages,
    layout: 'carousel',
  },
};

export const ThumbnailsLayout: Story = {
  args: {
    images: sampleImages,
    layout: 'thumbnails',
  },
};

// Grid Columns
export const TwoColumns: Story = {
  args: {
    images: sampleImages,
    layout: 'grid',
    columns: 2,
  },
};

export const FourColumns: Story = {
  args: {
    images: manyImages,
    layout: 'grid',
    columns: 4,
  },
};

export const SixColumns: Story = {
  args: {
    images: manyImages,
    layout: 'grid',
    columns: 6,
  },
};

// Gap Sizes
export const SmallGap: Story = {
  args: {
    images: sampleImages,
    gap: 'sm',
  },
};

export const LargeGap: Story = {
  args: {
    images: sampleImages,
    gap: 'lg',
  },
};

// Aspect Ratios
export const SquareAspectRatio: Story = {
  args: {
    images: sampleImages,
    aspectRatio: 'square',
  },
};

export const VideoAspectRatio: Story = {
  args: {
    images: sampleImages,
    aspectRatio: '16/9',
  },
};

export const AutoAspectRatio: Story = {
  args: {
    images: sampleImages,
    aspectRatio: 'auto',
  },
};

// Interactive Examples
export const WithClickHandler: Story = {
  args: {
    images: fewImages,
    lightbox: false,
    onImageClick: (image, index) => {
      alert(`Clicked on ${image.title} (index: ${index})`);
    },
  },
};

// Real Estate Examples
export const PropertyGallery: Story = {
  args: {
    images: sampleImages,
    layout: 'grid',
    columns: 3,
    aspectRatio: '4/3',
    showCaptions: true,
    lightbox: true,
    showThumbnails: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'A typical property gallery showing multiple rooms and areas of a villa.',
      },
    },
  },
};

export const PropertyHero: Story = {
  args: {
    images: sampleImages,
    layout: 'thumbnails',
    aspectRatio: '16/9',
    showCaptions: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Hero gallery layout with main image and thumbnail navigation.',
      },
    },
  },
};

export const PropertyCarousel: Story = {
  args: {
    images: sampleImages,
    layout: 'carousel',
    aspectRatio: '4/3',
    showCaptions: true,
    lightbox: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Carousel layout for showcasing property images in sequence.',
      },
    },
  },
};

// Edge Cases
export const SingleImage: Story = {
  args: {
    images: [sampleImages[0]],
  },
};

export const EmptyGallery: Story = {
  args: {
    images: [],
  },
};

// Performance Examples
export const LazyLoading: Story = {
  args: {
    images: manyImages,
    loading: 'lazy',
    quality: 75,
  },
};

export const EagerLoading: Story = {
  args: {
    images: fewImages,
    loading: 'eager',
    quality: 95,
  },
};

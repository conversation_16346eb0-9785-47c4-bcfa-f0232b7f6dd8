/**
 * Mobile Menu Component
 * Bali Property Scout Website
 * 
 * Enhanced mobile navigation with slide-in panel, smooth animations,
 * and backdrop blur for modern mobile experience.
 */

'use client';

import React, { useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { ChatbotCTA } from '@/components/ui/ChatbotCTA';

export interface MobileMenuProps {
  /** Whether mobile menu is open */
  isOpen: boolean;
  /** Function to close mobile menu */
  onClose: () => void;
  /** Navigation items */
  navigationItems: Array<{
    label: string;
    href: string;
    children?: Array<{
      label: string;
      href: string;
    }>;
  }>;
  /** Active dropdown state */
  activeDropdown: string | null;
  /** Function to handle dropdown toggle */
  onDropdownToggle: (label: string) => void;
}

export const MobileMenu: React.FC<MobileMenuProps> = ({
  isOpen,
  onClose,
  navigationItems,
  activeDropdown,
  onDropdownToggle,
}) => {
  const pathname = usePathname();

  // Close menu on route change
  useEffect(() => {
    if (isOpen) {
      onClose();
    }
  }, [pathname, isOpen, onClose]);

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Check if link is active
  const isActiveLink = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  return (
    <>
      {/* Backdrop */}
      <div
        className={cn(
          'fixed inset-0 bg-black/40 backdrop-blur-sm z-40 lg:hidden transition-opacity duration-300',
          isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        )}
        onClick={onClose}
        aria-hidden="true"
      />

      {/* Slide-in Panel */}
      <div
        className={cn(
          'fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-white/95 backdrop-blur-xl shadow-2xl z-50 lg:hidden',
          'transform transition-transform duration-300 ease-out',
          isOpen ? 'translate-x-0' : 'translate-x-full'
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200/50">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">
                BPS
              </span>
            </div>
            <span className="font-bold text-lg">Bali Property Scout</span>
          </div>
          
          <button
            onClick={onClose}
            className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
            aria-label="Close menu"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto p-6">
          <div className="space-y-2">
            {navigationItems.map((item) => (
              <div key={item.label}>
                {item.children ? (
                  // Dropdown item
                  <div>
                    <button
                      className={cn(
                        'flex items-center justify-between w-full px-4 py-3 text-left text-base font-medium rounded-lg transition-all duration-200',
                        isActiveLink(item.href)
                          ? 'text-primary bg-primary/10'
                          : 'text-gray-700 hover:text-primary hover:bg-gray-50'
                      )}
                      onClick={() => onDropdownToggle(item.label)}
                    >
                      <span>{item.label}</span>
                      <svg
                        className={cn(
                          'w-5 h-5 transition-transform duration-200',
                          activeDropdown === item.label && 'rotate-180'
                        )}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </button>

                    {/* Dropdown content */}
                    <div
                      className={cn(
                        'overflow-hidden transition-all duration-200',
                        activeDropdown === item.label ? 'max-h-64 mt-2' : 'max-h-0'
                      )}
                    >
                      <div className="pl-4 space-y-1">
                        {item.children.map((child) => (
                          <Link
                            key={child.href}
                            href={child.href}
                            className={cn(
                              'block px-4 py-2 text-sm rounded-md transition-colors',
                              isActiveLink(child.href)
                                ? 'text-primary bg-primary/10'
                                : 'text-gray-600 hover:text-primary hover:bg-gray-50'
                            )}
                            onClick={onClose}
                          >
                            {child.label}
                          </Link>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : (
                  // Regular link
                  <Link
                    href={item.href}
                    className={cn(
                      'block px-4 py-3 text-base font-medium rounded-lg transition-all duration-200',
                      isActiveLink(item.href)
                        ? 'text-primary bg-primary/10'
                        : 'text-gray-700 hover:text-primary hover:bg-gray-50'
                    )}
                    onClick={onClose}
                  >
                    {item.label}
                  </Link>
                )}
              </div>
            ))}
          </div>

          {/* Mobile CTA */}
          <div className="mt-8 pt-6 border-t border-gray-200/50">
            <ChatbotCTA
              query={{ type: 'general' }}
              variant="primary"
              size="lg"
              className="w-full font-semibold shadow-lg"
              onClick={onClose}
            >
              Find My Property
            </ChatbotCTA>
          </div>

          {/* Contact Info */}
          <div className="mt-6 pt-6 border-t border-gray-200/50 text-center">
            <p className="text-sm text-gray-600 mb-2">Need help? Contact us:</p>
            <a
              href="mailto:<EMAIL>"
              className="text-sm text-primary hover:text-primary/80 transition-colors"
            >
              <EMAIL>
            </a>
          </div>
        </nav>
      </div>
    </>
  );
};

export default MobileMenu;

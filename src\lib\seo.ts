/**
 * SEO Configuration and Utilities
 *
 * Centralized SEO management for the Bali Property Scout website
 */

import { DefaultSeoProps } from 'next-seo';

// Site configuration
export const SITE_CONFIG = {
  name: process.env.NEXT_PUBLIC_SITE_NAME || 'Bali Property Scout',
  url: process.env.NEXT_PUBLIC_SITE_URL || 'https://balipropertyscout.com',
  description: process.env.NEXT_PUBLIC_DEFAULT_SEO_DESCRIPTION || 'Find your perfect long-term rental or property in Bali. Expert guidance for expats, digital nomads, and investors.',
  contactEmail: process.env.NEXT_PUBLIC_CONTACT_EMAIL || '<EMAIL>',
  twitterHandle: process.env.NEXT_PUBLIC_TWITTER_HANDLE || '@BaliPropertyScout',
  chatbotUrl: process.env.NEXT_PUBLIC_CHATBOT_URL || 'https://app.balipropertyscout.com',
  locale: 'en_US',
  type: 'website',
};

// Default SEO configuration
export const DEFAULT_SEO: DefaultSeoProps = {
  title: SITE_CONFIG.name,
  description: SITE_CONFIG.description,
  canonical: SITE_CONFIG.url,
  openGraph: {
    type: 'website',
    locale: SITE_CONFIG.locale,
    url: SITE_CONFIG.url,
    siteName: SITE_CONFIG.name,
    title: SITE_CONFIG.name,
    description: SITE_CONFIG.description,
    images: [
      {
        url: `${SITE_CONFIG.url}/images/og-default.jpg`,
        width: 1200,
        height: 630,
        alt: `${SITE_CONFIG.name} - AI-Powered Bali Property Experts`,
        type: 'image/jpeg',
      },
    ],
  },
  twitter: {
    handle: SITE_CONFIG.twitterHandle,
    site: SITE_CONFIG.twitterHandle,
    cardType: 'summary_large_image',
  },
  additionalMetaTags: [
    {
      name: 'viewport',
      content: 'width=device-width, initial-scale=1',
    },
    {
      name: 'author',
      content: SITE_CONFIG.name,
    },
    {
      name: 'robots',
      content: 'index, follow',
    },
    {
      httpEquiv: 'x-ua-compatible',
      content: 'IE=edge',
    },
  ],
  additionalLinkTags: [
    {
      rel: 'icon',
      href: '/favicon.ico',
    },
    {
      rel: 'apple-touch-icon',
      href: '/apple-touch-icon.png',
      sizes: '180x180',
    },
    {
      rel: 'manifest',
      href: '/site.webmanifest',
    },
  ],
};

// SEO templates for different page types
export const SEO_TEMPLATES = {
  homepage: {
    title: 'Bali Property Scout - AI-Powered Property Search & Rentals',
    description: 'Find your perfect long-term rental or property in Bali with AI-powered search. Expert guidance for expats, digital nomads, and investors. Canggu, Seminyak, Ubud & more.',
    keywords: ['bali property scout', 'ai property search bali', 'property for sale bali', 'villa for sale bali', 'bali property investment', 'real estate agent bali'],
  },
  
  location: {
    title: (location: string) => `${location} Real Estate - Properties for Sale & Rent`,
    description: (location: string) => `Discover the best properties in ${location}, Bali. Villas, apartments, and land for sale or long-term rent. Expert local guidance.`,
    keywords: (location: string) => [`${location.toLowerCase()} property for sale`, `${location.toLowerCase()} villa for sale`, `${location.toLowerCase()} real estate`, `property ${location.toLowerCase()} bali`],
  },
  
  propertyType: {
    title: (type: string) => `${type}s for Sale in Bali - Premium Properties`,
    description: (type: string) => `Browse premium ${type.toLowerCase()}s for sale across Bali. From beachfront properties to mountain retreats. Professional guidance included.`,
    keywords: (type: string) => [`${type.toLowerCase()} for sale bali`, `bali ${type.toLowerCase()} investment`, `${type.toLowerCase()} property bali`],
  },
  
  contact: {
    title: 'Contact Us - Bali Real Estate Experts',
    description: 'Get in touch with our Bali real estate experts. Professional guidance for property purchases, rentals, and investments. Free consultation available.',
    keywords: ['bali real estate agent', 'property consultation bali', 'real estate advice bali'],
  },
};

// Structured data schemas
export const STRUCTURED_DATA = {
  organization: {
    '@context': 'https://schema.org',
    '@type': 'RealEstateAgent',
    name: SITE_CONFIG.name,
    url: SITE_CONFIG.url,
    logo: `${SITE_CONFIG.url}/images/logo.png`,
    description: SITE_CONFIG.description,
    email: SITE_CONFIG.contactEmail,
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'ID',
      addressRegion: 'Bali',
      addressLocality: 'Canggu',
    },
    areaServed: [
      {
        '@type': 'City',
        name: 'Canggu',
        addressCountry: 'ID',
      },
      {
        '@type': 'City',
        name: 'Seminyak',
        addressCountry: 'ID',
      },
      {
        '@type': 'City',
        name: 'Ubud',
        addressCountry: 'ID',
      },
      {
        '@type': 'City',
        name: 'Sanur',
        addressCountry: 'ID',
      },
    ],
    sameAs: [
      'https://www.facebook.com/BaliRealEstate',
      'https://www.instagram.com/BaliRealEstate',
      'https://twitter.com/BaliRealEstate',
    ],
  },

  website: {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: SITE_CONFIG.name,
    url: SITE_CONFIG.url,
    description: SITE_CONFIG.description,
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${SITE_CONFIG.url}/search?q={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
  },

  breadcrumbList: (items: Array<{ name: string; url: string }>) => ({
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  }),

  realEstateProperty: (property: {
    name: string;
    description: string;
    price: number;
    currency: string;
    location: string;
    propertyType: string;
    bedrooms?: number;
    bathrooms?: number;
    floorSize?: number;
    images: string[];
  }) => ({
    '@context': 'https://schema.org',
    '@type': 'RealEstateListing',
    name: property.name,
    description: property.description,
    url: `${SITE_CONFIG.url}/properties/${property.name.toLowerCase().replace(/\s+/g, '-')}`,
    offers: {
      '@type': 'Offer',
      price: property.price,
      priceCurrency: property.currency,
      availability: 'https://schema.org/InStock',
    },
    address: {
      '@type': 'PostalAddress',
      addressLocality: property.location,
      addressCountry: 'ID',
    },
    floorSize: property.floorSize ? {
      '@type': 'QuantitativeValue',
      value: property.floorSize,
      unitCode: 'MTK',
    } : undefined,
    numberOfRooms: property.bedrooms,
    numberOfBathroomsTotal: property.bathrooms,
    image: property.images,
  }),
};

// SEO utility functions
export function generatePageTitle(title: string, includesSiteName = true): string {
  if (includesSiteName) {
    return `${title} | ${SITE_CONFIG.name}`;
  }
  return title;
}

export function generateMetaDescription(description: string, maxLength = 160): string {
  if (description.length <= maxLength) {
    return description;
  }
  return description.substring(0, maxLength - 3) + '...';
}

export function generateKeywords(keywords: string[]): string {
  return keywords.join(', ');
}

export function generateCanonicalUrl(path: string): string {
  return `${SITE_CONFIG.url}${path.startsWith('/') ? path : `/${path}`}`;
}

export function generateOpenGraphImage(
  title: string,
  description?: string,
  image?: string
): string {
  if (image) {
    return image.startsWith('http') ? image : `${SITE_CONFIG.url}${image}`;
  }
  
  // Generate dynamic OG image URL (you can implement a service for this)
  const params = new URLSearchParams({
    title,
    description: description || '',
  });
  
  return `${SITE_CONFIG.url}/api/og?${params.toString()}`;
}

// Location-specific SEO data
export const LOCATION_SEO_DATA = {
  canggu: {
    title: 'Canggu Real Estate - Beachfront Villas & Properties for Sale',
    description: 'Discover premium beachfront villas and properties in Canggu, Bali. Surf lifestyle meets luxury living. Expert guidance for property investment.',
    keywords: ['canggu property for sale', 'canggu villa for sale', 'canggu real estate', 'beachfront property canggu'],
    localInfo: 'Known for its world-class surf breaks and vibrant expat community',
  },
  seminyak: {
    title: 'Seminyak Real Estate - Luxury Villas & Premium Properties',
    description: 'Luxury villas and premium properties in Seminyak, Bali. Upscale dining, shopping, and beach clubs. Professional real estate services.',
    keywords: ['seminyak villa for sale', 'luxury property seminyak', 'seminyak real estate', 'premium villa seminyak'],
    localInfo: 'Bali\'s most sophisticated beach destination with luxury amenities',
  },
  ubud: {
    title: 'Ubud Real Estate - Jungle Villas & Cultural Properties',
    description: 'Serene jungle villas and cultural properties in Ubud, Bali. Rice terraces, yoga retreats, and artistic community. Unique investment opportunities.',
    keywords: ['ubud real estate', 'jungle villa ubud', 'ubud property investment', 'rice field view property'],
    localInfo: 'Cultural heart of Bali surrounded by lush rice terraces and jungle',
  },
  sanur: {
    title: 'Sanur Real Estate - Family-Friendly Properties & Beachfront Homes',
    description: 'Family-friendly properties and beachfront homes in Sanur, Bali. Calm beaches, local culture, and established expat community.',
    keywords: ['sanur property investment', 'family home sanur', 'beachfront sanur', 'sanur real estate'],
    localInfo: 'Peaceful beachside town perfect for families and retirees',
  },
};

export default {
  SITE_CONFIG,
  DEFAULT_SEO,
  SEO_TEMPLATES,
  STRUCTURED_DATA,
  LOCATION_SEO_DATA,
};

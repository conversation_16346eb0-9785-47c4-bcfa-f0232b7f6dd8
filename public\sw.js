/**
 * Service Worker for Advanced Caching
 * 
 * Implements sophisticated caching strategies for optimal performance
 */

const CACHE_NAME = 'bali-real-estate-v1';
const STATIC_CACHE = 'static-v1';
const DYNAMIC_CACHE = 'dynamic-v1';
const IMAGE_CACHE = 'images-v1';
const API_CACHE = 'api-v1';

// Cache strategies configuration
const CACHE_STRATEGIES = {
  // Static assets - Cache First
  static: [
    '/_next/static/',
    '/images/',
    '/icons/',
    '/fonts/',
    '.css',
    '.js',
    '.woff',
    '.woff2',
  ],
  
  // API calls - Network First with fallback
  api: [
    '/api/',
  ],
  
  // Images - Cache First with network fallback
  images: [
    '.jpg',
    '.jpeg',
    '.png',
    '.webp',
    '.svg',
    '.gif',
  ],
  
  // Pages - Stale While Revalidate
  pages: [
    '/',
    '/locations/',
    '/contact',
    '/property-types/',
  ],
};

// Cache durations (in seconds)
const CACHE_DURATIONS = {
  static: 30 * 24 * 60 * 60,    // 30 days
  images: 7 * 24 * 60 * 60,     // 7 days
  api: 5 * 60,                  // 5 minutes
  pages: 24 * 60 * 60,          // 24 hours
  fallback: 60 * 60,            // 1 hour
};

// Install event - Cache essential resources
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE).then((cache) => {
      return cache.addAll([
        '/',
        '/offline',
        '/_next/static/css/',
        '/_next/static/js/',
        '/images/logo.png',
        '/images/placeholder.jpg',
      ].filter(Boolean));
    }).catch((error) => {
      console.error('Cache installation failed:', error);
    })
  );
  
  // Skip waiting to activate immediately
  self.skipWaiting();
});

// Activate event - Clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME && 
              cacheName !== STATIC_CACHE && 
              cacheName !== DYNAMIC_CACHE && 
              cacheName !== IMAGE_CACHE && 
              cacheName !== API_CACHE) {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  
  // Take control of all pages
  self.clients.claim();
});

// Fetch event - Implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return;
  }
  
  // Determine caching strategy based on request
  if (isStaticAsset(request.url)) {
    event.respondWith(cacheFirstStrategy(request, STATIC_CACHE));
  } else if (isImageRequest(request.url)) {
    event.respondWith(cacheFirstStrategy(request, IMAGE_CACHE));
  } else if (isAPIRequest(request.url)) {
    event.respondWith(networkFirstStrategy(request, API_CACHE));
  } else if (isPageRequest(request.url)) {
    event.respondWith(staleWhileRevalidateStrategy(request, DYNAMIC_CACHE));
  } else {
    // Default strategy for other requests
    event.respondWith(networkFirstStrategy(request, DYNAMIC_CACHE));
  }
});

// Cache First Strategy - Good for static assets
async function cacheFirstStrategy(request, cacheName) {
  try {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      // Check if cache is still fresh
      const cacheDate = new Date(cachedResponse.headers.get('date') || 0);
      const now = new Date();
      const age = (now.getTime() - cacheDate.getTime()) / 1000;
      
      if (age < CACHE_DURATIONS.static) {
        return cachedResponse;
      }
    }
    
    // Fetch from network
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Clone response before caching
      const responseClone = networkResponse.clone();
      await cache.put(request, responseClone);
    }
    
    return networkResponse;
  } catch (error) {
    console.error('Cache first strategy failed:', error);
    
    // Try to return cached version as fallback
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline fallback
    return getOfflineFallback(request);
  }
}

// Network First Strategy - Good for API calls
async function networkFirstStrategy(request, cacheName) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      const responseClone = networkResponse.clone();
      
      // Add cache headers
      const headers = new Headers(responseClone.headers);
      headers.set('sw-cache-date', new Date().toISOString());
      
      const cachedResponse = new Response(responseClone.body, {
        status: responseClone.status,
        statusText: responseClone.statusText,
        headers: headers,
      });
      
      await cache.put(request, cachedResponse);
    }
    
    return networkResponse;
  } catch (error) {
    console.error('Network first strategy failed:', error);
    
    // Fallback to cache
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline fallback
    return getOfflineFallback(request);
  }
}

// Stale While Revalidate Strategy - Good for pages
async function staleWhileRevalidateStrategy(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  // Fetch from network in background
  const networkResponsePromise = fetch(request).then((networkResponse) => {
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  }).catch((error) => {
    console.error('Background fetch failed:', error);
  });
  
  // Return cached version immediately if available
  if (cachedResponse) {
    return cachedResponse;
  }
  
  // Wait for network response if no cache
  try {
    return await networkResponsePromise;
  } catch (error) {
    return getOfflineFallback(request);
  }
}

// Helper functions
function isStaticAsset(url) {
  return CACHE_STRATEGIES.static.some(pattern => url.includes(pattern));
}

function isImageRequest(url) {
  return CACHE_STRATEGIES.images.some(ext => url.includes(ext));
}

function isAPIRequest(url) {
  return CACHE_STRATEGIES.api.some(pattern => url.includes(pattern));
}

function isPageRequest(url) {
  return CACHE_STRATEGIES.pages.some(pattern => url.includes(pattern)) ||
         (!url.includes('.') && !url.includes('/api/'));
}

function getOfflineFallback(request) {
  const url = new URL(request.url);
  
  if (isImageRequest(request.url)) {
    // Return placeholder image
    return caches.match('/images/placeholder.jpg');
  }
  
  if (isPageRequest(request.url)) {
    // Return offline page
    return caches.match('/offline');
  }
  
  if (isAPIRequest(request.url)) {
    // Return empty JSON response
    return new Response(JSON.stringify({ error: 'Offline', cached: false }), {
      status: 503,
      statusText: 'Service Unavailable',
      headers: { 'Content-Type': 'application/json' },
    });
  }
  
  // Generic offline response
  return new Response('Offline', {
    status: 503,
    statusText: 'Service Unavailable',
  });
}

// Background sync for failed requests
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

async function doBackgroundSync() {
  // Implement background sync logic for failed API calls
  console.log('Background sync triggered');
}

// Push notifications (if needed)
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();
    
    const options = {
      body: data.body,
      icon: '/images/icon-192.png',
      badge: '/images/badge-72.png',
      data: data.data,
      actions: data.actions,
    };
    
    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

// Notification click handler
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  if (event.action) {
    // Handle action clicks
    console.log('Notification action clicked:', event.action);
  } else {
    // Handle notification click
    event.waitUntil(
      clients.openWindow(event.notification.data?.url || '/')
    );
  }
});

// Message handler for communication with main thread
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_CACHE_STATS') {
    getCacheStats().then((stats) => {
      event.ports[0].postMessage(stats);
    });
  }
  
  if (event.data && event.data.type === 'CLEAR_CACHE') {
    clearAllCaches().then(() => {
      event.ports[0].postMessage({ success: true });
    });
  }
});

// Cache management functions
async function getCacheStats() {
  const cacheNames = await caches.keys();
  const stats = {};
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName);
    const keys = await cache.keys();
    stats[cacheName] = keys.length;
  }
  
  return stats;
}

async function clearAllCaches() {
  const cacheNames = await caches.keys();
  return Promise.all(cacheNames.map(cacheName => caches.delete(cacheName)));
}

console.log('Service Worker loaded successfully');

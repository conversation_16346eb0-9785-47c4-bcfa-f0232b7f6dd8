/**
 * Custom 404 Not Found Page
 * PRD Requirement: Custom 404 page with chatbot link
 */

import Link from 'next/link';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: '404 - Page Not Found | Bali Real Estate',
  description: 'This page is under construction. Use our chatbot to find properties or return to the homepage.',
  robots: 'noindex, nofollow',
};

export default function NotFound() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-emerald-50 to-teal-50 flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center">
        {/* 404 Illustration */}
        <div className="mb-8">
          <div className="text-8xl font-bold text-emerald-200 mb-4">404</div>
          <div className="w-32 h-32 mx-auto bg-emerald-100 rounded-full flex items-center justify-center">
            <svg 
              className="w-16 h-16 text-emerald-500" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 8h1m-1-4h1m4 4h1m-1-4h1" 
              />
            </svg>
          </div>
        </div>

        {/* PRD Compliant Content */}
        <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Page Under Construction
        </h1>
        
        <p className="text-xl text-gray-600 mb-8 leading-relaxed">
          This page is under construction. Use the chatbot to find properties or return to the homepage.
        </p>

        {/* PRD Compliant CTAs */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
          <Link
            href="https://app.balipropertyscout.com?query=general-consultation"
            className="bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl w-full sm:w-auto"
          >
            Find Properties with Chatbot
          </Link>
          
          <Link
            href="/"
            className="bg-white hover:bg-gray-50 text-emerald-600 border-2 border-emerald-600 hover:border-emerald-700 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 w-full sm:w-auto"
          >
            Return to Homepage
          </Link>
        </div>

        {/* Quick Navigation */}
        <div className="text-gray-500 text-sm">
          <p className="mb-4">Or explore these popular sections:</p>
          <div className="flex flex-wrap justify-center gap-4">
            <Link 
              href="/locations" 
              className="text-emerald-600 hover:text-emerald-700 underline underline-offset-2"
            >
              Browse Locations
            </Link>
            <Link 
              href="/property-types" 
              className="text-emerald-600 hover:text-emerald-700 underline underline-offset-2"
            >
              Property Types
            </Link>
            <Link 
              href="/services" 
              className="text-emerald-600 hover:text-emerald-700 underline underline-offset-2"
            >
              Our Services
            </Link>
            <Link 
              href="/contact" 
              className="text-emerald-600 hover:text-emerald-700 underline underline-offset-2"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </div>
    </main>
  );
}

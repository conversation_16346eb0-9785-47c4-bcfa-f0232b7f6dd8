#!/usr/bin/env node

/**
 * Vercel Build Script
 * Handles Payload CMS build issues by providing fallbacks
 */

const { execSync } = require('child_process');

console.log('🚀 Starting Vercel build process...');

// Set environment variables for build
process.env.SKIP_ENV_VALIDATION = 'true';
process.env.NODE_ENV = 'production';
process.env.BUILD_MODE = 'static'; // Flag to indicate we're in build mode

// Use SUPABASE_DATABASE_URL as primary, set DATABASE_URL as alias
if (process.env.SUPABASE_DATABASE_URL && !process.env.DATABASE_URL) {
  console.log('✅ Using SUPABASE_DATABASE_URL as DATABASE_URL');
  process.env.DATABASE_URL = process.env.SUPABASE_DATABASE_URL;
} else if (!process.env.DATABASE_URL && !process.env.SUPABASE_DATABASE_URL) {
  console.log('⚠️  No database URL found, using fallback for build...');
  process.env.DATABASE_URL = 'postgresql://localhost:5432/fallback';
  process.env.SUPABASE_DATABASE_URL = 'postgresql://localhost:5432/fallback';
}

// Provide fallback Payload secret if not set
if (!process.env.PAYLOAD_SECRET) {
  console.log('⚠️  No Payload secret found, using fallback for build...');
  process.env.PAYLOAD_SECRET = '7cc8b36cf86d42ad284f6fbb6b1eb85b473cbe8e22eb8d6df3c23f56f0e6c7e0';
}

try {
  console.log('📦 Running Next.js build...');
  // Try build without Payload config first for static generation
  execSync('next build', {
    stdio: 'inherit',
    env: process.env
  });
  console.log('✅ Build completed successfully!');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  console.log('🔄 Trying fallback build with Payload config...');
  try {
    execSync('cross-env PAYLOAD_CONFIG_PATH=payload.config.ts next build', {
      stdio: 'inherit',
      env: process.env
    });
    console.log('✅ Fallback build completed successfully!');
  } catch (fallbackError) {
    console.error('❌ Fallback build also failed:', fallbackError.message);
    process.exit(1);
  }
}

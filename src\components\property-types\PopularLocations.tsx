/**
 * Popular Locations Component
 * Bali Property Scout Website
 * 
 * Location cards for property type pages showing popular areas
 * for villas, guesthouses, etc. with links to location pages
 * and property-specific information.
 */

'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import ChatbotCTA from '@/components/ui/ChatbotCTA';

export interface LocationInfo {
  id: string;
  name: string;
  slug: string;
  description: string;
  priceRange: string;
  propertyCount: string;
  imageUrl: string;
  imageAlt: string;
  highlights: string[];
  bestFor: string[];
  averageRental?: string;
  investmentPotential?: 'high' | 'medium' | 'low';
}

export interface PopularLocationsProps {
  /** Property type (villa, guesthouse, apartment) */
  propertyType: string;
  /** Section title */
  title?: string;
  /** Section description */
  description?: string;
  /** Locations to display */
  locations: LocationInfo[];
  /** Additional CSS classes */
  className?: string;
}

export const PopularLocations: React.FC<PopularLocationsProps> = ({
  propertyType,
  title,
  description,
  locations,
  className,
}) => {
  const getInvestmentBadge = (potential?: 'high' | 'medium' | 'low') => {
    if (!potential) return null;
    
    const badges = {
      high: { text: 'High ROI', style: 'bg-green-500 text-white' },
      medium: { text: 'Good ROI', style: 'bg-yellow-500 text-white' },
      low: { text: 'Stable', style: 'bg-blue-500 text-white' }
    };
    
    return badges[potential];
  };

  return (
    <section id="popular-locations" className={cn('section-spacing bg-white', className)}>
      <div className="max-w-7xl mx-auto container-padding">
        {/* Section Header */}
        <div className="text-center mb-16">
          {title && (
            <h2 className="heading-2 text-3xl md:text-4xl font-bold mb-6" style={{ color: 'var(--neutral-900)' }}>
              {title}
            </h2>
          )}
          {description && (
            <p className="body-text-large text-lg max-w-3xl mx-auto leading-relaxed" style={{ color: 'var(--neutral-600)' }}>
              {description}
            </p>
          )}
        </div>

        {/* Location Cards Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {locations.map((location) => {
            const investmentBadge = getInvestmentBadge(location.investmentPotential);
            
            return (
              <div
                key={location.id}
                className="card group hover:shadow-2xl transition-all duration-300 hover:scale-105 overflow-hidden"
              >
                {/* Location Image */}
                <div className="relative h-48 mb-6 rounded-xl overflow-hidden">
                  <Image
                    src={location.imageUrl}
                    alt={location.imageAlt}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-110"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                  
                  {/* Price Range Overlay */}
                  <div className="absolute top-4 right-4">
                    <div 
                      className="px-3 py-2 rounded-lg font-semibold text-sm shadow-lg backdrop-blur-md"
                      style={{ 
                        backgroundColor: 'var(--brand-primary)', 
                        color: 'var(--neutral-white)' 
                      }}
                    >
                      {location.priceRange}
                    </div>
                  </div>

                  {/* Investment Badge */}
                  {investmentBadge && (
                    <div className="absolute top-4 left-4">
                      <div className={cn('px-3 py-1.5 rounded-lg font-medium text-xs', investmentBadge.style)}>
                        {investmentBadge.text}
                      </div>
                    </div>
                  )}

                  {/* Property Count */}
                  <div className="absolute bottom-4 left-4">
                    <div className="px-3 py-1.5 bg-black/70 backdrop-blur-md text-white rounded-lg text-sm font-medium">
                      {location.propertyCount} {propertyType}s available
                    </div>
                  </div>
                </div>

                {/* Location Content */}
                <div className="space-y-4">
                  {/* Title & Description */}
                  <div>
                    <h3 className="heading-3 text-xl font-bold mb-2" style={{ color: 'var(--neutral-900)' }}>
                      {location.name}
                    </h3>
                    <p className="body-text text-sm leading-relaxed" style={{ color: 'var(--neutral-600)' }}>
                      {location.description}
                    </p>
                  </div>

                  {/* Key Highlights */}
                  <div>
                    <h4 className="font-semibold text-sm mb-2" style={{ color: 'var(--neutral-800)' }}>
                      Key Highlights:
                    </h4>
                    <div className="space-y-1">
                      {location.highlights.slice(0, 3).map((highlight, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <div 
                            className="w-1.5 h-1.5 rounded-full flex-shrink-0"
                            style={{ backgroundColor: 'var(--brand-primary)' }}
                          ></div>
                          <span className="text-xs" style={{ color: 'var(--neutral-600)' }}>
                            {highlight}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Best For Tags */}
                  <div>
                    <h4 className="font-semibold text-sm mb-2" style={{ color: 'var(--neutral-800)' }}>
                      Best For:
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {location.bestFor.map((tag, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 rounded-md text-xs font-medium border"
                          style={{
                            backgroundColor: 'var(--brand-primary-bg)',
                            color: 'var(--brand-primary)',
                            borderColor: 'var(--brand-primary-light)'
                          }}
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Average Rental (if available) */}
                  {location.averageRental && (
                    <div className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg">
                      <span className="text-sm font-medium" style={{ color: 'var(--neutral-700)' }}>
                        Avg. {propertyType} rental:
                      </span>
                      <span className="text-sm font-bold" style={{ color: 'var(--brand-primary)' }}>
                        {location.averageRental}
                      </span>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-3 pt-4 border-t" style={{ borderColor: 'var(--neutral-200)' }}>
                    <Link
                      href={`/locations/${location.slug}`}
                      className="btn-secondary flex-1 text-center text-sm font-medium px-4 py-3 transition-all duration-300 hover:scale-105"
                      style={{
                        borderColor: 'var(--brand-primary)',
                        color: 'var(--brand-primary)'
                      }}
                    >
                      Learn More
                    </Link>
                    
                    <ChatbotCTA
                      query={{ 
                        type: 'location-property-search', 
                        location: location.name,
                        propertyType: propertyType,
                        context: 'popular-locations'
                      }}
                      variant="primary"
                      className="btn-primary flex-1 text-center text-sm font-semibold px-4 py-3 transition-all duration-300 hover:scale-105"
                      style={{
                        backgroundColor: 'var(--brand-primary)',
                        borderColor: 'var(--brand-primary)',
                        color: 'var(--neutral-white)'
                      }}
                    >
                      Find {propertyType.charAt(0).toUpperCase() + propertyType.slice(1)}s
                    </ChatbotCTA>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Bottom CTA Section */}
        <div className="text-center">
          <div className="card max-w-2xl mx-auto">
            <h3 className="heading-3 text-xl font-bold mb-4" style={{ color: 'var(--neutral-900)' }}>
              Explore All Bali Locations
            </h3>
            <p className="body-text mb-6" style={{ color: 'var(--neutral-600)' }}>
              Discover {propertyType}s in every corner of Bali. From bustling beach towns to peaceful mountain villages, 
              find your perfect location with our comprehensive area guide.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/locations"
                className="btn-primary text-lg font-semibold px-8 py-4"
                style={{
                  backgroundColor: 'var(--brand-primary)',
                  borderColor: 'var(--brand-primary)',
                  color: 'var(--neutral-white)'
                }}
              >
                View All Locations
              </Link>
              
              <ChatbotCTA
                query={{ 
                  type: 'location-recommendation', 
                  propertyType: propertyType,
                  context: 'popular-locations-all'
                }}
                variant="secondary"
                className="btn-secondary text-base font-medium px-6 py-3"
                style={{
                  borderColor: 'var(--brand-primary)',
                  color: 'var(--brand-primary)'
                }}
              >
                Get Location Recommendations
              </ChatbotCTA>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PopularLocations;

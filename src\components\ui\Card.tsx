/**
 * Card Component
 * 
 * A flexible card component for displaying content in a contained format.
 * Supports various layouts, padding options, and interactive states.
 */

import React from 'react';
import { cn } from '@/lib/utils';
import { OptimizedThumbnail } from './OptimizedImage';

export type CardVariant = 'default' | 'outlined' | 'elevated' | 'filled';
export type CardPadding = 'none' | 'sm' | 'md' | 'lg' | 'xl';

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Card visual variant */
  variant?: CardVariant;
  /** Card padding size */
  padding?: CardPadding;
  /** Whether card is interactive (hover effects) */
  interactive?: boolean;
  /** Whether card is disabled */
  disabled?: boolean;
  /** Custom className */
  className?: string;
  /** Card content */
  children?: React.ReactNode;
}

// Card variant styles
const cardVariants = {
  default: [
    'bg-white border border-neutral-200',
    'shadow-sm',
  ].join(' '),
  
  outlined: [
    'bg-white border-2 border-neutral-300',
  ].join(' '),
  
  elevated: [
    'bg-white border border-neutral-100',
    'shadow-lg',
  ].join(' '),
  
  filled: [
    'bg-neutral-50 border border-neutral-200',
  ].join(' '),
};

// Card padding styles
const cardPadding = {
  none: 'p-0',
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8',
  xl: 'p-10',
};

// Interactive styles
const interactiveStyles = [
  'cursor-pointer transition-all duration-200',
  'hover:shadow-md hover:border-neutral-300',
  'active:scale-[0.98]',
].join(' ');

export const Card = React.forwardRef<HTMLDivElement, CardProps>(
  (
    {
      variant = 'default',
      padding = 'md',
      interactive = false,
      disabled = false,
      className,
      children,
      ...props
    },
    ref
  ) => {
    return (
      <div
        ref={ref}
        className={cn(
          // Base styles
          'rounded-lg overflow-hidden',
          
          // Variant styles
          cardVariants[variant],
          
          // Padding styles
          cardPadding[padding],
          
          // Interactive styles
          interactive && !disabled && interactiveStyles,
          
          // Disabled styles
          disabled && 'opacity-60 cursor-not-allowed',
          
          // Custom className
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Card.displayName = 'Card';

// Card Header component
export interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  subtitle?: string;
  action?: React.ReactNode;
  className?: string;
  children?: React.ReactNode;
}

export const CardHeader: React.FC<CardHeaderProps> = ({
  title,
  subtitle,
  action,
  className,
  children,
  ...props
}) => {
  return (
    <div
      className={cn(
        'flex items-start justify-between',
        'pb-4 border-b border-neutral-200',
        className
      )}
      {...props}
    >
      <div className="flex-1 min-w-0">
        {title && (
          <h3 className="text-lg font-semibold text-neutral-900 truncate">
            {title}
          </h3>
        )}
        {subtitle && (
          <p className="mt-1 text-sm text-neutral-600 truncate">
            {subtitle}
          </p>
        )}
        {children}
      </div>
      {action && (
        <div className="flex-shrink-0 ml-4">
          {action}
        </div>
      )}
    </div>
  );
};

// Card Body component
export interface CardBodyProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
  children?: React.ReactNode;
}

export const CardBody: React.FC<CardBodyProps> = ({
  className,
  children,
  ...props
}) => {
  return (
    <div
      className={cn('py-4', className)}
      {...props}
    >
      {children}
    </div>
  );
};

// Card Footer component
export interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
  children?: React.ReactNode;
}

export const CardFooter: React.FC<CardFooterProps> = ({
  className,
  children,
  ...props
}) => {
  return (
    <div
      className={cn(
        'pt-4 border-t border-neutral-200',
        'flex items-center justify-between',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

// Property Card - specialized card for property listings
export interface PropertyCardProps extends Omit<CardProps, 'children'> {
  image?: string;
  imageAlt?: string;
  title: string;
  location: string;
  price: string;
  priceLabel?: string;
  features?: string[];
  onImageClick?: () => void;
  onCardClick?: () => void;
}

export const PropertyCard: React.FC<PropertyCardProps> = ({
  image,
  imageAlt,
  title,
  location,
  price,
  priceLabel = 'From',
  features = [],
  onImageClick,
  onCardClick,
  className,
  ...cardProps
}) => {
  return (
    <Card
      variant="elevated"
      padding="none"
      interactive={!!onCardClick}
      className={cn('overflow-hidden', className)}
      onClick={onCardClick}
      {...cardProps}
    >
      {/* Property Image */}
      {image && (
        <button
          className="relative aspect-[4/3] overflow-hidden cursor-pointer w-full"
          onClick={onImageClick}
          aria-label={`View image for ${title}`}
        >
          <OptimizedThumbnail
            src={image}
            alt={imageAlt || title}
            width={400}
            height={300}
            className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
            fill
            objectFit="cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
        </button>
      )}
      
      {/* Property Details */}
      <div className="p-6">
        <div className="mb-3">
          <h3 className="text-lg font-semibold text-neutral-900 mb-1 line-clamp-2">
            {title}
          </h3>
          <p className="text-sm text-neutral-600 flex items-center">
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
            </svg>
            {location}
          </p>
        </div>
        
        {/* Price */}
        <div className="mb-4">
          <div className="flex items-baseline">
            <span className="text-xs text-neutral-500 mr-1">{priceLabel}</span>
            <span className="text-xl font-bold text-primary-600">{price}</span>
          </div>
        </div>
        
        {/* Features */}
        {features.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {features.slice(0, 3).map((feature, index) => (
              <span
                key={index}
                className="px-2 py-1 text-xs font-medium bg-neutral-100 text-neutral-700 rounded-full"
              >
                {feature}
              </span>
            ))}
            {features.length > 3 && (
              <span className="px-2 py-1 text-xs font-medium bg-neutral-100 text-neutral-700 rounded-full">
                +{features.length - 3} more
              </span>
            )}
          </div>
        )}
      </div>
    </Card>
  );
};

// Location Card - specialized card for location information
export interface LocationCardProps extends Omit<CardProps, 'children'> {
  image?: string;
  imageAlt?: string;
  name: string;
  description: string;
  propertyCount?: number;
  priceRange?: string;
  onCardClick?: () => void;
}

export const LocationCard: React.FC<LocationCardProps> = ({
  image,
  imageAlt,
  name,
  description,
  propertyCount,
  priceRange,
  onCardClick,
  className,
  ...cardProps
}) => {
  return (
    <Card
      variant="elevated"
      padding="none"
      interactive={!!onCardClick}
      className={cn('overflow-hidden', className)}
      onClick={onCardClick}
      {...cardProps}
    >
      {/* Location Image */}
      {image && (
        <div className="relative aspect-[16/9] overflow-hidden">
          <OptimizedThumbnail
            src={image}
            alt={imageAlt || name}
            width={600}
            height={338}
            className="w-full h-full object-cover"
            fill
            objectFit="cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent" />
          <div className="absolute bottom-4 left-4 text-white">
            <h3 className="text-xl font-bold mb-1">{name}</h3>
            {propertyCount && (
              <p className="text-sm opacity-90">
                {propertyCount} properties available
              </p>
            )}
          </div>
        </div>
      )}
      
      {/* Location Details */}
      <div className="p-6">
        {!image && (
          <h3 className="text-lg font-semibold text-neutral-900 mb-2">
            {name}
          </h3>
        )}
        
        <p className="text-neutral-600 text-sm mb-4 line-clamp-3">
          {description}
        </p>
        
        {priceRange && (
          <div className="flex items-center justify-between text-sm">
            <span className="text-neutral-500">Price range:</span>
            <span className="font-semibold text-primary-600">{priceRange}</span>
          </div>
        )}
      </div>
    </Card>
  );
};

/**
 * Floating Chat Widget Component
 * Bali Property Scout Website
 * 
 * Floating AI chat bubble for quick questions on contact page.
 * Features accessibility, non-obtrusive design, and smooth animations.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

export interface FloatingChatWidgetProps {
  /** Show only on contact page */
  showOnContactPage?: boolean;
  /** Custom positioning */
  position?: 'bottom-right' | 'bottom-left';
  /** Additional CSS classes */
  className?: string;
}

export const FloatingChatWidget: React.FC<FloatingChatWidgetProps> = ({
  showOnContactPage = true,
  position = 'bottom-right',
  className,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);

  useEffect(() => {
    // Show widget after a delay to avoid being intrusive
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Auto-expand after being visible for a while (if not interacted)
    if (isVisible && !hasInteracted) {
      const expandTimer = setTimeout(() => {
        setIsExpanded(true);
      }, 5000);

      return () => clearTimeout(expandTimer);
    }
  }, [isVisible, hasInteracted]);

  const handleChatClick = () => {
    setHasInteracted(true);
    // Open chatbot with contact page context
    const chatbotUrl = 'https://app.bali-realestate.com/chatbot?query=contact-page-quick-question';
    window.open(chatbotUrl, '_blank');
  };

  const handleExpand = () => {
    setHasInteracted(true);
    setIsExpanded(!isExpanded);
  };

  const handleClose = () => {
    setHasInteracted(true);
    setIsExpanded(false);
  };

  if (!isVisible) return null;

  const positionClasses = {
    'bottom-right': 'bottom-6 right-6',
    'bottom-left': 'bottom-6 left-6'
  };

  return (
    <div
      className={cn(
        'fixed z-50 transition-all duration-500 ease-out',
        positionClasses[position],
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4',
        className
      )}
      role="complementary"
      aria-label="AI Chat Assistant"
    >
      {/* Expanded Chat Preview */}
      {isExpanded && (
        <div className="mb-4 bg-white rounded-2xl shadow-2xl border border-gray-200 p-6 max-w-sm animate-in slide-in-from-bottom-2 duration-300">
          {/* Close Button */}
          <button
            onClick={handleClose}
            className="absolute top-3 right-3 text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="Close chat preview"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* Chat Preview Content */}
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-blue-500 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900">AI Property Assistant</h4>
                <p className="text-sm text-green-600 flex items-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                  Online now
                </p>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-3">
              <p className="text-sm text-gray-700 leading-relaxed">
                👋 Hi! I'm here to help with quick questions about Bali properties. 
                What would you like to know?
              </p>
            </div>

            <div className="space-y-2">
              <p className="text-xs text-gray-500 font-medium">Quick questions I can help with:</p>
              <div className="flex flex-wrap gap-2">
                {[
                  'Property prices',
                  'Best locations',
                  'Rental process',
                  'Investment advice'
                ].map((topic, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-emerald-100 text-emerald-700 rounded-md text-xs font-medium"
                  >
                    {topic}
                  </span>
                ))}
              </div>
            </div>

            <button
              onClick={handleChatClick}
              className="w-full bg-gradient-to-r from-emerald-600 to-blue-600 hover:from-emerald-700 hover:to-blue-700 text-white px-4 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl"
            >
              Start Chat
            </button>
          </div>
        </div>
      )}

      {/* Main Chat Button */}
      <div className="relative">
        {/* Pulse Animation Ring */}
        {!hasInteracted && (
          <div className="absolute inset-0 bg-emerald-500 rounded-full animate-ping opacity-20"></div>
        )}
        
        {/* Main Button */}
        <button
          onClick={isExpanded ? handleChatClick : handleExpand}
          className={cn(
            'relative w-16 h-16 bg-gradient-to-br from-emerald-500 to-blue-500 hover:from-emerald-600 hover:to-blue-600 text-white rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 hover:scale-110 focus:outline-none focus:ring-4 focus:ring-emerald-200',
            'flex items-center justify-center group'
          )}
          aria-label={isExpanded ? 'Start AI chat' : 'Open chat preview'}
          aria-expanded={isExpanded}
        >
          {/* Chat Icon */}
          <svg 
            className={cn(
              'w-6 h-6 transition-transform duration-300',
              isExpanded ? 'scale-110' : 'group-hover:scale-110'
            )} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" 
            />
          </svg>

          {/* Notification Badge */}
          {!hasInteracted && (
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
              <span className="text-xs text-white font-bold">!</span>
            </div>
          )}
        </button>

        {/* Tooltip */}
        {!isExpanded && (
          <div className="absolute bottom-full right-0 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
            <div className="bg-gray-900 text-white text-sm px-3 py-2 rounded-lg whitespace-nowrap">
              Quick questions? Ask our AI!
              <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
            </div>
          </div>
        )}
      </div>

      {/* Screen Reader Only Text */}
      <div className="sr-only">
        AI chat assistant available for quick property questions. 
        Click to {isExpanded ? 'start chatting' : 'see preview'}.
      </div>
    </div>
  );
};

export default FloatingChatWidget;

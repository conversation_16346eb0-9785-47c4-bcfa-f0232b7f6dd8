/**
 * FAQ Component Tests
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { FAQ, FAQBordered, FAQMinimal } from '../FAQ';
import type { FAQItem } from '../FAQ';

// Mock FAQ data
const mockFAQItems: FAQItem[] = [
  {
    id: '1',
    question: 'What is the rental process?',
    answer: 'The rental process involves viewing properties, submitting applications, and signing contracts.',
    category: 'Rentals',
    tags: ['rental', 'process'],
    defaultExpanded: false,
  },
  {
    id: '2',
    question: 'How much is the security deposit?',
    answer: 'Security deposits typically range from 1-3 months rent depending on the property.',
    category: 'Rentals',
    tags: ['deposit', 'security'],
    defaultExpanded: true,
  },
  {
    id: '3',
    question: 'Can foreigners buy property in Bali?',
    answer: 'Foreigners can buy property through various legal structures including leasehold and nominee arrangements.',
    category: 'Buying',
    tags: ['foreigner', 'legal', 'buying'],
    defaultExpanded: false,
  },
  {
    id: '4',
    question: 'What are the best areas to invest?',
    answer: 'Popular investment areas include Canggu, Seminyak, and Ubud due to their tourism appeal.',
    category: 'Investment',
    tags: ['investment', 'areas', 'location'],
    defaultExpanded: false,
  },
];

describe('FAQ Component', () => {
  describe('Basic Rendering', () => {
    it('renders FAQ items correctly', () => {
      render(<FAQ items={mockFAQItems} />);
      
      expect(screen.getByText('What is the rental process?')).toBeInTheDocument();
      expect(screen.getByText('How much is the security deposit?')).toBeInTheDocument();
      expect(screen.getByText('Can foreigners buy property in Bali?')).toBeInTheDocument();
      expect(screen.getByText('What are the best areas to invest?')).toBeInTheDocument();
    });

    it('renders empty FAQ list gracefully', () => {
      render(<FAQ items={[]} />);
      expect(screen.getByText('No FAQs found matching your search.')).toBeInTheDocument();
    });

    it('shows default expanded items', () => {
      render(<FAQ items={mockFAQItems} />);
      
      // Item with defaultExpanded: true should show its answer
      expect(screen.getByText('Security deposits typically range from 1-3 months rent depending on the property.')).toBeInTheDocument();
    });
  });

  describe('Search Functionality', () => {
    it('shows search input by default', () => {
      render(<FAQ items={mockFAQItems} />);
      expect(screen.getByPlaceholderText('Search FAQs...')).toBeInTheDocument();
    });

    it('hides search input when searchable is false', () => {
      render(<FAQ items={mockFAQItems} searchable={false} />);
      expect(screen.queryByPlaceholderText('Search FAQs...')).not.toBeInTheDocument();
    });

    it('filters items by question text', async () => {
      const user = userEvent.setup();
      render(<FAQ items={mockFAQItems} />);
      
      const searchInput = screen.getByPlaceholderText('Search FAQs...');
      await user.type(searchInput, 'rental');
      
      expect(screen.getByText('What is the rental process?')).toBeInTheDocument();
      expect(screen.queryByText('Can foreigners buy property in Bali?')).not.toBeInTheDocument();
    });

    it('filters items by answer text', async () => {
      const user = userEvent.setup();
      render(<FAQ items={mockFAQItems} />);
      
      const searchInput = screen.getByPlaceholderText('Search FAQs...');
      await user.type(searchInput, 'leasehold');
      
      expect(screen.getByText('Can foreigners buy property in Bali?')).toBeInTheDocument();
      expect(screen.queryByText('What is the rental process?')).not.toBeInTheDocument();
    });

    it('filters items by tags', async () => {
      const user = userEvent.setup();
      render(<FAQ items={mockFAQItems} />);
      
      const searchInput = screen.getByPlaceholderText('Search FAQs...');
      await user.type(searchInput, 'investment');
      
      expect(screen.getByText('What are the best areas to invest?')).toBeInTheDocument();
      expect(screen.queryByText('What is the rental process?')).not.toBeInTheDocument();
    });

    it('shows no results message when no matches found', async () => {
      const user = userEvent.setup();
      render(<FAQ items={mockFAQItems} />);
      
      const searchInput = screen.getByPlaceholderText('Search FAQs...');
      await user.type(searchInput, 'nonexistent');
      
      expect(screen.getByText('No FAQs found matching your search.')).toBeInTheDocument();
    });

    it('uses custom search placeholder', () => {
      render(<FAQ items={mockFAQItems} searchPlaceholder="Custom search..." />);
      expect(screen.getByPlaceholderText('Custom search...')).toBeInTheDocument();
    });

    it('uses custom no results message', async () => {
      const user = userEvent.setup();
      render(<FAQ items={mockFAQItems} noResultsMessage="Custom no results message" />);
      
      const searchInput = screen.getByPlaceholderText('Search FAQs...');
      await user.type(searchInput, 'nonexistent');
      
      expect(screen.getByText('Custom no results message')).toBeInTheDocument();
    });
  });

  describe('Category Functionality', () => {
    it('shows category filter by default', () => {
      render(<FAQ items={mockFAQItems} />);

      expect(screen.getByText('All')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Rentals' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Buying' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Investment' })).toBeInTheDocument();
    });

    it('hides category filter when showCategories is false', () => {
      render(<FAQ items={mockFAQItems} showCategories={false} />);
      
      expect(screen.queryByText('All')).not.toBeInTheDocument();
      expect(screen.queryByText('Rentals')).not.toBeInTheDocument();
    });

    it('filters items by category', async () => {
      const user = userEvent.setup();
      render(<FAQ items={mockFAQItems} />);

      await user.click(screen.getByRole('button', { name: 'Rentals' }));

      expect(screen.getByText('What is the rental process?')).toBeInTheDocument();
      expect(screen.getByText('How much is the security deposit?')).toBeInTheDocument();
      expect(screen.queryByText('Can foreigners buy property in Bali?')).not.toBeInTheDocument();
    });

    it('shows all items when "All" category is selected', async () => {
      const user = userEvent.setup();
      render(<FAQ items={mockFAQItems} />);
      
      // First select a specific category
      await user.click(screen.getByRole('button', { name: 'Buying' }));
      expect(screen.queryByText('What is the rental process?')).not.toBeInTheDocument();
      
      // Then select "All"
      await user.click(screen.getByText('All'));
      expect(screen.getByText('What is the rental process?')).toBeInTheDocument();
    });

    it('groups items by category when showCategories is true', () => {
      render(<FAQ items={mockFAQItems} />);

      // Should show category headers (as headings, not buttons)
      expect(screen.getByRole('heading', { name: 'Rentals' })).toBeInTheDocument();
      expect(screen.getByRole('heading', { name: 'Buying' })).toBeInTheDocument();
      expect(screen.getByRole('heading', { name: 'Investment' })).toBeInTheDocument();
    });
  });

  describe('Expand/Collapse Functionality', () => {
    it('expands item when question is clicked', async () => {
      const user = userEvent.setup();
      render(<FAQ items={mockFAQItems} />);
      
      await user.click(screen.getByText('What is the rental process?'));
      
      expect(screen.getByText('The rental process involves viewing properties, submitting applications, and signing contracts.')).toBeInTheDocument();
    });

    it('collapses expanded item when clicked again', async () => {
      const user = userEvent.setup();
      const onToggle = vi.fn();
      render(<FAQ items={mockFAQItems} onToggle={onToggle} />);

      // First expand
      await user.click(screen.getByText('How much is the security deposit?'));
      expect(onToggle).toHaveBeenCalledWith('2', false); // false because it was defaultExpanded: true

      // Then collapse
      await user.click(screen.getByText('How much is the security deposit?'));
      expect(onToggle).toHaveBeenCalledWith('2', true); // true because we're expanding it again
    });

    it('allows multiple items expanded when allowMultiple is true', async () => {
      const user = userEvent.setup();
      render(<FAQ items={mockFAQItems} allowMultiple={true} />);
      
      await user.click(screen.getByText('What is the rental process?'));
      await user.click(screen.getByText('Can foreigners buy property in Bali?'));
      
      expect(screen.getByText('The rental process involves viewing properties, submitting applications, and signing contracts.')).toBeInTheDocument();
      expect(screen.getByText('Foreigners can buy property through various legal structures including leasehold and nominee arrangements.')).toBeInTheDocument();
    });

    it('collapses other items when allowMultiple is false', async () => {
      const user = userEvent.setup();
      const onToggle = vi.fn();
      render(<FAQ items={mockFAQItems} allowMultiple={false} onToggle={onToggle} />);

      // First expand one item
      await user.click(screen.getByText('What is the rental process?'));
      expect(onToggle).toHaveBeenCalledWith('1', true); // First item expanded

      // Then expand another item
      await user.click(screen.getByText('Can foreigners buy property in Bali?'));
      expect(onToggle).toHaveBeenCalledWith('3', true); // Second item expanded

      // Second item should be expanded
      expect(screen.getByText('Foreigners can buy property through various legal structures including leasehold and nominee arrangements.')).toBeInTheDocument();
    });

    it('calls onToggle callback when item is toggled', async () => {
      const user = userEvent.setup();
      const onToggle = vi.fn();
      render(<FAQ items={mockFAQItems} onToggle={onToggle} />);
      
      await user.click(screen.getByText('What is the rental process?'));
      
      expect(onToggle).toHaveBeenCalledWith('1', true);
    });
  });

  describe('Markdown Support', () => {
    const markdownFAQ: FAQItem[] = [
      {
        id: '1',
        question: 'Markdown Question',
        answer: '**Bold text** and *italic text*',
        category: 'Test',
      },
    ];

    it('renders markdown when enabled', async () => {
      const user = userEvent.setup();
      render(<FAQ items={markdownFAQ} markdown={true} />);
      
      await user.click(screen.getByText('Markdown Question'));
      
      expect(screen.getByText('Bold text')).toBeInTheDocument();
      expect(screen.getByText('italic text')).toBeInTheDocument();
    });

    it('does not render markdown when disabled', async () => {
      const user = userEvent.setup();
      render(<FAQ items={markdownFAQ} markdown={false} />);
      
      await user.click(screen.getByText('Markdown Question'));
      
      expect(screen.getByText('**Bold text** and *italic text*')).toBeInTheDocument();
    });
  });

  describe('Variant Styles', () => {
    it('applies default variant styles', () => {
      const { container } = render(<FAQ items={mockFAQItems} />);
      // Look for the FAQ items container (after search and category filters)
      const faqItemsContainer = container.querySelector('.space-y-8');
      expect(faqItemsContainer).toBeInTheDocument();
    });

    it('applies bordered variant styles', () => {
      const { container } = render(<FAQ items={mockFAQItems} variant="bordered" />);
      // Look for bordered variant specific classes
      const borderedContainer = container.querySelector('.divide-y.divide-neutral-200');
      expect(borderedContainer).toBeInTheDocument();
    });

    it('applies minimal variant styles', () => {
      const { container } = render(<FAQ items={mockFAQItems} variant="minimal" />);
      // Look for minimal variant specific classes
      const minimalContainer = container.querySelector('.space-y-2');
      expect(minimalContainer).toBeInTheDocument();
    });
  });

  describe('Custom ClassName', () => {
    it('applies custom className', () => {
      const { container } = render(
        <FAQ items={mockFAQItems} className="custom-faq" />
      );
      
      expect(container.querySelector('.faq-component')).toHaveClass('custom-faq');
    });
  });
});

describe('FAQ Variants', () => {
  it('FAQBordered applies bordered variant', () => {
    const { container } = render(<FAQBordered items={mockFAQItems} />);
    const borderedContainer = container.querySelector('.divide-y.divide-neutral-200');
    expect(borderedContainer).toBeInTheDocument();
  });

  it('FAQMinimal applies minimal variant', () => {
    const { container } = render(<FAQMinimal items={mockFAQItems} />);
    const minimalContainer = container.querySelector('.space-y-2');
    expect(minimalContainer).toBeInTheDocument();
  });
});

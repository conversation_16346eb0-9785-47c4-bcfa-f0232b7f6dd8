import { NextRequest, NextResponse } from 'next/server'
import { getPayloadHMR } from '@payloadcms/next/utilities'
import configPromise from '../../../../../../payload.config'

// GET /api/ai/conversations/[id] - Get specific conversation
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const payload = await getPayloadHMR({ config: configPromise })
    const { id } = params

    const conversation = await payload.findByID({
      collection: 'ai-conversations',
      id,
    })

    if (!conversation) {
      return NextResponse.json(
        { error: 'Conversation not found' },
        { status: 404 }
      )
    }

    // Check if conversation has expired
    const now = new Date()
    const expiresAt = new Date(conversation.expiresAt)
    
    if (now > expiresAt) {
      // Mark as expired
      await payload.update({
        collection: 'ai-conversations',
        id: conversation.id,
        data: {
          status: 'expired',
        },
      })

      return NextResponse.json(
        { error: 'Conversation has expired' },
        { status: 410 }
      )
    }

    return NextResponse.json({ conversation })

  } catch (error) {
    console.error('Error retrieving conversation:', error)
    return NextResponse.json(
      { error: 'Failed to retrieve conversation' },
      { status: 500 }
    )
  }
}

// PUT /api/ai/conversations/[id] - Update specific conversation
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const payload = await getPayloadHMR({ config: configPromise })
    const { id } = params
    const body = await request.json()

    const updatedConversation = await payload.update({
      collection: 'ai-conversations',
      id,
      data: body,
    })

    return NextResponse.json({ conversation: updatedConversation })

  } catch (error) {
    console.error('Error updating conversation:', error)
    return NextResponse.json(
      { error: 'Failed to update conversation' },
      { status: 500 }
    )
  }
}

// DELETE /api/ai/conversations/[id] - Delete specific conversation
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const payload = await getPayloadHMR({ config: configPromise })
    const { id } = params

    await payload.delete({
      collection: 'ai-conversations',
      id,
    })

    return NextResponse.json({
      success: true,
      message: 'Conversation deleted',
    })

  } catch (error) {
    console.error('Error deleting conversation:', error)
    return NextResponse.json(
      { error: 'Failed to delete conversation' },
      { status: 500 }
    )
  }
}

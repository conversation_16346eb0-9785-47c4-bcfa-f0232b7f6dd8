#!/usr/bin/env node

/**
 * Performance Audit Script
 * 
 * Runs Lighthouse audits and analyzes bundle sizes
 */

const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');
const fs = require('fs');
const path = require('path');

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
  performance: 90,
  accessibility: 95,
  'best-practices': 90,
  seo: 90,
  pwa: 70,
  // Core Web Vitals
  'largest-contentful-paint': 2500,
  'first-input-delay': 100,
  'cumulative-layout-shift': 0.1,
  'first-contentful-paint': 1800,
  'speed-index': 3400,
  'total-blocking-time': 200,
};

// URLs to audit
const AUDIT_URLS = [
  'http://localhost:3001',
  'http://localhost:3001/locations/canggu',
  'http://localhost:3001/property-types/villa',
  'http://localhost:3001/contact',
];

async function runLighthouseAudit(url) {
  console.log(`🔍 Auditing: ${url}`);
  
  const chrome = await chromeLauncher.launch({ chromeFlags: ['--headless'] });
  const options = {
    logLevel: 'info',
    output: 'json',
    onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
    port: chrome.port,
  };

  try {
    const runnerResult = await lighthouse(url, options);
    await chrome.kill();

    if (!runnerResult || !runnerResult.lhr) {
      throw new Error('Failed to run Lighthouse audit');
    }

    return runnerResult.lhr;
  } catch (error) {
    await chrome.kill();
    throw error;
  }
}

function analyzeResults(results) {
  const analysis = {
    url: results.finalUrl,
    timestamp: new Date().toISOString(),
    scores: {},
    metrics: {},
    issues: [],
    recommendations: [],
  };

  // Extract category scores
  Object.entries(results.categories).forEach(([key, category]) => {
    const score = Math.round(category.score * 100);
    analysis.scores[key] = score;
    
    const threshold = PERFORMANCE_THRESHOLDS[key];
    if (threshold && score < threshold) {
      analysis.issues.push({
        category: key,
        score,
        threshold,
        message: `${key} score (${score}) is below threshold (${threshold})`,
      });
    }
  });

  // Extract Core Web Vitals
  const audits = results.audits;
  const coreWebVitals = [
    'largest-contentful-paint',
    'first-input-delay',
    'cumulative-layout-shift',
    'first-contentful-paint',
    'speed-index',
    'total-blocking-time',
  ];

  coreWebVitals.forEach((metric) => {
    if (audits[metric]) {
      const value = audits[metric].numericValue;
      analysis.metrics[metric] = {
        value: Math.round(value),
        displayValue: audits[metric].displayValue,
        score: Math.round(audits[metric].score * 100),
      };

      const threshold = PERFORMANCE_THRESHOLDS[metric];
      if (threshold && value > threshold) {
        analysis.issues.push({
          metric,
          value: Math.round(value),
          threshold,
          message: `${metric} (${Math.round(value)}) exceeds threshold (${threshold})`,
        });
      }
    }
  });

  // Extract recommendations from failed audits
  Object.entries(audits).forEach(([key, audit]) => {
    if (audit.score !== null && audit.score < 0.9 && audit.details) {
      analysis.recommendations.push({
        audit: key,
        title: audit.title,
        description: audit.description,
        score: Math.round(audit.score * 100),
        savings: audit.details.overallSavingsMs || 0,
      });
    }
  });

  return analysis;
}

function generateReport(analyses) {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalUrls: analyses.length,
      averageScores: {},
      criticalIssues: 0,
      totalIssues: 0,
    },
    results: analyses,
  };

  // Calculate average scores
  const scoreKeys = Object.keys(analyses[0].scores);
  scoreKeys.forEach((key) => {
    const total = analyses.reduce((sum, analysis) => sum + analysis.scores[key], 0);
    report.summary.averageScores[key] = Math.round(total / analyses.length);
  });

  // Count issues
  analyses.forEach((analysis) => {
    report.summary.totalIssues += analysis.issues.length;
    analysis.issues.forEach((issue) => {
      if (issue.category === 'performance' || issue.metric) {
        report.summary.criticalIssues++;
      }
    });
  });

  return report;
}

function saveReport(report) {
  const reportsDir = path.join(process.cwd(), 'performance-reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `performance-audit-${timestamp}.json`;
  const filepath = path.join(reportsDir, filename);

  fs.writeFileSync(filepath, JSON.stringify(report, null, 2));
  console.log(`📊 Report saved: ${filepath}`);

  // Also save as latest
  const latestPath = path.join(reportsDir, 'latest.json');
  fs.writeFileSync(latestPath, JSON.stringify(report, null, 2));

  return filepath;
}

function printSummary(report) {
  console.log('\n🎯 Performance Audit Summary');
  console.log('================================');
  console.log(`📅 Timestamp: ${report.timestamp}`);
  console.log(`🔗 URLs Audited: ${report.summary.totalUrls}`);
  console.log(`⚠️  Total Issues: ${report.summary.totalIssues}`);
  console.log(`🚨 Critical Issues: ${report.summary.criticalIssues}`);
  
  console.log('\n📊 Average Scores:');
  Object.entries(report.summary.averageScores).forEach(([category, score]) => {
    const emoji = score >= 90 ? '✅' : score >= 70 ? '⚠️' : '❌';
    console.log(`  ${emoji} ${category}: ${score}`);
  });

  if (report.summary.criticalIssues > 0) {
    console.log('\n🚨 Critical Issues Found:');
    report.results.forEach((result) => {
      result.issues.forEach((issue) => {
        if (issue.category === 'performance' || issue.metric) {
          console.log(`  ❌ ${result.url}: ${issue.message}`);
        }
      });
    });
  }

  console.log('\n💡 Top Recommendations:');
  const allRecommendations = report.results.flatMap(r => r.recommendations);
  const topRecommendations = allRecommendations
    .sort((a, b) => b.savings - a.savings)
    .slice(0, 5);

  topRecommendations.forEach((rec, index) => {
    console.log(`  ${index + 1}. ${rec.title} (${rec.savings}ms savings)`);
  });
}

async function main() {
  console.log('🚀 Starting Performance Audit...\n');

  try {
    const analyses = [];

    for (const url of AUDIT_URLS) {
      const results = await runLighthouseAudit(url);
      const analysis = analyzeResults(results);
      analyses.push(analysis);
      
      console.log(`✅ Completed: ${url}`);
      console.log(`   Performance: ${analysis.scores.performance}`);
      console.log(`   LCP: ${analysis.metrics['largest-contentful-paint']?.displayValue || 'N/A'}`);
      console.log(`   Issues: ${analysis.issues.length}\n`);
    }

    const report = generateReport(analyses);
    const reportPath = saveReport(report);
    printSummary(report);

    // Exit with error code if critical issues found
    if (report.summary.criticalIssues > 0) {
      console.log('\n❌ Performance audit failed due to critical issues');
      process.exit(1);
    } else {
      console.log('\n✅ Performance audit passed!');
      process.exit(0);
    }

  } catch (error) {
    console.error('❌ Performance audit failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  runLighthouseAudit,
  analyzeResults,
  generateReport,
  PERFORMANCE_THRESHOLDS,
};

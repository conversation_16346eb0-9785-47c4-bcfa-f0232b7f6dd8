'use client'

import Image from 'next/image'
import { useState, useEffect } from 'react'

const benefits = [
  {
    id: 1,
    title: 'Affordable Paradise',
    description: 'Live in tropical luxury for a fraction of Western costs. Enjoy world-class amenities, beautiful weather, and stunning landscapes without breaking the bank.',
    statistic: '70% Lower',
    statisticLabel: 'Cost of Living vs. Western Countries',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
      </svg>
    )
  },
  {
    id: 2,
    title: 'Digital Nomad Hub',
    description: 'Thriving community of remote workers with excellent coworking spaces, reliable internet, and networking opportunities in every major area.',
    statistic: '50,000+',
    statisticLabel: 'Digital Nomads Choose Bali',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
      </svg>
    )
  },
  {
    id: 3,
    title: 'Rich Culture & Wellness',
    description: 'Immerse yourself in ancient Balinese traditions, world-renowned wellness practices, and spiritual experiences that transform your lifestyle.',
    statistic: '1000+',
    statisticLabel: 'Temples & Cultural Sites',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
      </svg>
    )
  },
  {
    id: 4,
    title: 'Investment Opportunity',
    description: 'Growing property market with strong rental yields and capital appreciation. Perfect timing for smart real estate investments.',
    statistic: '8-12%',
    statisticLabel: 'Annual Rental Yields',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
      </svg>
    )
  }
]

const testimonials = [
  {
    id: 1,
    name: 'Sarah Johnson',
    role: 'Digital Marketing Consultant',
    location: 'Canggu',
    quote: 'Moving to Bali was the best decision I ever made. The quality of life is incredible, and I&apos;ve built an amazing network of like-minded entrepreneurs.',
    avatar: '/images/testimonials/sarah-johnson.jpg'
  },
  {
    id: 2,
    name: 'Marcus Chen',
    role: 'Software Developer',
    location: 'Ubud',
    quote: 'The combination of affordable living, great internet, and inspiring environment has boosted my productivity and happiness significantly.',
    avatar: '/images/testimonials/marcus-chen.jpg'
  },
  {
    id: 3,
    name: 'Emma Rodriguez',
    role: 'Property Investor',
    location: 'Seminyak',
    quote: 'My villa investment in Seminyak has exceeded all expectations. The rental yields are fantastic and the property has appreciated beautifully.',
    avatar: '/images/testimonials/emma-rodriguez.jpg'
  }
]

const BenefitCard = ({ benefit, index }: { benefit: typeof benefits[0], index: number }) => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), index * 200)
    return () => clearTimeout(timer)
  }, [index])

  return (
    <div className={`transition-all duration-700 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
      <div className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
        {/* Icon */}
        <div className="inline-flex p-3 bg-emerald-100 text-emerald-600 rounded-lg mb-6">
          {benefit.icon}
        </div>

        {/* Content */}
        <h3 className="text-xl font-bold text-gray-900 mb-4">
          {benefit.title}
        </h3>

        <p className="text-gray-600 mb-6 leading-relaxed">
          {benefit.description}
        </p>

        {/* Statistic */}
        <div className="border-t border-gray-100 pt-6">
          <div className="text-3xl font-bold text-emerald-600 mb-1">
            {benefit.statistic}
          </div>
          <div className="text-sm text-gray-500 font-medium">
            {benefit.statisticLabel}
          </div>
        </div>
      </div>
    </div>
  )
}

const TestimonialCard = ({ testimonial, index }: { testimonial: typeof testimonials[0], index: number }) => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 500 + index * 150)
    return () => clearTimeout(timer)
  }, [index])

  return (
    <div className={`transition-all duration-700 ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-8'}`}>
      <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
        <div className="flex items-start gap-4">
          <div className="relative w-12 h-12 rounded-full overflow-hidden flex-shrink-0">
            <Image
              src={testimonial.avatar}
              alt={`${testimonial.name} - ${testimonial.role}`}
              fill
              className="object-cover"
              placeholder="blur"
              blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
            />
          </div>
          <div className="flex-1">
            <p className="text-gray-700 italic mb-4 leading-relaxed">
              "              {`"${testimonial.quote}"`}"
            </p>
            <div>
              <div className="font-semibold text-gray-900">{testimonial.name}</div>
              <div className="text-sm text-gray-500">{testimonial.role}</div>
              <div className="text-sm text-emerald-600 font-medium">{testimonial.location}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

const WhyChooseBali = () => {
  return (
    <section className="py-20 bg-gradient-to-br from-emerald-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            Why Choose Bali for Your Next Home?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Join thousands of expats, digital nomads, and investors who have discovered 
            the perfect blend of lifestyle, opportunity, and paradise in Bali.
          </p>
        </div>

        {/* Benefits Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {benefits.map((benefit, index) => (
            <BenefitCard key={benefit.id} benefit={benefit} index={index} />
          ))}
        </div>

        {/* Testimonials Section */}
        <div className="mb-12">
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-12">
            What Our Clients Say
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <TestimonialCard key={testimonial.id} testimonial={testimonial} index={index} />
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-white rounded-2xl p-8 shadow-lg">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Ready to Start Your Bali Journey?
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Let our local experts guide you through every step of finding your perfect property in paradise. 
            From initial consultation to moving in, we&apos;re here to help.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => window.location.href = '/chatbot?query=general-consultation'}
              className="bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105"
            >
              Schedule Consultation
            </button>
            <button className="border-2 border-emerald-600 text-emerald-600 hover:bg-emerald-600 hover:text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300">
              Download Guide
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default WhyChooseBali

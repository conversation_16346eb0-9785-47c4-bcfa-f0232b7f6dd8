# 🎨 **Bali Property Scout UI/UX Specification**

This document defines the user experience goals, information architecture, user flows, and visual design specifications for Bali Property Scout's user interface. It serves as the foundation for visual design and frontend development, ensuring a cohesive and user-centered experience.

## Overall UX Goals & Principles

### Target User Personas

**Digital Nomad Explorer:** Tech-savvy remote workers (25-40) seeking 3-12 month rentals in vibrant communities. Values fast internet, coworking spaces, and modern amenities. Prefers quick, mobile-first interactions and visual property discovery.

**Expat Family Seeker:** International families (30-55) relocating for 1-5 years. Prioritizes safety, schools, healthcare proximity, and family-friendly neighborhoods. Needs comprehensive location information and detailed consultation support.

**Property Investor:** International buyers (35-65) seeking rental income or retirement properties. Focuses on ROI analysis, legal compliance, and market trends. Values detailed data, expert consultation, and professional presentation.

### Usability Goals

**Immediate Engagement:** Visitors understand the value proposition and can start property search within 30 seconds of landing
**Effortless Navigation:** Users can find location and property information with maximum 3 clicks from homepage
**Conversion Clarity:** Clear path to chatbot consultation is visible and accessible from every page
**Mobile Excellence:** All interactions work seamlessly on mobile devices with touch-optimized interface
**Trust Building:** Professional design and comprehensive information establish credibility and expertise

### Design Principles

1. **Tropical Professionalism** - Blend Bali's natural beauty with professional real estate expertise
2. **Conversion-First Design** - Every element guides users toward chatbot consultation
3. **Information Hierarchy** - Present complex property/location data in digestible, scannable formats
4. **Interactive Delight** - Subtle animations and hover effects enhance engagement without distraction
5. **Accessible Excellence** - Design for all users including screen readers, keyboard navigation, and reduced motion preferences

### Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-28 | 1.0 | Initial UI/UX specification for brownfield enhancement | UX Expert |

## Information Architecture (IA)

### Site Map / Screen Inventory

```mermaid
graph TD
    A[Homepage] --> B[All Locations]
    A --> C[Property Types]
    A --> D[Services]
    A --> E[About]
    A --> F[Contact]
    A --> G[Chatbot - External]
    
    B --> B1[Canggu]
    B --> B2[Ubud]
    B --> B3[Seminyak]
    B --> B4[Sanur]
    B --> B5[Jimbaran]
    
    B1 --> B1A[Overview]
    B1 --> B1B[Neighborhoods]
    B1 --> B1C[Property Types]
    B1 --> B1D[Lifestyle]
    B1 --> B1E[Practical Info]
    B1 --> B1F[FAQ]
    
    C --> C1[Villas]
    C --> C2[Guesthouses]
    
    C1 --> C1A[Modern Luxury]
    C1 --> C1B[Traditional Balinese]
    C1 --> C1C[Investment Properties]
    
    D --> D1[Investment Consultation]
    D --> D2[Purchase Assistance]
    D --> D3[Consultation & Viewing]
    
    G --> G1[General Consultation]
    G --> G2[Location-Specific Search]
    G --> G3[Property Type Search]
    G --> G4[Investment Inquiry]
```

### Navigation Structure

**Primary Navigation:** Floating header with logo, main menu (Home, Locations dropdown, Property Types dropdown, Services, About, Contact), and prominent "Find My Property" CTA button

**Secondary Navigation:** Sticky anchor navigation on location pages (Overview, Neighborhoods, Property Types, Lifestyle, Practical Info, FAQ) that appears below floating header on scroll

**Breadcrumb Strategy:** Minimal breadcrumbs on deep pages (Location > Canggu > Neighborhoods) with emphasis on clear page titles and back navigation

## User Flows

### Property Discovery Flow

**User Goal:** Find suitable property options in Bali based on personal preferences and requirements

**Entry Points:** Homepage hero CTA, location cards, navigation menu, search engines

**Success Criteria:** User engages with chatbot with specific location/property type context

#### Flow Diagram
```mermaid
graph TD
    A[Land on Homepage] --> B{Knows Preferred Location?}
    B -->|Yes| C[Click Location Card]
    B -->|No| D[Browse All Locations]
    B -->|Unsure| E[Click 'Start Chat']
    
    C --> F[Location Page]
    D --> G[All Locations Page]
    E --> H[Chatbot - General]
    
    F --> I[Explore Neighborhoods]
    F --> J[View Property Types]
    F --> K[Click 'Find Properties']
    
    G --> L[Filter by Lifestyle]
    G --> M[Click Location Card]
    
    I --> N[Click Neighborhood CTA]
    J --> O[Click Property Type CTA]
    K --> P[Chatbot - Location Specific]
    
    L --> Q[Refined Location List]
    M --> F
    
    N --> P
    O --> P
    Q --> M
    
    P --> R[Qualified Lead]
    H --> R
```

#### Edge Cases & Error Handling:
- User clicks broken/outdated links → Redirect to relevant chatbot query
- Mobile user has difficulty with dropdowns → Simplified mobile navigation
- User overwhelmed by options → Clear "Need Help Choosing?" CTA to chatbot
- Slow internet connection → Progressive loading with skeleton screens

**Notes:** All property-specific CTAs must route to app.balipropertyscout.com with appropriate query strings

### Service Consultation Flow

**User Goal:** Understand available services and request professional consultation

**Entry Points:** Services navigation, homepage service mentions, about page CTAs

**Success Criteria:** User submits consultation request or engages with service-specific chatbot

#### Flow Diagram
```mermaid
graph TD
    A[Services Page] --> B[Review Service Options]
    B --> C{Service Interest}
    
    C -->|Investment| D[Investment Consultation Details]
    C -->|Purchase| E[Purchase Assistance Details]
    C -->|Viewing| F[Consultation & Viewing Details]
    
    D --> G[Click 'Book Investment Consultation']
    E --> H[Click 'Get Purchase Help']
    F --> I[Click 'Schedule Viewing']
    
    G --> J[Chatbot - Investment Query]
    H --> K[Chatbot - Purchase Query]
    I --> L[Chatbot - Viewing Query]
    
    J --> M[Qualified Investment Lead]
    K --> N[Qualified Purchase Lead]
    L --> O[Qualified Viewing Lead]
```

#### Edge Cases & Error Handling:
- User confused about service differences → Clear comparison table and descriptions
- User wants multiple services → General consultation option available
- User prefers form over chat → Contact page alternative provided

**Notes:** Service-specific chatbot queries help qualify leads immediately

## Wireframes & Mockups

**Primary Design Files:** Figma workspace (to be created) - https://figma.com/bali-property-scout-ui

### Key Screen Layouts

#### Homepage
**Purpose:** Immediate value communication and conversion to chatbot or location exploration

**Key Elements:**
- Cinematic hero section with Bali imagery and translucent overlay card
- "Find Your Perfect Bali Home" headline with compelling sub-headline
- Dual CTAs: "Start Chat" (primary) and "Browse Locations" (secondary)
- High priority location cards with hover animations and real photography
- Trust indicators: "Why Choose Us" section with local expertise highlights
- Coverage overview with clickable lifestyle chips (Digital Nomad, Family, Investment)

**Interaction Notes:** Smooth scroll from hero to locations, hover effects on all cards, floating elements for visual interest

**Design File Reference:** Homepage_Hero_v1.fig, Homepage_Locations_v1.fig

#### Location Detail Page (e.g., Canggu)
**Purpose:** Comprehensive location information with clear conversion paths

**Key Elements:**
- Full-width hero banner with location-specific imagery
- Sticky anchor navigation (Overview, Neighborhoods, Property Types, Lifestyle, Practical Info, FAQ)
- Interactive neighborhood cards with photos and "Best For" tags
- Property pricing visualization with charts and infographics
- Lifestyle section with icons and collapsible accordions
- Multiple CTAs throughout: "Rent Villa in Canggu", "Get Expert Advice"

**Interaction Notes:** Anchor navigation highlights active section, smooth scrolling, hover lift effects on cards

**Design File Reference:** Location_Detail_v1.fig, Location_Navigation_v1.fig

#### Services Page
**Purpose:** Clear service presentation with immediate consultation access

**Key Elements:**
- Hero section: "Our Services – Investment, Purchase & Consultation"
- Three-column layout for service categories
- Each service includes bullet points, statistics, and dedicated CTA
- Cross-links section with cards to locations and property types
- Complete removal of Property Management and Legal Services

**Interaction Notes:** Service cards have subtle hover effects, CTAs route to service-specific chatbot queries

**Design File Reference:** Services_Layout_v1.fig

## Component Library / Design System

**Design System Approach:** Extend existing design system in `src/components/design-system/` with new interactive components and animation tokens while maintaining current color palette and typography

### Core Components

#### Enhanced Floating Header
**Purpose:** Modern navigation that responds to scroll behavior and provides clear site navigation

**Variants:** 
- Default state: Semi-transparent with subtle shadow
- Scrolled state: Increased opacity and reduced height
- Mobile state: Hamburger menu with slide-in panel

**States:** Default, scrolled, mobile-open, mobile-closed, dropdown-active

**Usage Guidelines:** Always fixed position, maintains brand visibility, CTA button always prominent

#### Interactive Location Card
**Purpose:** Showcase location information with engaging hover effects and clear CTAs

**Variants:**
- Priority card: Larger size with priority badge
- Standard card: Regular grid item
- Compact card: Smaller version for cross-links

**States:** Default, hover (scale + shadow), loading, error

**Usage Guidelines:** Always include real photography, price range, and dual CTAs (Learn More + Find Properties)

#### Sticky Anchor Navigation
**Purpose:** Help users navigate long location pages with clear section indicators

**Variants:** 
- Desktop: Horizontal tabs with underline indicators
- Mobile: Collapsible dropdown with current section display

**States:** Default, active-section, mobile-collapsed, mobile-expanded

**Usage Guidelines:** Only on location detail pages, becomes sticky after hero section

#### Animated CTA Button
**Purpose:** Drive conversions with clear visual hierarchy and micro-interactions

**Variants:**
- Primary: Solid background for main actions
- Secondary: Outline style for secondary actions
- Chatbot: Special styling for chatbot-routing buttons

**States:** Default, hover (scale + color shift), active, loading, disabled

**Usage Guidelines:** Primary CTAs route to chatbot, secondary CTAs navigate within site

## Branding & Style Guide

### Visual Identity
**Brand Guidelines:** Bali Property Scout brand identity - tropical professionalism with modern real estate expertise

### Color Palette
| Color Type | Hex Code | Usage |
|------------|----------|-------|
| Primary | #10b981 | Main brand color, primary CTAs, active states |
| Secondary | #f97316 | Accent elements, secondary CTAs, highlights |
| Accent | #0ea5e9 | Interactive elements, links, info indicators |
| Success | #22c55e | Positive feedback, confirmations, success states |
| Warning | #f59e0b | Cautions, important notices, pending states |
| Error | #ef4444 | Errors, destructive actions, validation failures |
| Neutral | #6b7280, #374151, #1f2937 | Text hierarchy, borders, backgrounds |

### Typography

#### Font Families
- **Primary:** Inter (existing) - Clean, modern sans-serif for all UI text
- **Secondary:** Inter (existing) - Consistent typography system
- **Monospace:** JetBrains Mono - Code snippets and technical data

#### Type Scale
| Element | Size | Weight | Line Height |
|---------|------|--------|-------------|
| H1 | 3.5rem (56px) | 700 | 1.1 |
| H2 | 2.25rem (36px) | 600 | 1.2 |
| H3 | 1.5rem (24px) | 600 | 1.3 |
| Body | 1rem (16px) | 400 | 1.6 |
| Small | 0.875rem (14px) | 400 | 1.5 |

### Iconography
**Icon Library:** Heroicons (existing) - Consistent with current implementation

**Usage Guidelines:** 24px standard size, 16px for inline text, maintain consistent stroke width

### Spacing & Layout
**Grid System:** CSS Grid with 12-column layout, responsive breakpoints

**Spacing Scale:** 4px base unit (0.25rem) with 8px, 16px, 24px, 32px, 48px, 64px increments

## Accessibility Requirements

### Compliance Target
**Standard:** WCAG 2.1 AA compliance with enhanced focus on keyboard navigation and screen reader support

### Key Requirements

**Visual:**
- Color contrast ratios: 4.5:1 for normal text, 3:1 for large text
- Focus indicators: 2px solid outline with high contrast color
- Text sizing: Minimum 16px base size, scalable to 200% without horizontal scroll

**Interaction:**
- Keyboard navigation: All interactive elements accessible via Tab, Enter, Space, Arrow keys
- Screen reader support: Proper ARIA labels, landmarks, and live regions for dynamic content
- Touch targets: Minimum 44px touch target size for mobile interactions

**Content:**
- Alternative text: Descriptive alt text for all images, especially location photography
- Heading structure: Logical H1-H6 hierarchy for screen reader navigation
- Form labels: Clear, descriptive labels for all form inputs with error messaging

### Testing Strategy
Automated testing with axe-core, manual keyboard navigation testing, screen reader testing with NVDA/JAWS, mobile accessibility testing with TalkBack/VoiceOver

## Responsiveness Strategy

### Breakpoints
| Breakpoint | Min Width | Max Width | Target Devices |
|------------|-----------|-----------|----------------|
| Mobile | 320px | 767px | Smartphones, small tablets |
| Tablet | 768px | 1023px | Tablets, small laptops |
| Desktop | 1024px | 1439px | Laptops, desktop monitors |
| Wide | 1440px | - | Large monitors, ultrawide displays |

### Adaptation Patterns

**Layout Changes:** Single column on mobile, multi-column grids on tablet+, sidebar layouts on desktop

**Navigation Changes:** Hamburger menu on mobile, full navigation on desktop, sticky behavior maintained across all sizes

**Content Priority:** Hero content stacks vertically on mobile, location cards in single column, service cards stack on mobile

**Interaction Changes:** Touch-optimized buttons on mobile, hover effects on desktop, swipe gestures for mobile carousels

## Animation & Micro-interactions

### Motion Principles
Subtle, purposeful animations that enhance usability without distraction. All animations respect `prefers-reduced-motion` settings and use hardware acceleration for smooth performance.

### Key Animations
- **Header Scroll Response:** Opacity and height changes on scroll (Duration: 300ms, Easing: ease-out)
- **Card Hover Effects:** Scale (1.02) and shadow elevation (Duration: 200ms, Easing: ease-in-out)
- **CTA Button Interactions:** Scale (1.05) and color transitions (Duration: 150ms, Easing: ease-out)
- **Page Transitions:** Fade in content sections on scroll (Duration: 400ms, Easing: ease-out)
- **Mobile Menu:** Slide-in panel with backdrop blur (Duration: 250ms, Easing: ease-in-out)
- **Anchor Navigation:** Smooth scroll to sections (Duration: 500ms, Easing: ease-in-out)

## Performance Considerations

### Performance Goals
- **Page Load:** First Contentful Paint < 1.5s, Largest Contentful Paint < 2.5s
- **Interaction Response:** Button/link responses < 100ms
- **Animation FPS:** Maintain 60fps for all animations and transitions

### Design Strategies
Progressive image loading with blur-up technique, lazy loading for below-fold content, optimized image formats (WebP/AVIF), minimal animation complexity, efficient CSS with hardware acceleration

## Next Steps

### Immediate Actions
1. Review and validate UX specification with stakeholders
2. Create detailed visual designs in Figma based on wireframes and component specifications
3. Prepare component library documentation for development handoff
4. Conduct accessibility review of proposed interactions
5. Validate responsive breakpoints with target device testing

### Design Handoff Checklist
☑️ All user flows documented
☑️ Component inventory complete
☑️ Accessibility requirements defined
☑️ Responsive strategy clear
☑️ Brand guidelines incorporated
☑️ Performance goals established

---

**This UI/UX specification provides the foundation for creating a modern, conversion-focused Bali Property Scout website. Ready for handoff to Architect for detailed frontend architecture planning.**

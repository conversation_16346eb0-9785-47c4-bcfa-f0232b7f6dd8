/**
 * Modern Card Component with Glassmorphism Effects
 * Epic 6: UI Modernization - Modern card designs
 */

'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import Image from 'next/image';

export interface ModernCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'glass' | 'elevated' | 'gradient' | 'minimal';
  hover?: boolean;
  image?: string;
  imageAlt?: string;
  overlay?: boolean;
}

export const ModernCard: React.FC<ModernCardProps> = ({
  children,
  className,
  variant = 'glass',
  hover = true,
  image,
  imageAlt = '',
  overlay = false,
}) => {
  const baseClasses = "relative overflow-hidden transition-all duration-500 ease-out";
  
  const variantClasses = {
    glass: "bg-white/10 backdrop-blur-xl border border-white/20 shadow-2xl",
    elevated: "bg-white shadow-xl border border-gray-100/50",
    gradient: "bg-gradient-to-br from-white/90 to-white/70 backdrop-blur-lg border border-white/30",
    minimal: "bg-white/95 backdrop-blur-sm border border-gray-200/30 shadow-lg"
  };

  const hoverClasses = hover ? {
    glass: "hover:bg-white/20 hover:border-white/30 hover:shadow-3xl hover:scale-[1.02] hover:-translate-y-2",
    elevated: "hover:shadow-2xl hover:scale-[1.02] hover:-translate-y-2 hover:border-gray-200/70",
    gradient: "hover:from-white/95 hover:to-white/80 hover:scale-[1.02] hover:-translate-y-2",
    minimal: "hover:bg-white hover:shadow-xl hover:scale-[1.02] hover:-translate-y-1"
  } : {};

  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        hover && hoverClasses[variant],
        "rounded-2xl",
        className
      )}
    >
      {image && (
        <div className="relative w-full h-48 overflow-hidden rounded-t-2xl">
          <Image
            src={image}
            alt={imageAlt}
            fill
            className="object-cover transition-transform duration-700 hover:scale-110"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
          {overlay && (
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
          )}
        </div>
      )}
      <div className={cn(
        "relative z-10",
        image ? "p-6" : "p-8"
      )}>
        {children}
      </div>
    </div>
  );
};

export interface PropertyCardProps {
  title: string;
  location: string;
  price: string;
  image: string;
  description: string;
  features: string[];
  href: string;
  priority?: 'ZEER HOOG' | 'HOOG';
  className?: string;
}

export const PropertyCard: React.FC<PropertyCardProps> = ({
  title,
  location,
  price,
  image,
  description,
  features,
  href,
  priority,
  className,
}) => {
  return (
    <ModernCard 
      variant="glass" 
      image={image} 
      imageAlt={title}
      overlay
      className={className}
    >
      <div className="space-y-4">
        {/* Priority Badge */}
        {priority && (
          <div className="flex justify-between items-start">
            <span className={cn(
              "px-3 py-1 rounded-full text-xs font-semibold backdrop-blur-sm",
              priority === 'ZEER HOOG' 
                ? 'bg-red-500/20 text-red-700 border border-red-500/30' 
                : 'bg-orange-500/20 text-orange-700 border border-orange-500/30'
            )}>
              {priority}
            </span>
            <span className="text-emerald-600 font-bold text-lg">{price}</span>
          </div>
        )}

        {/* Title and Location */}
        <div>
          <h3 className="text-xl font-bold text-gray-900 mb-2 line-clamp-2">
            {title}
          </h3>
          <p className="text-gray-600 text-sm font-medium">
            📍 {location}
          </p>
        </div>

        {/* Description */}
        <p className="text-gray-700 text-sm line-clamp-3 leading-relaxed">
          {description}
        </p>

        {/* Features */}
        <div className="flex flex-wrap gap-2">
          {features.slice(0, 3).map((feature, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-emerald-100/80 text-emerald-700 text-xs rounded-lg font-medium"
            >
              {feature}
            </span>
          ))}
          {features.length > 3 && (
            <span className="px-2 py-1 bg-gray-100/80 text-gray-600 text-xs rounded-lg font-medium">
              +{features.length - 3} more
            </span>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-2">
          <a
            href={href}
            className="flex-1 bg-gray-100/80 hover:bg-gray-200/80 text-gray-800 px-4 py-2.5 rounded-xl font-medium text-center transition-all duration-300 hover:scale-105 backdrop-blur-sm"
          >
            Learn More
          </a>
          <a
            href={`https://app.bali-realestate.com/chatbot?query=rent-villa-in-${location.toLowerCase()}`}
            className="flex-1 bg-emerald-600/90 hover:bg-emerald-700/90 text-white px-4 py-2.5 rounded-xl font-medium text-center transition-all duration-300 hover:scale-105 backdrop-blur-sm"
            target="_self"
          >
            Find Properties
          </a>
        </div>
      </div>
    </ModernCard>
  );
};

export interface LocationCardProps {
  name: string;
  image: string;
  description: string;
  priceRange: string;
  highlight: string;
  href: string;
  className?: string;
}

export const LocationCard: React.FC<LocationCardProps> = ({
  name,
  image,
  description,
  priceRange,
  highlight,
  href,
  className,
}) => {
  return (
    <ModernCard 
      variant="elevated" 
      image={image} 
      imageAlt={`${name} location`}
      overlay
      className={className}
    >
      <div className="space-y-3">
        <div>
          <h3 className="text-xl font-bold text-gray-900 mb-1">{name}</h3>
          <p className="text-emerald-600 font-semibold text-sm">{priceRange}</p>
        </div>
        
        <p className="text-gray-600 text-sm font-medium">{highlight}</p>
        
        <p className="text-gray-700 text-sm line-clamp-2 leading-relaxed">
          {description}
        </p>
        
        <a
          href={href}
          className="inline-flex items-center text-emerald-600 hover:text-emerald-700 font-medium text-sm transition-colors group"
        >
          Explore {name}
          <svg 
            className="w-4 h-4 ml-1 transition-transform group-hover:translate-x-1" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </a>
      </div>
    </ModernCard>
  );
};

export default ModernCard;

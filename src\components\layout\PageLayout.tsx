/**
 * PageLayout Component
 * 
 * Main layout wrapper that provides consistent page structure
 * with header, footer, and content areas.
 */

import React from 'react';
import { cn } from '@/lib/utils';
import { Header } from './Header';
import { Footer } from './Footer';
import { Breadcrumbs, type BreadcrumbItem } from './Navigation';

export interface PageLayoutProps {
  /** Page content */
  children: React.ReactNode;
  /** Custom className for main content */
  className?: string;
  /** Whether to show header */
  showHeader?: boolean;
  /** Whether to show footer */
  showFooter?: boolean;
  /** Header props */
  headerProps?: React.ComponentProps<typeof Header>;
  /** Footer props */
  footerProps?: React.ComponentProps<typeof Footer>;
  /** Breadcrumb items */
  breadcrumbs?: BreadcrumbItem[];
  /** Page title for SEO */
  title?: string;
  /** Page description for SEO */
  description?: string;
  /** Whether to add container padding */
  containerized?: boolean;
  /** Maximum width of content */
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
}

// Max width styles
const maxWidthStyles = {
  sm: 'max-w-3xl',
  md: 'max-w-4xl',
  lg: 'max-w-6xl',
  xl: 'max-w-7xl',
  '2xl': 'max-w-8xl',
  full: 'max-w-full',
};

export const PageLayout: React.FC<PageLayoutProps> = ({
  children,
  className,
  showHeader = true,
  showFooter = true,
  headerProps,
  footerProps,
  breadcrumbs,
  title,
  description,
  containerized = true,
  maxWidth = 'xl',
}) => {
  return (
    <div className="min-h-screen flex flex-col">
      {/* SEO Meta Tags */}
      {title && (
        <head>
          <title>{title}</title>
          {description && <meta name="description" content={description} />}
        </head>
      )}

      {/* Header */}
      {showHeader && <Header {...headerProps} />}

      {/* Main Content */}
      <main className="flex-1">
        {/* Breadcrumbs */}
        {breadcrumbs && breadcrumbs.length > 0 && (
          <div className={cn(
            'border-b border-neutral-200 bg-neutral-50',
            containerized && 'px-4 sm:px-6 lg:px-8'
          )}>
            <div className={cn(
              'py-4 mx-auto',
              containerized && maxWidthStyles[maxWidth]
            )}>
              <Breadcrumbs items={breadcrumbs} />
            </div>
          </div>
        )}

        {/* Page Content */}
        <div className={cn(
          containerized && 'px-4 sm:px-6 lg:px-8',
          className
        )}>
          <div className={cn(
            containerized && 'mx-auto',
            containerized && maxWidthStyles[maxWidth]
          )}>
            {children}
          </div>
        </div>
      </main>

      {/* Footer */}
      {showFooter && <Footer {...footerProps} />}
    </div>
  );
};

// Specialized layout for content pages
export interface ContentPageLayoutProps extends Omit<PageLayoutProps, 'children'> {
  /** Page heading */
  heading?: string;
  /** Page subheading */
  subheading?: string;
  /** Page actions (buttons, etc.) */
  actions?: React.ReactNode;
  /** Sidebar content */
  sidebar?: React.ReactNode;
  /** Whether sidebar is on the left */
  sidebarLeft?: boolean;
  /** Main content */
  children: React.ReactNode;
}

export const ContentPageLayout: React.FC<ContentPageLayoutProps> = ({
  heading,
  subheading,
  actions,
  sidebar,
  sidebarLeft = false,
  children,
  ...pageLayoutProps
}) => {
  return (
    <PageLayout {...pageLayoutProps}>
      {/* Page Header */}
      {(heading || subheading || actions) && (
        <div className="py-8 border-b border-neutral-200">
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              {heading && (
                <h1 className="text-3xl font-bold text-neutral-900 sm:text-4xl">
                  {heading}
                </h1>
              )}
              {subheading && (
                <p className="mt-2 text-lg text-neutral-600">
                  {subheading}
                </p>
              )}
            </div>
            {actions && (
              <div className="flex-shrink-0 ml-4">
                {actions}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Content with optional sidebar */}
      <div className="py-8">
        {sidebar ? (
          <div className={cn(
            'grid gap-8',
            sidebarLeft ? 'lg:grid-cols-[300px_1fr]' : 'lg:grid-cols-[1fr_300px]'
          )}>
            {sidebarLeft && (
              <aside className="lg:sticky lg:top-8 lg:self-start">
                {sidebar}
              </aside>
            )}
            
            <div className="min-w-0">
              {children}
            </div>
            
            {!sidebarLeft && (
              <aside className="lg:sticky lg:top-8 lg:self-start">
                {sidebar}
              </aside>
            )}
          </div>
        ) : (
          children
        )}
      </div>
    </PageLayout>
  );
};

// Specialized layout for dashboard/admin pages
export interface DashboardLayoutProps extends Omit<PageLayoutProps, 'children' | 'showHeader' | 'showFooter'> {
  /** Sidebar navigation items */
  sidebarItems?: React.ComponentProps<typeof import('./Navigation').SidebarNavigation>['items'];
  /** Whether sidebar is collapsed */
  sidebarCollapsed?: boolean;
  /** Sidebar header content */
  sidebarHeader?: React.ReactNode;
  /** Sidebar footer content */
  sidebarFooter?: React.ReactNode;
  /** Top navigation content */
  topNav?: React.ReactNode;
  /** Page content */
  children: React.ReactNode;
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  sidebarItems = [],
  sidebarCollapsed = false,
  sidebarHeader,
  sidebarFooter,
  topNav,
  children,
  breadcrumbs,
  ...pageLayoutProps
}) => {
  return (
    <div className="min-h-screen bg-neutral-50">
      <div className="flex h-screen">
        {/* Sidebar */}
        {sidebarItems.length > 0 && (
          <div className={cn(
            'flex-shrink-0 transition-all duration-300',
            sidebarCollapsed ? 'w-16' : 'w-64'
          )}>
            <div className="h-full bg-white border-r border-neutral-200">
              {/* Sidebar Header */}
              {sidebarHeader && (
                <div className="p-4 border-b border-neutral-200">
                  {sidebarHeader}
                </div>
              )}
              
              {/* Sidebar Navigation */}
              <div className="flex-1 overflow-y-auto p-4">
                <nav className="space-y-1">
                  {/* This would use the SidebarNavigation component */}
                  {/* Implementation would go here */}
                </nav>
              </div>
              
              {/* Sidebar Footer */}
              {sidebarFooter && (
                <div className="p-4 border-t border-neutral-200">
                  {sidebarFooter}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Top Navigation */}
          {topNav && (
            <div className="flex-shrink-0 bg-white border-b border-neutral-200 px-6 py-4">
              {topNav}
            </div>
          )}

          {/* Breadcrumbs */}
          {breadcrumbs && breadcrumbs.length > 0 && (
            <div className="flex-shrink-0 bg-neutral-50 border-b border-neutral-200 px-6 py-3">
              <Breadcrumbs items={breadcrumbs} />
            </div>
          )}

          {/* Page Content */}
          <main className="flex-1 overflow-y-auto p-6">
            <div className={cn(pageLayoutProps.className)}>
              {children}
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};

// Error layout for error pages
export interface ErrorLayoutProps {
  /** Error code */
  code?: string;
  /** Error title */
  title: string;
  /** Error description */
  description?: string;
  /** Action buttons */
  actions?: React.ReactNode;
  /** Custom illustration */
  illustration?: React.ReactNode;
}

export const ErrorLayout: React.FC<ErrorLayoutProps> = ({
  code,
  title,
  description,
  actions,
  illustration,
}) => {
  return (
    <PageLayout showFooter={false}>
      <div className="min-h-[60vh] flex items-center justify-center py-12">
        <div className="text-center max-w-md mx-auto">
          {/* Illustration */}
          {illustration ? (
            <div className="mb-8">
              {illustration}
            </div>
          ) : (
            <div className="mb-8">
              <div className="w-24 h-24 mx-auto bg-neutral-100 rounded-full flex items-center justify-center">
                <svg className="w-12 h-12 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            </div>
          )}

          {/* Error Code */}
          {code && (
            <div className="text-6xl font-bold text-primary mb-4">
              {code}
            </div>
          )}

          {/* Title */}
          <h1 className="text-2xl font-bold text-neutral-900 mb-4">
            {title}
          </h1>

          {/* Description */}
          {description && (
            <p className="text-neutral-600 mb-8">
              {description}
            </p>
          )}

          {/* Actions */}
          {actions && (
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {actions}
            </div>
          )}
        </div>
      </div>
    </PageLayout>
  );
};

/**
 * Cinematic Hero Component
 * Bali Property Scout Website
 * 
 * Engaging cinematic hero section with high-resolution Bali imagery,
 * translucent overlay card, and prominent CTAs for homepage.
 */

'use client';

import React from 'react';
import { ChatbotCTA } from '@/components/ui/ChatbotCTA';

export const CinematicHero: React.FC = () => {
  const handleBrowseLocations = () => {
    const locationsSection = document.getElementById('locations-section');
    locationsSection?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* High-Resolution Background */}
      <div className="absolute inset-0 z-0">
        <div
          className="w-full h-full bg-cover bg-center bg-no-repeat transform scale-105 transition-transform duration-[20s] ease-out"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=2400&auto=format&fit=crop&q=90')`
          }}
        />
        {/* Professional gradient overlay with brand colors */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/60 via-black/40 to-black/50" />
        <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-black/20" />
      </div>

      {/* Subtle floating elements - reduced for cleaner look */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-48 h-48 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-64 h-64 bg-white/8 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      {/* Clean Translucent Overlay Card */}
      <div className="relative z-10 max-w-5xl mx-auto px-6 text-center text-white section-spacing">
        <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-12 md:p-16 border border-white/20 shadow-2xl">
          {/* Concise Compelling Headline */}
          <h1 className="heading-1 text-4xl md:text-6xl lg:text-7xl font-bold mb-8 leading-tight text-white">
            Find Your Perfect
            <span className="block text-white">
              Bali Home
            </span>
          </h1>

          {/* Concise Sub-headline */}
          <p className="body-text-large text-lg md:text-xl mb-12 text-white/90 max-w-3xl mx-auto leading-relaxed">
            AI-powered property discovery meets local expertise for expats, digital nomads, and smart investors.
          </p>

          {/* Maximum Two High-Contrast CTAs */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <ChatbotCTA
              query={{ type: 'general' }}
              variant="primary"
              size="lg"
              className="btn-primary text-lg font-semibold px-8 py-4 min-h-[56px] shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300"
              style={{
                backgroundColor: 'var(--brand-primary)',
                borderColor: 'var(--brand-primary)',
                color: 'var(--neutral-white)'
              }}
            >
              Start Consultation
            </ChatbotCTA>

            <button
              onClick={handleBrowseLocations}
              className="btn-secondary text-lg font-semibold px-8 py-4 min-h-[56px] bg-transparent border-2 border-white text-white hover:bg-white hover:text-gray-900 shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
            >
              Browse Locations
            </button>
          </div>

          {/* Professional Trust Indicators - No Emojis */}
          <div className="mt-12 flex flex-wrap justify-center items-center gap-8 text-white/80">
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              <span className="text-sm font-medium">AI-Powered Search</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              <span className="text-sm font-medium">Local Expertise</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              <span className="text-sm font-medium">500+ Satisfied Clients</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CinematicHero;

import { <PERSON>ada<PERSON> } from 'next'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { getLocationBySlug, getLocations } from '@/lib/payload'
import ComprehensiveLocationContent from '@/components/location/ComprehensiveLocationContent'
import { Breadcrumbs } from '@/components/common/Breadcrumbs'
import { InternalLinking } from '@/components/seo/InternalLinking'
import { StickyAnchorNavigation } from '@/components/location/StickyAnchorNavigation'
import { ChatbotCTA } from '@/components/ui/ChatbotCTA'
import { InteractiveCoverageTags } from '@/components/ui/InteractiveCoverageTags'
import { LocationHeroBanner } from '@/components/location/LocationHeroBanner'
import PricingTable from '@/components/location/PricingTable'
import PricingChart from '@/components/location/PricingChart'
import LifestyleGrid from '@/components/location/LifestyleGrid'
import LifestyleAccordion from '@/components/location/LifestyleAccordion'
import PracticalInfoCards from '@/components/location/PracticalInfoCards'
import InteractiveFAQ from '@/components/location/InteractiveFAQ'
// AI components temporarily disabled

// Generate static params for all locations
export async function generateStaticParams() {
  // During build, return static params to avoid database connection issues
  if (process.env.NODE_ENV === 'production' && !process.env.DATABASE_URL?.includes('supabase')) {
    console.log('🔧 Using static params for locations build (no database connection)')
    return [
      { slug: 'canggu' },
      { slug: 'ubud' },
      { slug: 'seminyak' },
      { slug: 'sanur' },
      { slug: 'jimbaran' },
    ]
  }

  try {
    const locations = await getLocations()
    return locations.map((location: any) => ({
      slug: location.slug,
    }))
  } catch (error) {
    console.error('Error generating static params for locations:', error)
    // Return fallback params if database is not available
    return [
      { slug: 'canggu' },
      { slug: 'ubud' },
      { slug: 'seminyak' },
      { slug: 'sanur' },
      { slug: 'jimbaran' },
    ]
  }
}

// Generate metadata for each location
export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {
  const { slug } = await params
  const location = await getLocationBySlug(slug)
  
  if (!location) {
    return {
      title: 'Location Not Found',
    }
  }

  // Use custom meta description if available, otherwise generate from content
  const metaDescription = location.seoContent?.metaDescription ||
    location.description?.replace(/<[^>]*>/g, '').substring(0, 160) ||
    `Find long-term rentals and properties for sale in ${location.name}, Bali. Expert guidance for expats, digital nomads, and investors.`

  // Generate keywords from target keywords if available
  const keywords = location.seoContent?.targetKeywords?.map((k: any) => k.keyword).join(', ') ||
    `${location.name} Bali, ${location.name} rental, ${location.name} property, ${location.name} villa, Bali real estate`

  return {
    title: `${location.name} Long-term Rentals & Properties for Sale | Bali Real Estate`,
    description: metaDescription,
    keywords: keywords,
    openGraph: {
      title: `${location.name} - Bali Real Estate`,
      description: metaDescription,
      type: 'website',
    },
  }
}

export default async function LocationPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params
  const location = await getLocationBySlug(slug)

  if (!location) {
    notFound()
  }

  // Define navigation items for sticky anchor navigation
  const navigationItems = [
    { id: 'overview', label: 'Overview', icon: '📍' },
    { id: 'neighborhoods', label: 'Neighborhoods', icon: '🏘️' },
    { id: 'property-types', label: 'Property Types', icon: '🏠' },
    { id: 'lifestyle', label: 'Lifestyle', icon: '🌴' },
    { id: 'practical-info', label: 'Practical Info', icon: '📋' },
    { id: 'faq', label: 'FAQ', icon: '❓' },
  ];

  return (
    <main className="min-h-screen bg-white">
      {/* AI Context temporarily disabled */}

      {/* Breadcrumbs - PRD Requirement */}
      <div className="bg-gray-50 py-4">
        <div className="max-w-6xl mx-auto px-4">
          <Breadcrumbs />
        </div>
      </div>

      {/* Enhanced Hero Banner */}
      <LocationHeroBanner location={location} />

      {/* Enhanced Sticky Anchor Navigation */}
      <StickyAnchorNavigation
        items={navigationItems}
        stickyOffset={100}
      />

      {/* Location Overview */}
      <section id="overview" className="py-16">
        <div className="max-w-4xl mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8">About {location.name}</h2>
          <div className="prose prose-lg max-w-none">
            <div className="text-gray-700 leading-relaxed">
              {location.lifestyle ? (
                <div dangerouslySetInnerHTML={{ __html: location.lifestyle }} />
              ) : (
                <p>
                  {location.name} is one of Bali's most sought-after locations for long-term rentals and property investment. 
                  This area offers a unique blend of traditional Indonesian culture and modern amenities, making it perfect 
                  for expats, digital nomads, and property investors looking for the authentic Bali experience.
                </p>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Amenities Section */}
      {location.amenities && location.amenities.length > 0 && (
        <section className="py-16 bg-gray-50">
          <div className="max-w-6xl mx-auto px-4">
            <h2 className="text-3xl font-bold mb-8 text-center">What Makes {location.name} Special</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {location.amenities.map((amenity: any, index: number) => (
                <div key={index} className="bg-white rounded-lg p-6 shadow-sm">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-emerald-500 rounded-full mr-3"></div>
                    <span className="font-medium">{amenity.amenity}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Neighborhoods Section */}
      <section id="neighborhoods" className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8 text-center">Neighborhoods in {location.name}</h2>
          <p className="text-lg text-gray-600 text-center mb-12 max-w-3xl mx-auto">
            Discover the unique character and charm of different areas within {location.name}.
            Each neighborhood offers its own lifestyle, amenities, and community feel.
          </p>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Central Area */}
            <div className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 hover:-translate-y-2 group">
              <div className="relative h-56 overflow-hidden">
                <img
                  src={`https://images.unsplash.com/photo-1555881400-74d7acaacd8b?w=600&auto=format&fit=crop&q=80`}
                  alt={`Central ${location.name} area`}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                  loading="lazy"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                <div className="absolute bottom-4 left-4 text-white">
                  <h3 className="text-xl font-bold mb-1">Central {location.name}</h3>
                  <p className="text-emerald-200 text-sm">Main hub & business district</p>
                </div>
                {/* Mini Map Indicator */}
                <div className="absolute top-4 right-4 w-8 h-8 bg-white/90 rounded-full flex items-center justify-center">
                  <span className="text-emerald-600 text-sm">📍</span>
                </div>
              </div>
              <div className="p-6">
                {/* Best For Tags */}
                <div className="mb-4">
                  <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Best For</p>
                  <div className="flex flex-wrap gap-2">
                    <span className="px-3 py-1 bg-emerald-100 text-emerald-700 rounded-full text-sm font-medium">
                      🏪 Shopping
                    </span>
                    <span className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">
                      🍽️ Dining
                    </span>
                    <span className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm font-medium">
                      💼 Business
                    </span>
                  </div>
                </div>

                <p className="text-gray-600 text-sm leading-relaxed mb-4">
                  The bustling heart of {location.name} featuring the main commercial strip, international restaurants,
                  shopping centers, and business facilities. Ideal for those who want convenience and urban energy.
                </p>

                {/* Key Features */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-xs text-gray-500">
                    <span className="w-2 h-2 bg-emerald-500 rounded-full mr-2"></span>
                    Walking distance to main attractions
                  </div>
                  <div className="flex items-center text-xs text-gray-500">
                    <span className="w-2 h-2 bg-emerald-500 rounded-full mr-2"></span>
                    International dining & nightlife
                  </div>
                  <div className="flex items-center text-xs text-gray-500">
                    <span className="w-2 h-2 bg-emerald-500 rounded-full mr-2"></span>
                    Coworking spaces & business centers
                  </div>
                </div>

                {/* Mini Static Map */}
                <div className="bg-gray-100 rounded-lg p-3 mb-4">
                  <div className="flex items-center justify-between text-xs text-gray-600">
                    <span>📍 Central Location</span>
                    <span>🚶 5 min to main street</span>
                  </div>
                  <div className="mt-2 h-16 bg-gradient-to-r from-emerald-200 to-teal-200 rounded relative">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                    </div>
                    <div className="absolute bottom-1 left-2 text-xs text-gray-600">Main St</div>
                    <div className="absolute top-1 right-2 text-xs text-gray-600">Beach</div>
                  </div>
                </div>

                <ChatbotCTA
                  query={{ type: 'location', location: location.name, context: 'central-area' }}
                  className="w-full bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white py-2 px-4 rounded-lg text-sm font-medium transition-all duration-300 hover:shadow-lg"
                >
                  🏠 Find Properties Here
                </ChatbotCTA>
              </div>
            </div>

            {/* Residential Area */}
            <div className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 hover:-translate-y-2 group">
              <div className="relative h-56 overflow-hidden">
                <img
                  src={`https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=600&auto=format&fit=crop&q=80`}
                  alt={`Residential ${location.name} area`}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                  loading="lazy"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                <div className="absolute bottom-4 left-4 text-white">
                  <h3 className="text-xl font-bold mb-1">Residential {location.name}</h3>
                  <p className="text-green-200 text-sm">Local community & authentic living</p>
                </div>
                {/* Mini Map Indicator */}
                <div className="absolute top-4 right-4 w-8 h-8 bg-white/90 rounded-full flex items-center justify-center">
                  <span className="text-green-600 text-sm">🏡</span>
                </div>
              </div>
              <div className="p-6">
                {/* Best For Tags */}
                <div className="mb-4">
                  <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Best For</p>
                  <div className="flex flex-wrap gap-2">
                    <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm font-medium">
                      👨‍👩‍👧‍👦 Families
                    </span>
                    <span className="px-3 py-1 bg-indigo-100 text-indigo-700 rounded-full text-sm font-medium">
                      🌱 Local Culture
                    </span>
                    <span className="px-3 py-1 bg-orange-100 text-orange-700 rounded-full text-sm font-medium">
                      💰 Budget-Friendly
                    </span>
                  </div>
                </div>

                <p className="text-gray-600 text-sm leading-relaxed mb-4">
                  Peaceful residential streets lined with traditional homes, local warungs, and family-run businesses.
                  Perfect for those seeking authentic Balinese community life and long-term cultural immersion.
                </p>

                {/* Key Features */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-xs text-gray-500">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    Traditional Balinese architecture
                  </div>
                  <div className="flex items-center text-xs text-gray-500">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    Local warungs & markets
                  </div>
                  <div className="flex items-center text-xs text-gray-500">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    Family-friendly environment
                  </div>
                </div>

                {/* Mini Static Map */}
                <div className="bg-gray-100 rounded-lg p-3 mb-4">
                  <div className="flex items-center justify-between text-xs text-gray-600">
                    <span>🏡 Residential Zone</span>
                    <span>🚶 10 min to center</span>
                  </div>
                  <div className="mt-2 h-16 bg-gradient-to-r from-green-200 to-emerald-200 rounded relative">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    </div>
                    <div className="absolute bottom-1 left-2 text-xs text-gray-600">Local St</div>
                    <div className="absolute top-1 right-2 text-xs text-gray-600">Rice Fields</div>
                  </div>
                </div>

                <ChatbotCTA
                  query={{ type: 'location', location: location.name, context: 'residential-area' }}
                  className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white py-2 px-4 rounded-lg text-sm font-medium transition-all duration-300 hover:shadow-lg"
                >
                  🏡 Find Family Homes
                </ChatbotCTA>
              </div>
            </div>

            {/* Premium/Beachside Area */}
            <div className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105 hover:-translate-y-2 group">
              <div className="relative h-56 overflow-hidden">
                <img
                  src={`https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=600&auto=format&fit=crop&q=80`}
                  alt={`Premium ${location.name} beachside area`}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                  loading="lazy"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                <div className="absolute bottom-4 left-4 text-white">
                  <h3 className="text-xl font-bold mb-1">Premium {location.name}</h3>
                  <p className="text-blue-200 text-sm">Luxury beachfront & exclusive resorts</p>
                </div>
                {/* Mini Map Indicator */}
                <div className="absolute top-4 right-4 w-8 h-8 bg-white/90 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 text-sm">🏖️</span>
                </div>
              </div>
              <div className="p-6">
                {/* Best For Tags */}
                <div className="mb-4">
                  <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Best For</p>
                  <div className="flex flex-wrap gap-2">
                    <span className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">
                      ✨ Luxury Living
                    </span>
                    <span className="px-3 py-1 bg-yellow-100 text-yellow-700 rounded-full text-sm font-medium">
                      🏖️ Beach Access
                    </span>
                    <span className="px-3 py-1 bg-pink-100 text-pink-700 rounded-full text-sm font-medium">
                      🍸 Entertainment
                    </span>
                  </div>
                </div>

                <p className="text-gray-600 text-sm leading-relaxed mb-4">
                  Exclusive beachfront area featuring luxury villas, high-end beach clubs, premium spas, and upscale dining.
                  Ideal for those seeking sophisticated lifestyle with direct beach access and world-class amenities.
                </p>

                {/* Key Features */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-xs text-gray-500">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    Direct beach access & ocean views
                  </div>
                  <div className="flex items-center text-xs text-gray-500">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    Luxury beach clubs & fine dining
                  </div>
                  <div className="flex items-center text-xs text-gray-500">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    Premium spa & wellness centers
                  </div>
                </div>

                {/* Mini Static Map */}
                <div className="bg-gray-100 rounded-lg p-3 mb-4">
                  <div className="flex items-center justify-between text-xs text-gray-600">
                    <span>🏖️ Beachfront Zone</span>
                    <span>🚶 Beach access</span>
                  </div>
                  <div className="mt-2 h-16 bg-gradient-to-r from-blue-200 to-cyan-200 rounded relative">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                    </div>
                    <div className="absolute bottom-1 left-2 text-xs text-gray-600">Beach Club</div>
                    <div className="absolute top-1 right-2 text-xs text-gray-600">Ocean</div>
                  </div>
                </div>

                <ChatbotCTA
                  query={{ type: 'location', location: location.name, context: 'premium-beachfront' }}
                  className="w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white py-2 px-4 rounded-lg text-sm font-medium transition-all duration-300 hover:shadow-lg"
                >
                  🏖️ Find Luxury Villas
                </ChatbotCTA>
              </div>
            </div>
          </div>

          {/* Neighborhood CTA */}
          <div className="text-center mt-12">
            <ChatbotCTA
              query={{ type: 'location', location: location.name, context: 'neighborhoods' }}
              className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-8 py-4 rounded-2xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
            >
              🏘️ Find Properties in Specific Neighborhoods
            </ChatbotCTA>
          </div>
        </div>
      </section>

      {/* Property Types & Pricing */}
      <section id="property-types" className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Property Types & Pricing in {location.name}</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Comprehensive market data and pricing insights for different property types.
              Interactive charts and detailed breakdowns to help you make informed decisions.
            </p>
          </div>

          {/* Enhanced Pricing Table */}
          <div className="mb-12">
            <PricingTable
              locationName={location.name}
              pricingData={[
                {
                  propertyType: 'Private Villa',
                  icon: '🏡',
                  rentalRange: { min: 800, max: 3500, currency: 'USD' },
                  saleRange: { min: 150000, max: 800000, currency: 'USD' },
                  growthTrend: 8.5,
                  popularityScore: 9,
                  features: [
                    'Private swimming pool',
                    'Tropical garden setting',
                    'Full kitchen & living areas',
                    'Parking & security',
                    'Housekeeping service',
                    'Prime location access'
                  ],
                  description: `Private villas in ${location.name} offer the ultimate in luxury and privacy. These properties typically feature modern amenities, traditional Balinese architecture, and are perfect for families or groups seeking an authentic yet comfortable experience.`
                },
                {
                  propertyType: 'Modern Apartment',
                  icon: '🏢',
                  rentalRange: { min: 400, max: 1200, currency: 'USD' },
                  saleRange: { min: 80000, max: 300000, currency: 'USD' },
                  growthTrend: 6.2,
                  popularityScore: 7,
                  features: [
                    'Modern furnishing',
                    'Shared pool & gym',
                    'Security & maintenance',
                    'Central location',
                    'Utilities included',
                    'Community amenities'
                  ],
                  description: `Modern apartments provide convenient, low-maintenance living in ${location.name}. Ideal for digital nomads and young professionals, these properties offer great value with shared amenities and prime locations.`
                },
                {
                  propertyType: 'Traditional Guesthouse',
                  icon: '🏨',
                  rentalRange: { min: 300, max: 800, currency: 'USD' },
                  saleRange: { min: 60000, max: 200000, currency: 'USD' },
                  growthTrend: 4.1,
                  popularityScore: 6,
                  features: [
                    'Authentic Balinese design',
                    'Local community integration',
                    'Budget-friendly options',
                    'Cultural immersion',
                    'Traditional architecture',
                    'Local market access'
                  ],
                  description: `Traditional guesthouses offer authentic Balinese living experiences in ${location.name}. These properties provide cultural immersion and are perfect for budget-conscious travelers and long-term residents seeking local community connections.`
                },
                {
                  propertyType: 'Luxury Resort Villa',
                  icon: '🌟',
                  rentalRange: { min: 2000, max: 8000, currency: 'USD' },
                  saleRange: { min: 500000, max: 2000000, currency: 'USD' },
                  growthTrend: 12.3,
                  popularityScore: 10,
                  features: [
                    'Resort-style amenities',
                    'Concierge services',
                    'Premium locations',
                    'Infinity pools',
                    'Spa & wellness facilities',
                    'Investment potential'
                  ],
                  description: `Luxury resort villas represent the pinnacle of ${location.name} real estate. These premium properties offer world-class amenities, stunning locations, and exceptional investment potential for discerning buyers.`
                }
              ]}
            />
          </div>

          {/* Interactive Pricing Chart */}
          <div className="mb-12">
            <PricingChart
              locationName={location.name}
              pricingData={[
                {
                  propertyType: 'Private Villa',
                  rentalRange: { min: 800, max: 3500, currency: 'USD' },
                  saleRange: { min: 150000, max: 800000, currency: 'USD' },
                  growthTrend: 8.5,
                  popularityScore: 9
                },
                {
                  propertyType: 'Modern Apartment',
                  rentalRange: { min: 400, max: 1200, currency: 'USD' },
                  saleRange: { min: 80000, max: 300000, currency: 'USD' },
                  growthTrend: 6.2,
                  popularityScore: 7
                },
                {
                  propertyType: 'Traditional Guesthouse',
                  rentalRange: { min: 300, max: 800, currency: 'USD' },
                  saleRange: { min: 60000, max: 200000, currency: 'USD' },
                  growthTrend: 4.1,
                  popularityScore: 6
                },
                {
                  propertyType: 'Luxury Resort Villa',
                  rentalRange: { min: 2000, max: 8000, currency: 'USD' },
                  saleRange: { min: 500000, max: 2000000, currency: 'USD' },
                  growthTrend: 12.3,
                  popularityScore: 10
                }
              ]}
              chartType="rental"
            />
          </div>

          {/* Market Insights */}
          <div className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold mb-4 text-center">Market Insights for {location.name}</h3>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">📈</span>
                </div>
                <h4 className="font-semibold mb-2">Strong Growth</h4>
                <p className="text-sm text-gray-600">
                  Property values in {location.name} have shown consistent growth, with luxury segments leading the market.
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🌍</span>
                </div>
                <h4 className="font-semibold mb-2">International Appeal</h4>
                <p className="text-sm text-gray-600">
                  High demand from international buyers and renters makes {location.name} a stable investment choice.
                </p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">💎</span>
                </div>
                <h4 className="font-semibold mb-2">Premium Location</h4>
                <p className="text-sm text-gray-600">
                  Strategic location with excellent infrastructure makes {location.name} properties highly sought after.
                </p>
              </div>
            </div>

            <div className="text-center mt-8">
              <ChatbotCTA
                query={{ type: 'service', service: 'investment-consultation', location: location.name }}
                className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-8 py-4 rounded-2xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
              >
                💼 Get Investment Consultation
              </ChatbotCTA>
            </div>
          </div>
        </div>
      </section>

      {/* Lifestyle & Amenities */}
      <section id="lifestyle" className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Lifestyle in {location.name}</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Experience the perfect blend of modern convenience and traditional Balinese culture.
              Discover what makes {location.name} an exceptional place to live, work, and thrive.
            </p>
          </div>

          {/* Lifestyle Features Grid */}
          <div className="mb-16">
            <LifestyleGrid
              locationName={location.name}
              features={[
                {
                  id: 'coworking',
                  title: 'Coworking Spaces',
                  icon: '💻',
                  description: 'Modern coworking spaces with high-speed internet, meeting rooms, and vibrant communities.',
                  category: 'work',
                  rating: 5
                },
                {
                  id: 'dining',
                  title: 'International Dining',
                  icon: '🍽️',
                  description: 'World-class restaurants, local warungs, and international cuisine options.',
                  category: 'lifestyle',
                  rating: 5
                },
                {
                  id: 'wellness',
                  title: 'Health & Wellness',
                  icon: '🧘',
                  description: 'Yoga studios, spas, wellness centers, and international healthcare facilities.',
                  category: 'health',
                  rating: 4
                },
                {
                  id: 'nightlife',
                  title: 'Entertainment',
                  icon: '🎭',
                  description: 'Beach clubs, bars, cultural events, and vibrant nightlife scene.',
                  category: 'entertainment',
                  rating: 5
                },
                {
                  id: 'transport',
                  title: 'Transportation',
                  icon: '🛵',
                  description: 'Scooter rentals, ride-sharing, taxis, and easy airport access.',
                  category: 'transport',
                  rating: 4
                },
                {
                  id: 'community',
                  title: 'Expat Community',
                  icon: '👥',
                  description: 'Welcoming international community with networking events and social groups.',
                  category: 'community',
                  rating: 5
                },
                {
                  id: 'shopping',
                  title: 'Shopping',
                  icon: '🛍️',
                  description: 'Modern malls, local markets, boutique stores, and artisan shops.',
                  category: 'lifestyle',
                  rating: 4
                },
                {
                  id: 'fitness',
                  title: 'Fitness & Sports',
                  icon: '🏋️',
                  description: 'Gyms, fitness centers, swimming pools, and outdoor sports facilities.',
                  category: 'health',
                  rating: 4
                },
                {
                  id: 'internet',
                  title: 'High-Speed Internet',
                  icon: '📶',
                  description: 'Reliable fiber internet up to 100+ Mbps, perfect for remote work.',
                  category: 'work',
                  rating: 5
                }
              ]}
              columns={3}
            />
          </div>

          {/* Detailed Lifestyle Accordions */}
          <div className="mb-16">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold mb-2">Explore {location.name} Lifestyle</h3>
              <p className="text-gray-600">
                Click to discover detailed information about living, working, and enjoying life in {location.name}
              </p>
            </div>

            <LifestyleAccordion
              locationName={location.name}
              items={[
                {
                  id: 'living-experience',
                  title: 'Living Experience',
                  icon: '🏡',
                  summary: 'Modern conveniences meet traditional Balinese culture in perfect harmony',
                  details: {
                    description: `${location.name} offers a unique lifestyle that perfectly balances modern conveniences with traditional Balinese culture. The area is known for its welcoming expat community, excellent infrastructure, and diverse dining and entertainment options that cater to international residents while maintaining authentic local charm.`,
                    features: [
                      'Modern infrastructure with traditional charm',
                      'Welcoming international community',
                      'Excellent dining and entertainment options',
                      'Safe and secure environment',
                      'Easy access to cultural attractions',
                      'Year-round tropical climate'
                    ],
                    highlights: [
                      'Perfect blend of modern and traditional',
                      'Strong expat support network',
                      'Rich cultural experiences',
                      'Affordable cost of living',
                      'Beautiful natural surroundings',
                      'Friendly local community'
                    ]
                  },
                  ctaText: '🏠 Find Your Perfect Home',
                  ctaQuery: { type: 'property-search', context: 'living-experience' }
                },
                {
                  id: 'digital-nomad',
                  title: 'Digital Nomad Hub',
                  icon: '💻',
                  summary: 'Thriving remote work community with world-class infrastructure',
                  details: {
                    description: `With reliable high-speed internet, numerous coworking spaces, and a thriving community of remote workers, ${location.name} has become a top destination for digital nomads and remote professionals. The area offers the perfect work-life balance with modern amenities and inspiring surroundings.`,
                    features: [
                      'High-speed fiber internet (100+ Mbps)',
                      'Multiple coworking spaces and cafes',
                      'Reliable power supply and backup',
                      'Time zone advantages for global work',
                      'Professional networking events',
                      'Affordable workspace options'
                    ],
                    highlights: [
                      'Top-rated digital nomad destination',
                      'Strong remote work community',
                      'Excellent work-life balance',
                      'Inspiring creative environment',
                      'Networking opportunities',
                      'Cost-effective living'
                    ]
                  },
                  ctaText: '💼 Explore Coworking Options',
                  ctaQuery: { type: 'service', context: 'coworking-spaces' }
                },
                {
                  id: 'health-wellness',
                  title: 'Health & Wellness',
                  icon: '🌿',
                  summary: 'Comprehensive healthcare and wellness facilities for optimal living',
                  details: {
                    description: `${location.name} prioritizes health and wellness with international-standard healthcare facilities, numerous wellness centers, and a culture that embraces healthy living. From yoga studios to modern hospitals, all your health needs are covered.`,
                    features: [
                      'International hospitals and clinics',
                      'Yoga studios and meditation centers',
                      'Spa and wellness facilities',
                      'Fitness centers and gyms',
                      'Healthy dining options',
                      'Traditional healing practices'
                    ],
                    highlights: [
                      'World-class healthcare facilities',
                      'Wellness-focused lifestyle',
                      'Traditional and modern medicine',
                      'Active outdoor lifestyle',
                      'Stress-free environment',
                      'Holistic health approach'
                    ]
                  },
                  ctaText: '🏥 Healthcare Information',
                  ctaQuery: { type: 'service', context: 'healthcare-wellness' }
                }
              ]}
            />
          </div>

          {/* Lifestyle CTA Section */}
          <div className="bg-gradient-to-r from-emerald-600 to-teal-600 rounded-2xl p-8 text-center text-white">
            <h3 className="text-2xl font-bold mb-4">Ready to Experience {location.name}?</h3>
            <p className="text-emerald-100 mb-6 max-w-2xl mx-auto">
              Discover properties that match your lifestyle preferences and start your journey
              to the perfect Bali living experience.
            </p>
            <ChatbotCTA
              query={{ type: 'lifestyle-match', location: location.name, context: 'comprehensive-search' }}
              className="bg-white text-emerald-600 hover:bg-gray-100 px-8 py-4 rounded-2xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
            >
              🌴 Find Your Lifestyle Match
            </ChatbotCTA>
          </div>
        </div>
      </section>

      {/* Transportation & Accessibility */}
      <section className="py-16">
        <div className="max-w-6xl mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8">Transportation & Accessibility</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">✈️</span>
              </div>
              <h3 className="font-semibold mb-2">Airport Access</h3>
              <p className="text-gray-600">30-60 minutes to Ngurah Rai International Airport</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🛵</span>
              </div>
              <h3 className="font-semibold mb-2">Local Transport</h3>
              <p className="text-gray-600">Scooter rentals, taxis, and ride-sharing available</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🚶</span>
              </div>
              <h3 className="font-semibold mb-2">Walkability</h3>
              <p className="text-gray-600">Many amenities within walking or short scooter distance</p>
            </div>
          </div>
        </div>
      </section>

      {/* Explore Nearby Areas */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4">
          <InteractiveCoverageTags
            tags={[
              { id: `${location.slug}-center`, label: `Central ${location.name}`, type: 'location', context: location.name, icon: '🏛️', description: 'Main area with shops and restaurants' },
              { id: `${location.slug}-beach`, label: `${location.name} Beach`, type: 'location', context: location.name, icon: '🏖️', description: 'Beachfront properties and resorts' },
              { id: `${location.slug}-hills`, label: `${location.name} Hills`, type: 'location', context: location.name, icon: '⛰️', description: 'Elevated areas with views' },
              { id: 'villa-rental', label: 'Villa Rentals', type: 'property-type', context: location.name, icon: '🏡', description: 'Private villas for long-term rent' },
              { id: 'villa-sale', label: 'Villas for Sale', type: 'property-type', context: location.name, icon: '🏠', description: 'Purchase opportunities' },
              { id: 'guesthouse', label: 'Guesthouses', type: 'property-type', context: location.name, icon: '🏨', description: 'Authentic local accommodations' },
            ]}
            title={`Explore ${location.name} Areas & Properties`}
            description={`Discover different neighborhoods and property types in ${location.name} with our AI assistant`}
            variant="inline"
            showPriority={false}
            animated={true}
          />
        </div>
      </section>

      {/* Practical Information Section */}
      <section id="practical-info" className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <PracticalInfoCards
            locationName={location.name}
            infoCards={[
              {
                id: 'visa-legal',
                title: 'Visa & Legal Requirements',
                icon: '📋',
                category: 'visa',
                priority: 'critical',
                items: [
                  {
                    title: 'Tourist Visas (B211/B212)',
                    description: 'Short-term stays for tourism and business',
                    details: [
                      'B211: 30 days, extendable once for 30 days',
                      'B212: 60 days on arrival, extendable once',
                      'Valid passport required (6+ months)',
                      'Return ticket may be required'
                    ],
                    highlight: true
                  },
                  {
                    title: 'Social/Cultural Visa (B213)',
                    description: '6-month visa for cultural activities and learning',
                    details: [
                      'Initial 60 days, extendable 4 times',
                      'Requires sponsor or cultural institution',
                      'Popular for digital nomads and students',
                      'Cannot be used for business activities'
                    ]
                  },
                  {
                    title: 'Residence Permit (Kitas)',
                    description: '1-year renewable residence permit',
                    details: [
                      'Requires Indonesian sponsor or company',
                      'Allows multiple entries and exits',
                      'Can be renewed annually',
                      'Required for long-term residents'
                    ]
                  }
                ],
                ctaText: '📋 Get Visa Consultation',
                ctaQuery: { type: 'service', context: 'visa-consultation' }
              },
              {
                id: 'property-ownership',
                title: 'Property Ownership & Investment',
                icon: '🏠',
                category: 'ownership',
                priority: 'critical',
                items: [
                  {
                    title: 'Leasehold Ownership',
                    description: 'Most common ownership structure for foreigners',
                    details: [
                      'Initial lease: 25-30 years',
                      'Renewable for additional 25-30 years',
                      'Transferable to heirs or buyers',
                      'Lower initial investment than freehold'
                    ],
                    highlight: true
                  },
                  {
                    title: 'Freehold via Indonesian Entity',
                    description: 'Full ownership through Indonesian company (PT PMA)',
                    details: [
                      'Requires Indonesian company setup',
                      'Foreign ownership up to 49%',
                      'Higher setup costs and ongoing compliance',
                      'Full ownership rights and control'
                    ]
                  },
                  {
                    title: 'Investment Considerations',
                    description: 'Key factors for property investment decisions',
                    details: [
                      'Location and accessibility',
                      'Rental yield potential (8-12% typical)',
                      'Property management requirements',
                      'Exit strategy and resale market'
                    ]
                  }
                ],
                ctaText: '🏠 Property Investment Advice',
                ctaQuery: { type: 'service', context: 'investment-consultation' }
              },
              {
                id: 'tax-obligations',
                title: 'Tax Considerations',
                icon: '💰',
                category: 'tax',
                priority: 'important',
                items: [
                  {
                    title: 'Income Tax for Residents',
                    description: 'Tax obligations for Indonesian tax residents',
                    details: [
                      'Progressive rates: 5% to 30%',
                      'Tax resident if >183 days/year in Indonesia',
                      'Global income subject to Indonesian tax',
                      'Tax treaties may provide relief'
                    ],
                    highlight: true
                  },
                  {
                    title: 'Property Tax (PBB)',
                    description: 'Annual property tax on land and buildings',
                    details: [
                      'Rate: 0.1% to 0.3% of assessed value',
                      'Based on government property valuation',
                      'Paid annually by property owner',
                      'Relatively low compared to Western countries'
                    ]
                  },
                  {
                    title: 'Transfer Tax & Fees',
                    description: 'Costs associated with property transactions',
                    details: [
                      'Transfer tax: 2.5% of transaction value',
                      'Notary fees: 0.5-1% of property value',
                      'Registration fees and administrative costs',
                      'Due diligence and legal fees'
                    ]
                  }
                ],
                ctaText: '💰 Tax Planning Consultation',
                ctaQuery: { type: 'service', context: 'tax-consultation' }
              },
              {
                id: 'banking-finance',
                title: 'Banking & Finance',
                icon: '🏦',
                category: 'banking',
                priority: 'important',
                items: [
                  {
                    title: 'Local Bank Account',
                    description: 'Opening Indonesian bank account requirements',
                    details: [
                      'Kitas required for full account features',
                      'Tourist visa allows basic savings account',
                      'Major banks: BCA, Mandiri, BNI, BRI',
                      'Mobile banking widely available'
                    ]
                  },
                  {
                    title: 'International Banking',
                    description: 'Managing finances across borders',
                    details: [
                      'Wise, Revolut for international transfers',
                      'HSBC, Citibank for expat banking',
                      'Currency exchange considerations',
                      'Investment and savings options'
                    ],
                    highlight: true
                  },
                  {
                    title: 'Property Financing',
                    description: 'Mortgage and financing options',
                    details: [
                      'Local banks offer mortgages to residents',
                      'Down payment: typically 30-40%',
                      'Interest rates: 8-12% annually',
                      'Alternative financing through developers'
                    ]
                  }
                ],
                ctaText: '🏦 Banking Setup Assistance',
                ctaQuery: { type: 'service', context: 'banking-assistance' }
              },
              {
                id: 'healthcare-insurance',
                title: 'Healthcare & Insurance',
                icon: '🏥',
                category: 'healthcare',
                priority: 'critical',
                items: [
                  {
                    title: 'International Hospitals',
                    description: 'World-class healthcare facilities',
                    details: [
                      'BIMC Hospital (international standard)',
                      'Siloam Hospitals (modern facilities)',
                      'Sanglah Hospital (government hospital)',
                      'English-speaking medical staff available'
                    ]
                  },
                  {
                    title: 'Health Insurance',
                    description: 'Essential coverage for expats and residents',
                    details: [
                      'International health insurance recommended',
                      'Local BPJS for residents with Kitas',
                      'Travel insurance for short-term visitors',
                      'Coverage for emergency evacuation advised'
                    ],
                    highlight: true
                  },
                  {
                    title: 'Pharmacies & Clinics',
                    description: 'Accessible healthcare services',
                    details: [
                      'Guardian, Kimia Farma pharmacy chains',
                      'Local clinics (Puskesmas) widely available',
                      'Prescription medications readily available',
                      '24-hour emergency services in main areas'
                    ]
                  }
                ],
                ctaText: '🏥 Healthcare Planning',
                ctaQuery: { type: 'service', context: 'healthcare-consultation' }
              },
              {
                id: 'transportation-mobility',
                title: 'Transportation & Mobility',
                icon: '🛵',
                category: 'transport',
                priority: 'helpful',
                items: [
                  {
                    title: 'Scooter Transportation',
                    description: 'Most popular and practical transport option',
                    details: [
                      'Rental: $50-80/month long-term',
                      'International driving license required',
                      'Local license available for residents',
                      'Helmet mandatory, traffic can be chaotic'
                    ],
                    highlight: true
                  },
                  {
                    title: 'Ride-Sharing & Taxis',
                    description: 'Convenient alternatives to personal transport',
                    details: [
                      'Grab and Gojek widely available',
                      'Blue Bird taxis (metered and reliable)',
                      'Airport transfers and long-distance trips',
                      'Reasonable rates for daily use'
                    ]
                  },
                  {
                    title: 'Airport & Long-Distance',
                    description: 'Connections to other destinations',
                    details: [
                      'Ngurah Rai Airport: 30-90 minutes depending on location',
                      'Fast boat services to Gili Islands, Lombok',
                      'Domestic flights to other Indonesian cities',
                      'Car rental available for longer trips'
                    ]
                  }
                ],
                ctaText: '🛵 Transportation Guide',
                ctaQuery: { type: 'service', context: 'transportation-guide' }
              }
            ]}
          />
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4">
          <InteractiveFAQ
            locationName={location.name}
            faqItems={[
              {
                id: 'cost-of-living',
                question: `What's the average cost of living in ${location.name}?`,
                answer: `Living costs in ${location.name} vary significantly based on lifestyle choices and accommodation preferences. Here's a breakdown:

Budget Living: $800-1,200/month
- Local villa or guesthouse: $300-500/month
- Local food and warungs: $150-250/month
- Scooter rental and transport: $50-80/month
- Utilities and internet: $50-100/month
- Entertainment and miscellaneous: $200-300/month

Comfortable Living: $1,200-2,500/month
- Modern villa with pool: $600-1,200/month
- Mix of local and international dining: $300-500/month
- Car rental or frequent taxis: $100-200/month
- Higher-end utilities and services: $100-150/month
- Regular entertainment and activities: $400-600/month

Luxury Living: $2,500+/month
- Premium beachfront villa: $1,500-3,000/month
- Fine dining and international cuisine: $600-1,000/month
- Private driver or luxury transport: $300-500/month
- Premium services and amenities: $200-300/month
- High-end entertainment and experiences: $800+/month`,
                category: 'cost',
                priority: 'high',
                relatedTopics: ['Property Rental', 'Transportation', 'Dining', 'Utilities']
              },
              {
                id: 'visa-requirements',
                question: `How long can foreigners stay in ${location.name}?`,
                answer: `Foreigners have several visa options for staying in ${location.name}:

Visa-Free Entry (30 days):
- Available for many nationalities
- Cannot be extended
- Suitable for short visits only

Visa on Arrival - B212 (60 days):
- $35 USD fee
- Extendable once for additional 30 days
- Total maximum stay: 90 days

Social/Cultural Visa - B213 (6 months):
- Requires Indonesian sponsor
- Initial 60 days, extendable 4 times (30 days each)
- Popular among digital nomads
- Cannot work legally on this visa

Residence Permit - Kitas (1 year):
- Requires Indonesian sponsor or company
- Renewable annually
- Allows multiple entries/exits
- Various categories: investment, retirement, family

For longer stays or work purposes, consult with a visa agent or immigration lawyer to determine the best option for your specific situation.`,
                category: 'visa',
                priority: 'high',
                relatedTopics: ['Immigration', 'Work Permits', 'Sponsorship', 'Legal Requirements']
              },
              {
                id: 'safety-security',
                question: `Is it safe for foreigners to live in ${location.name}?`,
                answer: `${location.name} is generally considered very safe for foreigners, with thousands of expats living comfortably in the area. Here's what you should know:

General Safety:
- Low violent crime rates
- Large, established expat community
- Good police presence in tourist areas
- English-speaking support available

Common Safety Considerations:
- Traffic safety (scooter accidents are the main risk)
- Petty theft in crowded areas
- Natural disasters (earthquakes, volcanic activity)
- Food and water safety for newcomers

Safety Tips:
- Always wear a helmet when riding scooters
- Use reputable transportation services
- Keep valuables secure and avoid displaying wealth
- Have comprehensive health insurance
- Register with your embassy if staying long-term
- Learn basic Indonesian phrases for emergencies

Emergency Contacts:
- Police: 110
- Medical Emergency: 118
- Fire Department: 113
- Tourist Police: Available in main areas

The expat community is very supportive, and local authorities are generally helpful to foreigners. Most safety issues can be avoided with common sense and basic precautions.`,
                category: 'safety',
                priority: 'high',
                relatedTopics: ['Emergency Services', 'Health Insurance', 'Transportation Safety', 'Expat Community']
              },
              {
                id: 'internet-speed',
                question: `What's the internet speed like in ${location.name}?`,
                answer: `Internet connectivity in ${location.name} has improved significantly and is generally suitable for remote work and digital nomads:

Typical Speeds:
- Fiber connections: 50-100+ Mbps
- Standard broadband: 20-50 Mbps
- Mobile data: 10-30 Mbps (4G/LTE)
- Coworking spaces: 100+ Mbps

Internet Providers:
- Telkom Indonesia (IndiHome): Most reliable fiber
- Biznet: High-speed fiber in select areas
- MyRepublic: Good speeds, growing coverage
- Mobile providers: Telkomsel, XL, Indosat

Considerations:
- Speed can vary by exact location
- Rainy season may affect connectivity
- Power outages can disrupt service
- Backup mobile data recommended
- Some areas still have limited fiber coverage

Tips for Remote Workers:
- Test internet before committing to accommodation
- Have multiple backup options (mobile hotspot, coworking spaces)
- Consider locations with established digital nomad communities
- Invest in a good router if staying long-term
- Use VPN for accessing geo-restricted content

Most modern villas and coworking spaces offer reliable internet suitable for video calls, streaming, and professional work.`,
                category: 'internet',
                priority: 'medium',
                relatedTopics: ['Remote Work', 'Coworking Spaces', 'Digital Nomads', 'Technology']
              },
              {
                id: 'property-ownership',
                question: `Can foreigners buy property in ${location.name}?`,
                answer: `Foreigners cannot directly own freehold land in Indonesia, but there are legal ways to control property in ${location.name}:

Leasehold (Hak Sewa):
- Most common option for foreigners
- Initial lease: 25-30 years
- Renewable for additional 25-30 years
- Transferable to heirs or buyers
- Lower initial cost than other options

Right to Use (Hak Pakai):
- Available to foreign residents with Kitas
- Initial period: 25-30 years
- Renewable twice (25 years each time)
- Can be converted to leasehold
- Requires residence permit

Indonesian Company (PT PMA):
- Foreign ownership up to 49%
- Full property control through company
- Higher setup and maintenance costs
- Requires ongoing compliance
- Professional management recommended

Indonesian Nominee:
- Property held by Indonesian citizen
- High legal risks
- Not recommended by most lawyers
- Potential for disputes

Important Considerations:
- Always use qualified legal counsel
- Conduct thorough due diligence
- Understand all costs and obligations
- Consider exit strategy
- Verify all permits and certificates

Most expats choose leasehold for its simplicity and legal security. Consult with a reputable property lawyer before making any commitments.`,
                category: 'legal',
                priority: 'high',
                relatedTopics: ['Property Investment', 'Legal Requirements', 'Due Diligence', 'Leasehold']
              },
              {
                id: 'healthcare-system',
                question: `What healthcare options are available in ${location.name}?`,
                answer: `${location.name} offers a range of healthcare options from basic local clinics to international-standard hospitals:

International Hospitals:
- BIMC Hospital: International standard, English-speaking staff
- Siloam Hospitals: Modern facilities, good reputation
- Prima Medika: Quality care, reasonable prices
- International SOS: Emergency and evacuation services

Local Healthcare:
- Puskesmas: Government health centers (basic care)
- Private clinics: Affordable, varying quality
- Pharmacies: Widely available (Guardian, Kimia Farma)
- Traditional healers: Balian and alternative medicine

Health Insurance Options:
- International health insurance (recommended)
- BPJS (Indonesian national insurance for residents)
- Travel insurance (short-term visitors)
- Private hospital insurance plans

Common Health Considerations:
- Tropical diseases (dengue, malaria in some areas)
- Food and water safety for newcomers
- Air pollution in busy areas
- Sun exposure and dehydration
- Scooter accident injuries

Preventive Measures:
- Comprehensive health insurance is essential
- Regular health check-ups
- Vaccinations as recommended
- Water filtration or bottled water
- Mosquito protection
- Sun protection and hydration

Emergency Services:
- Ambulance: 118
- International hospitals have 24/7 emergency services
- Medical evacuation available through insurance
- Tourist police can assist with medical emergencies

Most expats find healthcare adequate for routine needs, with evacuation insurance for serious conditions.`,
                category: 'general',
                priority: 'medium',
                relatedTopics: ['Health Insurance', 'Emergency Services', 'Preventive Care', 'Medical Tourism']
              },
              {
                id: 'transportation-options',
                question: `What are the transportation options in ${location.name}?`,
                answer: `${location.name} offers various transportation options suitable for different needs and budgets:

Scooter/Motorbike:
- Most popular and practical option
- Rental: $50-80/month long-term
- Daily rental: $5-8/day
- International driving license required
- Local license available for residents
- Always wear helmet (mandatory)

Ride-Sharing Apps:
- Grab: Most reliable, car and motorbike options
- Gojek: Local alternative, various services
- Available 24/7 in most areas
- Reasonable prices for daily use
- Good for airport transfers

Taxis:
- Blue Bird: Metered, reliable, English-speaking drivers
- Airport taxis: Fixed rates, safe option
- Hotel taxis: More expensive but convenient
- Negotiate price for longer trips

Car Rental:
- Daily: $25-40/day
- Monthly: $300-500/month
- International driving license required
- Consider traffic and parking challenges
- Good for families or longer trips

Public Transport:
- Bemo: Local minibuses (limited routes)
- Tourist shuttles: Between main destinations
- Trans Sarbagita: Bus system (limited coverage)

Airport Connections:
- Ngurah Rai Airport: 30-90 minutes depending on location
- Airport taxi: $15-25 to most areas
- Grab: Often cheaper alternative
- Hotel transfers: Convenient but expensive

Tips:
- Traffic can be heavy, especially during peak hours
- Rainy season affects all transport options
- Always negotiate prices in advance for non-metered transport
- Keep contact details for reliable drivers
- Consider location when choosing accommodation to minimize transport needs`,
                category: 'general',
                priority: 'medium',
                relatedTopics: ['Scooter Rental', 'Driving License', 'Airport Transfer', 'Traffic']
              }
            ]}
          />
        </div>
      </section>

      {/* STORY-011: Comprehensive Location Content */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Complete {location.name} Guide
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Everything you need to know about living, investing, and thriving in {location.name}.
              From market analysis to practical living information.
            </p>
          </div>

          <ComprehensiveLocationContent locationSlug={location.slug} />
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16 bg-gradient-to-r from-emerald-600 to-teal-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="text-white">
              <h2 className="text-3xl font-bold mb-6">Ready to Explore {location.name}?</h2>
              <p className="text-xl mb-8 text-emerald-100">
                Get personalized recommendations and expert guidance for your {location.name} property search.
                Our local specialists know every neighborhood and can help you find the perfect match.
              </p>

              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0 w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="ml-3 text-emerald-100">Local {location.name} expertise</span>
                </div>

                <div className="flex items-center">
                  <div className="flex-shrink-0 w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="ml-3 text-emerald-100">Personalized property recommendations</span>
                </div>

                <div className="flex items-center">
                  <div className="flex-shrink-0 w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="ml-3 text-emerald-100">Free market analysis and consultation</span>
                </div>
              </div>

              <div className="mt-8">
                <Link
                  href="/locations"
                  className="inline-flex items-center text-emerald-200 hover:text-white font-medium"
                >
                  Compare Other Locations
                  <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </Link>
              </div>
            </div>

            <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-200">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Find Your Perfect {location.name} Property</h3>
              <p className="text-gray-600 mb-6">Get expert local guidance and recommendations</p>
              <div className="flex flex-col sm:flex-row gap-4">
                <a
                  href={`https://app.bali-realestate.com/chatbot?query=find-property-in-${location.slug}`}
                  className="flex-1 bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-xl font-semibold text-center transition-all duration-300 hover:scale-105"
                  target="_self"
                >
                  Find Properties in {location.name}
                </a>
                <a
                  href="/contact"
                  className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 px-6 py-3 rounded-xl font-semibold text-center transition-all duration-300 hover:scale-105"
                >
                  Contact Expert
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* STORY-003: Internal Linking for SEO */}
      <InternalLinking
        currentPage="location"
        location={location.slug}
      />
    </main>
  )
}

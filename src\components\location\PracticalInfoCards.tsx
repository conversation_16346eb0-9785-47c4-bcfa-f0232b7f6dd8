/**
 * Practical Info Cards Component
 * Bali Property Scout Website
 * 
 * Organized cards for visa types, tax considerations, foreign ownership,
 * and other practical information with highlight boxes and clear lists.
 */

'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import ChatbotCTA from '@/components/ui/ChatbotCTA';

export interface InfoCard {
  id: string;
  title: string;
  icon: string;
  category: 'visa' | 'tax' | 'ownership' | 'banking' | 'healthcare' | 'transport';
  priority: 'critical' | 'important' | 'helpful';
  items: {
    title: string;
    description: string;
    details?: string[];
    highlight?: boolean;
  }[];
  ctaText?: string;
  ctaQuery?: {
    type: string;
    context: string;
  };
}

export interface PracticalInfoCardsProps {
  /** Location name for context */
  locationName: string;
  /** Info cards to display */
  infoCards: InfoCard[];
  /** Additional CSS classes */
  className?: string;
}

export const PracticalInfoCards: React.FC<PracticalInfoCardsProps> = ({
  locationName,
  infoCards,
  className,
}) => {
  // Get category styling
  const getCategoryStyle = (category: InfoCard['category']) => {
    const styles = {
      visa: {
        bg: 'from-blue-500 to-indigo-500',
        cardBg: 'bg-blue-50 border-blue-200',
        textColor: 'text-blue-900',
        accentColor: 'text-blue-600'
      },
      tax: {
        bg: 'from-green-500 to-emerald-500',
        cardBg: 'bg-green-50 border-green-200',
        textColor: 'text-green-900',
        accentColor: 'text-green-600'
      },
      ownership: {
        bg: 'from-purple-500 to-pink-500',
        cardBg: 'bg-purple-50 border-purple-200',
        textColor: 'text-purple-900',
        accentColor: 'text-purple-600'
      },
      banking: {
        bg: 'from-yellow-500 to-orange-500',
        cardBg: 'bg-yellow-50 border-yellow-200',
        textColor: 'text-yellow-900',
        accentColor: 'text-yellow-600'
      },
      healthcare: {
        bg: 'from-red-500 to-pink-500',
        cardBg: 'bg-red-50 border-red-200',
        textColor: 'text-red-900',
        accentColor: 'text-red-600'
      },
      transport: {
        bg: 'from-orange-500 to-red-500',
        cardBg: 'bg-orange-50 border-orange-200',
        textColor: 'text-orange-900',
        accentColor: 'text-orange-600'
      }
    };
    return styles[category];
  };

  // Get priority styling
  const getPriorityBadge = (priority: InfoCard['priority']) => {
    const badges = {
      critical: { text: '🚨 Critical', style: 'bg-red-100 text-red-700 border-red-200' },
      important: { text: '⚡ Important', style: 'bg-yellow-100 text-yellow-700 border-yellow-200' },
      helpful: { text: '💡 Helpful', style: 'bg-blue-100 text-blue-700 border-blue-200' }
    };
    return badges[priority];
  };

  return (
    <div className={cn('', className)}>
      {/* Header */}
      <div className="text-center mb-12">
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          Essential Practical Information
        </h3>
        <p className="text-gray-600 max-w-3xl mx-auto">
          Everything you need to know about visas, taxes, property ownership, and living practicalities in {locationName}. 
          Organized by priority and category for easy reference.
        </p>
      </div>

      {/* Info Cards Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
        {infoCards.map((card) => {
          const categoryStyle = getCategoryStyle(card.category);
          const priorityBadge = getPriorityBadge(card.priority);
          
          return (
            <div
              key={card.id}
              className={cn(
                'bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 overflow-hidden border-2',
                categoryStyle.cardBg
              )}
            >
              {/* Card Header */}
              <div className="p-6 pb-4">
                <div className="flex items-center justify-between mb-4">
                  <div
                    className={cn(
                      'w-12 h-12 rounded-xl bg-gradient-to-br flex items-center justify-center',
                      categoryStyle.bg
                    )}
                  >
                    <span className="text-2xl text-white">{card.icon}</span>
                  </div>
                  
                  {/* Priority Badge */}
                  <div className={cn('px-3 py-1 rounded-full text-xs font-medium border', priorityBadge.style)}>
                    {priorityBadge.text}
                  </div>
                </div>
                
                <h4 className={cn('text-xl font-bold mb-2', categoryStyle.textColor)}>
                  {card.title}
                </h4>
              </div>

              {/* Card Content */}
              <div className="px-6 pb-6">
                <div className="space-y-4">
                  {card.items.map((item, index) => (
                    <div
                      key={index}
                      className={cn(
                        'p-4 rounded-xl transition-all duration-200',
                        item.highlight 
                          ? 'bg-white border-2 border-dashed shadow-sm' + ' ' + categoryStyle.accentColor.replace('text-', 'border-')
                          : 'bg-white/50'
                      )}
                    >
                      <div className="flex items-start">
                        <div className={cn('w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0', categoryStyle.bg)}></div>
                        <div className="flex-1">
                          <h5 className={cn('font-semibold mb-1', categoryStyle.textColor)}>
                            {item.title}
                          </h5>
                          <p className="text-gray-700 text-sm leading-relaxed mb-2">
                            {item.description}
                          </p>
                          
                          {/* Details List */}
                          {item.details && item.details.length > 0 && (
                            <ul className="space-y-1">
                              {item.details.map((detail, detailIndex) => (
                                <li key={detailIndex} className="flex items-start text-xs text-gray-600">
                                  <span className="w-1 h-1 bg-gray-400 rounded-full mt-1.5 mr-2 flex-shrink-0"></span>
                                  <span>{detail}</span>
                                </li>
                              ))}
                            </ul>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Card CTA */}
                {card.ctaText && card.ctaQuery && (
                  <div className="mt-6 pt-4 border-t border-gray-200">
                    <ChatbotCTA
                      query={{
                        ...card.ctaQuery,
                        location: locationName,
                      }}
                      className={cn(
                        'w-full py-2 px-4 rounded-xl text-sm font-medium transition-all duration-300 hover:shadow-lg',
                        `bg-gradient-to-r ${categoryStyle.bg} text-white hover:scale-105`
                      )}
                    >
                      {card.ctaText}
                    </ChatbotCTA>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Summary Section */}
      <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl p-8">
        <div className="text-center mb-6">
          <h4 className="text-xl font-bold text-gray-900 mb-2">
            Quick Reference Summary
          </h4>
          <p className="text-gray-600">
            Key points to remember when planning your move to {locationName}
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Critical Points */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <div className="flex items-center mb-3">
              <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                <span className="text-red-600 text-sm">🚨</span>
              </div>
              <h5 className="font-semibold text-gray-900">Critical</h5>
            </div>
            <ul className="space-y-1 text-sm text-gray-600">
              <li>• Valid visa required</li>
              <li>• Health insurance mandatory</li>
              <li>• Property ownership restrictions</li>
            </ul>
          </div>

          {/* Important Points */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <div className="flex items-center mb-3">
              <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                <span className="text-yellow-600 text-sm">⚡</span>
              </div>
              <h5 className="font-semibold text-gray-900">Important</h5>
            </div>
            <ul className="space-y-1 text-sm text-gray-600">
              <li>• Tax obligations apply</li>
              <li>• Banking requirements</li>
              <li>• Transportation planning</li>
            </ul>
          </div>

          {/* Helpful Points */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <div className="flex items-center mb-3">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                <span className="text-blue-600 text-sm">💡</span>
              </div>
              <h5 className="font-semibold text-gray-900">Helpful</h5>
            </div>
            <ul className="space-y-1 text-sm text-gray-600">
              <li>• Local community support</li>
              <li>• Cultural integration tips</li>
              <li>• Cost optimization strategies</li>
            </ul>
          </div>

          {/* Resources */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <div className="flex items-center mb-3">
              <div className="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center mr-3">
                <span className="text-emerald-600 text-sm">📚</span>
              </div>
              <h5 className="font-semibold text-gray-900">Resources</h5>
            </div>
            <ul className="space-y-1 text-sm text-gray-600">
              <li>• Government websites</li>
              <li>• Expat communities</li>
              <li>• Professional services</li>
            </ul>
          </div>
        </div>

        {/* Main CTA */}
        <div className="text-center mt-8">
          <ChatbotCTA
            query={{ type: 'practical-consultation', location: locationName, context: 'comprehensive-planning' }}
            className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-8 py-4 rounded-2xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
          >
            📋 Get Personalized Practical Guidance
          </ChatbotCTA>
        </div>
      </div>
    </div>
  );
};

export default PracticalInfoCards;

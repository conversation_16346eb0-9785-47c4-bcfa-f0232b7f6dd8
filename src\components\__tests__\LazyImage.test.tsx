/**
 * LazyImage Component Tests
 * 
 * Unit tests for the performance-optimized LazyImage component
 */

import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { LazyImage } from '@/components/performance/LazyImage';

// Mock IntersectionObserver
const mockIntersectionObserver = jest.fn();
mockIntersectionObserver.mockReturnValue({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
});
window.IntersectionObserver = mockIntersectionObserver;

describe('LazyImage Component', () => {
  const defaultProps = {
    src: '/test-image.jpg',
    alt: 'Test image',
    width: 800,
    height: 600,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockIntersectionObserver.mockClear();
  });

  describe('Rendering', () => {
    it('renders placeholder initially', () => {
      render(<LazyImage {...defaultProps} />);
      
      // Should show placeholder
      const placeholder = screen.getByRole('img', { hidden: true });
      expect(placeholder).toBeInTheDocument();
      
      // Should not show actual image yet
      expect(screen.queryByAltText('Test image')).not.toBeInTheDocument();
    });

    it('renders with priority loading when priority is true', () => {
      render(<LazyImage {...defaultProps} priority={true} />);
      
      // Should show actual image immediately when priority is true
      const image = screen.getByAltText('Test image');
      expect(image).toBeInTheDocument();
      expect(image).toHaveAttribute('loading', 'eager');
    });

    it('applies custom className', () => {
      render(<LazyImage {...defaultProps} className="custom-class" />);
      
      const container = screen.getByTestId('lazy-image-container');
      expect(container).toHaveClass('custom-class');
    });
  });

  describe('Lazy Loading', () => {
    it('sets up IntersectionObserver for lazy loading', () => {
      render(<LazyImage {...defaultProps} />);
      
      expect(mockIntersectionObserver).toHaveBeenCalledWith(
        expect.any(Function),
        expect.objectContaining({
          rootMargin: '50px',
          threshold: 0.1,
        })
      );
    });

    it('loads image when intersecting', async () => {
      const mockObserve = jest.fn();
      const mockDisconnect = jest.fn();
      
      mockIntersectionObserver.mockImplementation((callback) => ({
        observe: mockObserve,
        unobserve: jest.fn(),
        disconnect: mockDisconnect,
      }));

      render(<LazyImage {...defaultProps} />);

      // Simulate intersection
      const callback = mockIntersectionObserver.mock.calls[0][0];
      callback([{ isIntersecting: true }]);

      await waitFor(() => {
        const image = screen.getByAltText('Test image');
        expect(image).toBeInTheDocument();
        expect(image).toHaveAttribute('loading', 'lazy');
      });

      expect(mockDisconnect).toHaveBeenCalled();
    });

    it('does not set up observer when priority is true', () => {
      render(<LazyImage {...defaultProps} priority={true} />);
      
      expect(mockIntersectionObserver).not.toHaveBeenCalled();
    });
  });

  describe('Image Loading States', () => {
    it('shows loading state initially', () => {
      render(<LazyImage {...defaultProps} />);
      
      const placeholder = screen.getByTestId('image-placeholder');
      expect(placeholder).toBeInTheDocument();
      expect(placeholder).toHaveClass('animate-pulse');
    });

    it('handles image load event', async () => {
      const onLoad = jest.fn();
      render(<LazyImage {...defaultProps} priority={true} onLoad={onLoad} />);
      
      const image = screen.getByAltText('Test image');
      fireEvent.load(image);
      
      await waitFor(() => {
        expect(onLoad).toHaveBeenCalled();
        expect(image).toHaveClass('opacity-100');
      });
    });

    it('handles image error event', async () => {
      const onError = jest.fn();
      render(<LazyImage {...defaultProps} priority={true} onError={onError} />);
      
      const image = screen.getByAltText('Test image');
      fireEvent.error(image);
      
      await waitFor(() => {
        expect(onError).toHaveBeenCalled();
        expect(screen.getByText('Failed to load image')).toBeInTheDocument();
      });
    });

    it('shows error fallback on image error', async () => {
      render(<LazyImage {...defaultProps} priority={true} />);
      
      const image = screen.getByAltText('Test image');
      fireEvent.error(image);
      
      await waitFor(() => {
        expect(screen.getByText('Failed to load image')).toBeInTheDocument();
        expect(screen.getByRole('img', { name: /error/i })).toBeInTheDocument();
      });
    });
  });

  describe('Image Optimization', () => {
    it('generates srcSet for responsive images', async () => {
      render(<LazyImage {...defaultProps} priority={true} />);
      
      const image = screen.getByAltText('Test image');
      expect(image).toHaveAttribute('srcset');
      
      const srcSet = image.getAttribute('srcset');
      expect(srcSet).toContain('0.5x');
      expect(srcSet).toContain('1x');
      expect(srcSet).toContain('1.5x');
      expect(srcSet).toContain('2x');
    });

    it('applies sizes attribute when provided', () => {
      const sizes = '(max-width: 768px) 100vw, 50vw';
      render(<LazyImage {...defaultProps} priority={true} sizes={sizes} />);
      
      const image = screen.getByAltText('Test image');
      expect(image).toHaveAttribute('sizes', sizes);
    });

    it('optimizes image URL with quality parameter', async () => {
      render(<LazyImage {...defaultProps} priority={true} quality={85} />);
      
      const image = screen.getByAltText('Test image');
      const src = image.getAttribute('src');
      expect(src).toContain('q=85');
    });

    it('adds WebP format parameter when supported', async () => {
      // Mock WebP support
      HTMLCanvasElement.prototype.toDataURL = jest.fn(() => 'data:image/webp;base64,test');
      
      render(<LazyImage {...defaultProps} priority={true} />);
      
      await waitFor(() => {
        const image = screen.getByAltText('Test image');
        const src = image.getAttribute('src');
        expect(src).toContain('f=webp');
      });
    });
  });

  describe('Placeholder Variants', () => {
    it('shows blur placeholder when blurDataURL is provided', () => {
      render(
        <LazyImage
          {...defaultProps}
          placeholder="blur"
          blurDataURL="data:image/jpeg;base64,test"
        />
      );
      
      const blurImage = screen.getByRole('img', { hidden: true });
      expect(blurImage).toHaveAttribute('src', 'data:image/jpeg;base64,test');
      expect(blurImage).toHaveClass('filter', 'blur-sm');
    });

    it('shows empty placeholder by default', () => {
      render(<LazyImage {...defaultProps} />);
      
      const placeholder = screen.getByTestId('image-placeholder');
      expect(placeholder).toBeInTheDocument();
      expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper alt text', () => {
      render(<LazyImage {...defaultProps} priority={true} />);
      
      const image = screen.getByAltText('Test image');
      expect(image).toBeInTheDocument();
    });

    it('has proper aspect ratio', () => {
      render(<LazyImage {...defaultProps} priority={true} />);
      
      const image = screen.getByAltText('Test image');
      expect(image).toHaveStyle({
        aspectRatio: '800/600',
      });
    });

    it('has proper decoding attribute', () => {
      render(<LazyImage {...defaultProps} priority={true} />);
      
      const image = screen.getByAltText('Test image');
      expect(image).toHaveAttribute('decoding', 'async');
    });
  });

  describe('Performance', () => {
    it('preloads critical images when priority is true', () => {
      render(<LazyImage {...defaultProps} priority={true} />);
      
      // Check if preload link is added to head
      const preloadLink = document.querySelector('link[rel="preload"][as="image"]');
      expect(preloadLink).toBeInTheDocument();
    });

    it('tracks image performance metrics', async () => {
      const mockGtag = jest.fn();
      window.gtag = mockGtag;
      
      render(<LazyImage {...defaultProps} priority={true} />);
      
      const image = screen.getByAltText('Test image');
      fireEvent.load(image);
      
      await waitFor(() => {
        expect(mockGtag).toHaveBeenCalledWith('event', 'image_load_time', {
          event_category: 'performance',
          event_label: '/test-image.jpg',
          value: expect.any(Number),
          custom_map: {
            metric_name: 'image_load_time',
            metric_value: expect.any(Number),
          },
        });
      });
    });

    it('cleans up observer on unmount', () => {
      const mockDisconnect = jest.fn();
      mockIntersectionObserver.mockReturnValue({
        observe: jest.fn(),
        unobserve: jest.fn(),
        disconnect: mockDisconnect,
      });

      const { unmount } = render(<LazyImage {...defaultProps} />);
      unmount();

      expect(mockDisconnect).toHaveBeenCalled();
    });
  });
});

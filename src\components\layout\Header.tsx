/**
 * Enhanced Floating Header Component
 *
 * Modern floating navigation header with scroll-responsive behavior,
 * semi-transparent background, rounded corners, and prominent CTA.
 * Includes mobile hamburger menu with slide-in panel.
 */

'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { useHeaderScrollBehavior } from '@/hooks/useScrollBehavior';
import { ChatbotCTA } from '@/components/ui/ChatbotCTA';
import { MobileMenu } from '@/components/layout/MobileMenu';
import { useDynamicNavigation, STATIC_NAVIGATION } from '@/components/layout/DynamicNavigation';
import { useIsClient, ClientOnly } from '@/lib/hydration-safe';

export interface HeaderProps {
  /** Custom className */
  className?: string;
  /** Whether header should be sticky */
  sticky?: boolean;
  /** Whether to show background on scroll */
  showBackgroundOnScroll?: boolean;
}

// Static fallback navigation items (kept for reference and fallback)
const FALLBACK_NAVIGATION = [
  {
    label: 'Home',
    href: '/',
  },
  {
    label: 'Locations',
    href: '/locations',
    children: [
      { label: 'All Locations', href: '/locations' },
      { label: 'Canggu', href: '/locations/canggu' },
      { label: 'Ubud', href: '/locations/ubud' },
      { label: 'Seminyak', href: '/locations/seminyak' },
      { label: 'Sanur', href: '/locations/sanur' },
      { label: 'Jimbaran', href: '/locations/jimbaran' },
    ],
  },
  {
    label: 'Property Types',
    href: '/property-types',
    children: [
      { label: 'All Property Types', href: '/property-types' },
      { label: 'Villas', href: '/property-types/villa' },
      { label: 'Apartments', href: '/property-types/apartment' },
      { label: 'Guesthouses', href: '/property-types/guesthouse' },
      { label: 'Land', href: '/property-types/land' },
    ],
  },
  {
    label: 'Services',
    href: '/services',
  },
  {
    label: 'About',
    href: '/about',
  },
  {
    label: 'Contact',
    href: '/contact',
  },
];

export const Header: React.FC<HeaderProps> = ({
  className,
  sticky = true,
  showBackgroundOnScroll = true,
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const pathname = usePathname();

  // Dynamic navigation hook
  const { navigationItems, isLoading: navLoading, DynamicNavigationProvider } = useDynamicNavigation(true);

  // Enhanced scroll behavior
  const {
    isScrolled,
    scrollDirection,
    headerOpacity,
    headerScale,
    headerBlur,
    headerShadow,
    mounted,
  } = useHeaderScrollBehavior();

  // Use hydration-safe client detection
  const isClient = useIsClient();

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
    setActiveDropdown(null);
  }, [pathname]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('[data-dropdown]')) {
        setActiveDropdown(null);
      }
    };

    if (activeDropdown) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [activeDropdown]);

  // Check if link is active
  const isActiveLink = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  // Handle dropdown toggle
  const handleDropdownToggle = (label: string) => {
    setActiveDropdown(activeDropdown === label ? null : label);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (activeDropdown && !(event.target as Element).closest('.dropdown-container')) {
        setActiveDropdown(null);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [activeDropdown]);

  return (
    <>
      {/* Dynamic Navigation Provider - temporarily disabled to stop infinite loop */}
      {/* <ClientOnly fallback={null}>
        <DynamicNavigationProvider />
      </ClientOnly> */}

      <header
        className={cn(
          'fixed top-4 left-4 right-4 z-50 transition-all duration-300 ease-out',
          'transform-gpu will-change-transform container-padding',
          // Always use consistent base styling to prevent hydration mismatch
          'bg-white/85 backdrop-blur-md shadow-lg border border-white/20 rounded-2xl',
          className
        )}
        // Remove dynamic styles completely to prevent hydration mismatch
        style={{
          transform: 'scale(1)', // Always use scale(1) for consistency
        }}
      >
      <div className="container mx-auto px-6 sm:px-8 lg:px-12">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo - Bali Property Scout Branding */}
          <Link
            href="/"
            className="flex items-center space-x-3 font-bold text-xl lg:text-2xl transition-colors hover:opacity-90"
            style={{ color: 'var(--brand-primary)' }}
          >
            <div
              className="w-8 h-8 lg:w-10 lg:h-10 rounded-lg flex items-center justify-center shadow-sm"
              style={{ backgroundColor: 'var(--brand-primary)' }}
            >
              <span
                className="font-bold text-sm lg:text-base"
                style={{ color: 'var(--neutral-white)' }}
              >
                BPS
              </span>
            </div>
            <span className="hidden sm:block">Bali Property Scout</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {navigationItems.map((item) => (
              <div key={item.label} className="relative group dropdown-container">
                {item.children ? (
                  // Dropdown menu
                  <div className="relative" data-dropdown>
                    <button
                      className={cn(
                        'flex items-center space-x-1 px-3 py-2 text-sm font-medium transition-colors',
                        isActiveLink(item.href)
                          ? 'text-brand-primary'
                          : 'text-neutral-700 hover:text-brand-primary'
                      )}
                      onClick={() => handleDropdownToggle(item.label)}
                      aria-expanded={activeDropdown === item.label}
                      aria-haspopup="true"
                    >
                      <span>{item.label}</span>
                      <svg
                        className={cn(
                          'w-4 h-4 transition-transform duration-200',
                          activeDropdown === item.label ? 'rotate-180' : ''
                        )}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </button>

                    {/* Dropdown content */}
                    <div
                      className={cn(
                        'absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 transition-all duration-200',
                        activeDropdown === item.label
                          ? 'opacity-100 visible translate-y-0'
                          : 'opacity-0 invisible -translate-y-2 pointer-events-none'
                      )}
                      role="menu"
                      aria-label={`${item.label} submenu`}
                    >
                      {item.children.map((child) => (
                        <Link
                          key={child.href}
                          href={child.href}
                          className={cn(
                            'block px-4 py-2 text-sm transition-colors hover:bg-gray-50',
                            isActiveLink(child.href)
                              ? 'text-brand-primary bg-brand-primary-bg'
                              : 'text-neutral-700 hover:text-brand-primary'
                          )}
                          onClick={() => setActiveDropdown(null)}
                        >
                          {child.label}
                        </Link>
                      ))}
                    </div>
                  </div>
                ) : (
                  // Regular link
                  <Link
                    href={item.href}
                    className={cn(
                      'px-3 py-2 text-sm font-medium transition-colors',
                      isActiveLink(item.href)
                        ? 'text-brand-primary'
                        : 'text-neutral-700 hover:text-brand-primary'
                    )}
                  >
                    {item.label}
                  </Link>
                )}
              </div>
            ))}
          </nav>

          {/* Prominent CTA Button - Deep Green Accent */}
          <div className="hidden lg:flex items-center space-x-4">
            <ChatbotCTA
              query={{ type: 'general' }}
              variant="primary"
              size="md"
              className="btn-primary font-semibold text-white bg-brand-primary hover:bg-brand-primary-hover border-brand-primary hover:border-brand-primary-hover shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
              style={{
                backgroundColor: 'var(--brand-primary)',
                borderColor: 'var(--brand-primary)',
                color: 'var(--neutral-white)'
              }}
            >
              Find My Property
            </ChatbotCTA>
          </div>

          {/* Mobile menu button */}
          <button
            className="lg:hidden p-2 text-neutral-700 hover:text-primary transition-colors"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label="Toggle mobile menu"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              {isMobileMenuOpen ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              )}
            </svg>
          </button>
        </div>

      </div>
      </header>

      {/* Enhanced Mobile Menu */}
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        navigationItems={navigationItems}
        activeDropdown={activeDropdown}
        onDropdownToggle={handleDropdownToggle}
      />
    </>
  );
};

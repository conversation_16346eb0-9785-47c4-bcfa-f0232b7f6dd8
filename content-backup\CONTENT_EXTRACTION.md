# CONTENT BACKUP - BALI REAL ESTATE WEBSITE

## OVERZICHT
Deze backup bevat alle uitgebreide content die we hebben ontwikkeld voor de static versie.
Deze content moet worden gemigreerd naar Payload CMS collections.

## CONTENT LOCATIES

### 1. LOCATION DATA
**Bestand:** `src/app/[location]/[property-type]/[transaction-type]/page.tsx`
**Sectie:** `locationData` object (lijn ~35-140)

**Bevat:**
- Canggu: name, description, lifestyle, priceRanges, amenities
- Ubud: name, description, lifestyle, priceRanges, amenities  
- Seminyak: name, description, lifestyle, priceRanges, amenities

### 2. LIFESTYLE INFORMATION
**Bestand:** `src/components/location/LifestyleInformation.tsx`
**Sectie:** `lifestyleContent` object (lijn ~10-171)

**Bevat per locatie:**
- Overview (400+ woorden)
- Dining (4 categorieën met prijzen)
- Coworking/Wellness (4 opties met features)
- Activities (6+ activiteiten)
- Shopping (4 categorieën)
- Healthcare (3+ faciliteiten)
- Entertainment (5+ opties)

### 3. EXPAT COMMUNITY DATA
**Bestand:** `src/components/location/ExpatCommunity.tsx`
**Sectie:** `expatCommunityData` object (lijn ~10-120)

**Bevat per locatie:**
- Demographics & statistics
- Popular hangouts (5+ locaties)
- Online communities (4+ platforms)
- Events & networking

### 4. TRANSPORTATION DATA
**Bestand:** `src/components/location/Transportation.tsx`
**Sectie:** `transportationData` object (lijn ~10-100)

**Bevat per locatie:**
- Airport access (tijd, kosten, opties)
- Local transport (scooter, taxi, etc.)
- Walkability score & details
- Cycling infrastructure

### 5. REGULATORY INFORMATION
**Bestand:** `src/components/location/RegulatoryInformation.tsx`
**Sectie:** `regulatoryData` object (lijn ~10-80)

**Bevat per locatie:**
- Rental regulations
- Purchase regulations (freehold/leasehold)
- Visa requirements
- Tax information

### 6. FAQ DATA
**Bestand:** `src/components/location/LocationFAQ.tsx`
**Sectie:** `faqData` object (lijn ~10-150)

**Bevat per locatie/transactie:**
- 10 specifieke FAQ's per combinatie
- Gedetailleerde antwoorden
- Praktische informatie

## MIGRATIE PLAN

### FASE 1: PAYLOAD COLLECTIONS SETUP
1. Locations collection
2. PropertyTypes collection  
3. LifestyleInfo collection
4. ExpatCommunity collection
5. Transportation collection
6. RegulatoryInfo collection
7. FAQ collection

### FASE 2: CONTENT IMPORT
1. Extract data uit backup bestanden
2. Transform naar CMS format
3. Import via Payload admin
4. Validate data integrity

### FASE 3: FRONTEND INTEGRATION
1. Update components voor CMS data
2. API calls implementeren
3. Static generation met CMS
4. Testing & validation

## BELANGRIJKE NOTES
- Alle content is SEO-geoptimaliseerd
- Prijzen zijn realistisch en up-to-date
- Lifestyle info is uitgebreid (400+ woorden per locatie)
- FAQ's zijn location/transaction specifiek
- Content is production-ready kwaliteit

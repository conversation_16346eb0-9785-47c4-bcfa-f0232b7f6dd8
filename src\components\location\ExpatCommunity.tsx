interface ExpatCommunityProps {
  location: string
  locationInfo: { name: string; community: string }
}

// Detailed expat community content based on PRD requirements
const expatCommunityContent = {
  'canggu': {
    overview: `Canggu hosts one of Bali's largest and most vibrant expat communities, with an estimated 5,000+ international residents calling this surf town home. The community is predominantly composed of digital nomads, remote workers, entrepreneurs, and creative professionals, creating a dynamic ecosystem of innovation and collaboration.

The demographic is diverse but skews younger, with most residents aged 25-40, representing over 50 nationalities. Australians, Americans, Europeans, and increasingly, professionals from Asia make up the core community. This international mix creates a unique cultural blend where business networking happens over morning surf sessions and co-working spaces buzz with multilingual conversations.`,

    demographics: [
      { group: 'Digital Nomads & Remote Workers', percentage: '40%', description: 'Freelancers, consultants, online entrepreneurs' },
      { group: 'Startup Entrepreneurs', percentage: '25%', description: 'Tech founders, e-commerce, digital agencies' },
      { group: 'Creative Professionals', percentage: '20%', description: 'Photographers, designers, content creators' },
      { group: 'Long-term Residents', percentage: '15%', description: 'Business owners, retirees, families' }
    ],

    hangouts: [
      { name: 'Dojo Bali', type: 'Coworking Space', description: 'Main networking hub, daily events, professional meetups' },
      { name: 'Old Man\'s Bar', type: 'Beach Bar', description: 'Sunset drinks, live music, casual networking' },
      { name: 'The Lawn', type: 'Beach Club', description: 'Weekend brunches, business lunches, social events' },
      { name: 'Crate Cafe', type: 'Cafe', description: 'Morning coffee meetings, laptop-friendly workspace' },
      { name: 'Echo Beach', type: 'Beach', description: 'Morning surf sessions, beach volleyball, fitness groups' }
    ],

    onlineCommunities: [
      { name: 'Canggu Community', platform: 'Facebook', members: '25,000+', focus: 'General community, events, housing' },
      { name: 'Digital Nomads Canggu', platform: 'Facebook', members: '15,000+', focus: 'Remote work, networking, jobs' },
      { name: 'Canggu Entrepreneurs', platform: 'WhatsApp/Telegram', members: '500+', focus: 'Business networking, partnerships' },
      { name: 'Canggu Housing', platform: 'Facebook', members: '20,000+', focus: 'Rentals, roommates, property advice' }
    ],

    events: [
      'Weekly networking events at coworking spaces',
      'Monthly entrepreneur meetups and pitch nights',
      'Beach cleanup and community service projects',
      'Surf competitions and beach sports tournaments',
      'Cultural exchange events and language practice',
      'Business workshops and skill-sharing sessions'
    ],

    integrationTips: [
      'Join coworking spaces for instant community access',
      'Attend weekly networking events and meetups',
      'Participate in beach activities and sports groups',
      'Learn basic Bahasa Indonesia for local interactions',
      'Respect local customs and participate in ceremonies',
      'Support local businesses and build relationships'
    ]
  },

  'ubud': {
    overview: `Ubud's expat community is distinctly different from Bali's beach destinations, attracting individuals focused on wellness, spirituality, personal growth, and cultural immersion. With approximately 3,000+ international residents, the community is characterized by yoga teachers, wellness practitioners, artists, writers, and those seeking a deeper connection with Balinese culture.

The demographic tends to be slightly older and more established, with many residents aged 30-50 who have chosen Ubud for its transformative environment. The community includes a significant number of yoga instructors, meditation teachers, healers, and creative professionals who have built sustainable businesses around wellness and personal development.`,

    demographics: [
      { group: 'Wellness Professionals', percentage: '35%', description: 'Yoga teachers, healers, therapists, retreat leaders' },
      { group: 'Artists & Creatives', percentage: '25%', description: 'Writers, painters, musicians, craftspeople' },
      { group: 'Spiritual Seekers', percentage: '20%', description: 'Long-term students, retreat participants' },
      { group: 'Sustainable Business Owners', percentage: '20%', description: 'Eco-tourism, organic farming, wellness centers' }
    ],

    hangouts: [
      { name: 'Yoga Barn', type: 'Yoga Studio', description: 'Community hub, teacher training, workshops' },
      { name: 'Alchemy', type: 'Raw Food Cafe', description: 'Health-conscious dining, community board' },
      { name: 'Intuitive Flow', type: 'Wellness Center', description: 'Healing sessions, practitioner networking' },
      { name: 'Ubud Writers Festival', type: 'Cultural Event', description: 'Annual literary gathering, year-round community' },
      { name: 'Campuhan Ridge', type: 'Nature Walk', description: 'Morning walks, meditation groups, nature connection' }
    ],

    onlineCommunities: [
      { name: 'Ubud Community', platform: 'Facebook', members: '18,000+', focus: 'General community, events, wellness' },
      { name: 'Ubud Yoga Teachers', platform: 'Facebook', members: '3,000+', focus: 'Professional development, opportunities' },
      { name: 'Ubud Artists & Creatives', platform: 'Facebook', members: '2,500+', focus: 'Art shows, collaborations, studios' },
      { name: 'Conscious Ubud', platform: 'WhatsApp', members: '800+', focus: 'Spiritual growth, ceremonies, healing' }
    ],

    events: [
      'Daily yoga classes and meditation sessions',
      'Full moon ceremonies and spiritual gatherings',
      'Art gallery openings and creative workshops',
      'Organic market days and sustainable living talks',
      'Cultural immersion programs and Balinese ceremonies',
      'Wellness retreats and healing workshops'
    ],

    integrationTips: [
      'Start with yoga classes to meet like-minded people',
      'Attend cultural events and traditional ceremonies',
      'Participate in organic farming and sustainability projects',
      'Learn about Balinese Hindu philosophy and practices',
      'Support local artisans and traditional crafts',
      'Engage with the wellness community through workshops'
    ]
  },

  'seminyak': {
    overview: `Seminyak's expat community represents the more affluent and established segment of Bali's international residents, with approximately 2,000+ professionals, business owners, and luxury lifestyle enthusiasts. The community is characterized by successful entrepreneurs, corporate executives, property investors, and those who appreciate the finer aspects of tropical living.

The demographic is generally older and more financially established, with many residents aged 35-55 who have built successful businesses or careers that allow them to enjoy Seminyak's luxury lifestyle. This community values quality over quantity, preferring exclusive venues and premium experiences.`,

    demographics: [
      { group: 'Business Owners & Investors', percentage: '40%', description: 'Restaurant owners, property investors, import/export' },
      { group: 'Corporate Executives', percentage: '25%', description: 'Regional managers, consultants, finance professionals' },
      { group: 'Luxury Lifestyle Enthusiasts', percentage: '20%', description: 'Retirees, trust fund recipients, luxury travelers' },
      { group: 'Creative Industry Professionals', percentage: '15%', description: 'Fashion designers, photographers, marketing executives' }
    ],

    hangouts: [
      { name: 'Ku De Ta', type: 'Beach Club', description: 'Luxury networking, business dinners, exclusive events' },
      { name: 'Potato Head Beach Club', type: 'Beach Club', description: 'High-end socializing, international crowd' },
      { name: 'Merah Putih Restaurant', type: 'Fine Dining', description: 'Business lunches, client entertainment' },
      { name: 'W Bali Lounge', type: 'Hotel Bar', description: 'Cocktail networking, luxury hospitality' },
      { name: 'Seminyak Village', type: 'Shopping Complex', description: 'Luxury shopping, business meetings, dining' }
    ],

    onlineCommunities: [
      { name: 'Seminyak Expats', platform: 'Facebook', members: '8,000+', focus: 'Luxury lifestyle, dining, events' },
      { name: 'Bali Business Network', platform: 'LinkedIn', members: '5,000+', focus: 'Professional networking, opportunities' },
      { name: 'Seminyak Property Investors', platform: 'WhatsApp', members: '300+', focus: 'Real estate, investment advice' },
      { name: 'Luxury Bali Living', platform: 'Facebook', members: '12,000+', focus: 'High-end lifestyle, recommendations' }
    ],

    events: [
      'Exclusive business networking dinners',
      'Luxury brand launches and fashion shows',
      'Private beach club events and parties',
      'Wine tastings and gourmet food festivals',
      'Art gallery openings and cultural events',
      'Charity galas and fundraising events'
    ],

    integrationTips: [
      'Join exclusive beach clubs for networking opportunities',
      'Attend high-end business events and dinners',
      'Participate in luxury lifestyle activities and clubs',
      'Build relationships through fine dining experiences',
      'Engage with the arts and culture scene',
      'Consider property investment for long-term integration'
    ]
  }
}

const ExpatCommunity = ({ location, locationInfo }: ExpatCommunityProps) => {
  const content = expatCommunityContent[location as keyof typeof expatCommunityContent]

  if (!content) {
    return (
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">
            Expat Community in {locationInfo.name}
          </h2>
          <p className="text-gray-600">{locationInfo.community}</p>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 bg-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            Expat Community in {locationInfo.name}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Connect with like-minded international residents and build your network
          </p>
        </div>

        {/* Overview */}
        <div className="prose prose-lg max-w-none mb-16">
          <div className="text-gray-700 leading-relaxed space-y-6">
            {content.overview.split('\n\n').map((paragraph, index) => (
              <p key={index}>{paragraph}</p>
            ))}
          </div>
        </div>

        {/* Demographics */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">Community Demographics</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {content.demographics.map((group, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-6">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-lg font-semibold text-gray-900">{group.group}</h4>
                  <span className="text-2xl font-bold text-emerald-600">{group.percentage}</span>
                </div>
                <p className="text-gray-600">{group.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Popular Hangouts */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">Popular Expat Hangouts</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {content.hangouts.map((place, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{place.name}</h4>
                <div className="text-sm text-emerald-600 font-medium mb-3">{place.type}</div>
                <p className="text-gray-600 text-sm">{place.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Online Communities */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">Online Communities & Groups</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {content.onlineCommunities.map((community, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-6">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-lg font-semibold text-gray-900">{community.name}</h4>
                  <span className="text-sm text-gray-500">{community.platform}</span>
                </div>
                <div className="text-emerald-600 font-semibold mb-2">{community.members}</div>
                <p className="text-gray-600 text-sm">{community.focus}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Regular Events */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">Regular Events & Meetups</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {content.events.map((event, index) => (
              <div key={index} className="flex items-start gap-3 bg-gray-50 rounded-lg p-4">
                <div className="w-6 h-6 bg-emerald-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <svg className="w-3 h-3 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="text-gray-700 text-sm">{event}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Integration Tips */}
        <div className="bg-emerald-50 rounded-2xl p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">Tips for Newcomers</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {content.integrationTips.map((tip, index) => (
              <div key={index} className="flex items-start gap-3">
                <div className="w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="text-gray-700">{tip}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default ExpatCommunity
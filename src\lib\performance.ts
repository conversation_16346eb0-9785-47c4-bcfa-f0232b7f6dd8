/**
 * Performance Monitoring Utilities
 * 
 * Utilities for monitoring Core Web Vitals and performance metrics
 */

// Core Web Vitals thresholds
export const PERFORMANCE_THRESHOLDS = {
  LCP: 2500, // Largest Contentful Paint (ms)
  FID: 100,  // First Input Delay (ms)
  CLS: 0.1,  // Cumulative Layout Shift
  FCP: 1800, // First Contentful Paint (ms)
  TTFB: 800, // Time to First Byte (ms)
} as const;

export interface PerformanceMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  timestamp: number;
}

export interface WebVitalsMetric {
  id: string;
  name: 'CLS' | 'FID' | 'FCP' | 'LCP' | 'TTFB';
  value: number;
  delta: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  entries: PerformanceEntry[];
}

/**
 * Get performance rating based on metric value and thresholds
 */
export function getPerformanceRating(
  metricName: keyof typeof PERFORMANCE_THRESHOLDS,
  value: number
): 'good' | 'needs-improvement' | 'poor' {
  const threshold = PERFORMANCE_THRESHOLDS[metricName];
  
  switch (metricName) {
    case 'CLS':
      if (value <= 0.1) return 'good';
      if (value <= 0.25) return 'needs-improvement';
      return 'poor';
    
    case 'FID':
      if (value <= 100) return 'good';
      if (value <= 300) return 'needs-improvement';
      return 'poor';
    
    case 'LCP':
      if (value <= 2500) return 'good';
      if (value <= 4000) return 'needs-improvement';
      return 'poor';
    
    case 'FCP':
      if (value <= 1800) return 'good';
      if (value <= 3000) return 'needs-improvement';
      return 'poor';
    
    case 'TTFB':
      if (value <= 800) return 'good';
      if (value <= 1800) return 'needs-improvement';
      return 'poor';
    
    default:
      return 'good';
  }
}

/**
 * Log performance metric to console (development) or analytics (production)
 */
export function logPerformanceMetric(metric: PerformanceMetric): void {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Performance] ${metric.name}: ${metric.value}ms (${metric.rating})`);
  } else {
    // In production, send to analytics service
    // Example: Google Analytics, DataDog, etc.
    if (typeof window !== 'undefined' && 'gtag' in window) {
      // @ts-ignore
      window.gtag('event', 'web_vitals', {
        event_category: 'Performance',
        event_label: metric.name,
        value: Math.round(metric.value),
        custom_map: {
          metric_rating: metric.rating,
        },
      });
    }
  }
}

/**
 * Web Vitals callback function
 */
export function onWebVitals(metric: WebVitalsMetric): void {
  const performanceMetric: PerformanceMetric = {
    name: metric.name,
    value: metric.value,
    rating: metric.rating,
    timestamp: Date.now(),
  };
  
  logPerformanceMetric(performanceMetric);
}

/**
 * Measure component render time
 */
export function measureComponentRender<T extends any[]>(
  componentName: string,
  renderFn: (...args: T) => any
) {
  return (...args: T) => {
    const startTime = performance.now();
    const result = renderFn(...args);
    const endTime = performance.now();
    
    const renderTime = endTime - startTime;
    
    if (renderTime > 16) { // More than one frame (60fps)
      console.warn(`[Performance] ${componentName} render took ${renderTime.toFixed(2)}ms`);
    }
    
    return result;
  };
}

/**
 * Performance observer for monitoring long tasks
 */
export function observeLongTasks(): void {
  if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
    return;
  }

  try {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.duration > 50) { // Tasks longer than 50ms
          console.warn(`[Performance] Long task detected: ${entry.duration.toFixed(2)}ms`);
          
          logPerformanceMetric({
            name: 'long-task',
            value: entry.duration,
            rating: entry.duration > 100 ? 'poor' : 'needs-improvement',
            timestamp: Date.now(),
          });
        }
      }
    });

    observer.observe({ entryTypes: ['longtask'] });
  } catch (error) {
    console.warn('[Performance] Long task observer not supported');
  }
}

/**
 * Monitor largest contentful paint
 */
export function observeLCP(): void {
  if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
    return;
  }

  try {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      
      if (lastEntry) {
        const metric: PerformanceMetric = {
          name: 'LCP',
          value: lastEntry.startTime,
          rating: getPerformanceRating('LCP', lastEntry.startTime),
          timestamp: Date.now(),
        };
        
        logPerformanceMetric(metric);
      }
    });

    observer.observe({ entryTypes: ['largest-contentful-paint'] });
  } catch (error) {
    console.warn('[Performance] LCP observer not supported');
  }
}

/**
 * Monitor first contentful paint
 */
export function observeFCP(): void {
  if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
    return;
  }

  try {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name === 'first-contentful-paint') {
          const metric: PerformanceMetric = {
            name: 'FCP',
            value: entry.startTime,
            rating: getPerformanceRating('FCP', entry.startTime),
            timestamp: Date.now(),
          };
          
          logPerformanceMetric(metric);
        }
      }
    });

    observer.observe({ entryTypes: ['paint'] });
  } catch (error) {
    console.warn('[Performance] FCP observer not supported');
  }
}

/**
 * Initialize all performance monitoring
 */
export function initPerformanceMonitoring(): void {
  if (typeof window === 'undefined') {
    return;
  }

  // Start observing performance metrics
  observeLongTasks();
  observeLCP();
  observeFCP();

  // Log initial page load metrics
  window.addEventListener('load', () => {
    setTimeout(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigation) {
        // Time to First Byte
        const ttfb = navigation.responseStart - navigation.requestStart;
        logPerformanceMetric({
          name: 'TTFB',
          value: ttfb,
          rating: getPerformanceRating('TTFB', ttfb),
          timestamp: Date.now(),
        });

        // DOM Content Loaded
        const dcl = navigation.domContentLoadedEventEnd - navigation.navigationStart;
        logPerformanceMetric({
          name: 'DCL',
          value: dcl,
          rating: dcl <= 1500 ? 'good' : dcl <= 3000 ? 'needs-improvement' : 'poor',
          timestamp: Date.now(),
        });

        // Load Complete
        const loadComplete = navigation.loadEventEnd - navigation.navigationStart;
        logPerformanceMetric({
          name: 'Load',
          value: loadComplete,
          rating: loadComplete <= 3000 ? 'good' : loadComplete <= 5000 ? 'needs-improvement' : 'poor',
          timestamp: Date.now(),
        });
      }
    }, 0);
  });
}

/**
 * Performance budget checker
 */
export interface PerformanceBudget {
  maxBundleSize: number; // KB
  maxImageSize: number;  // KB
  maxFontSize: number;   // KB
  maxLCP: number;        // ms
  maxFID: number;        // ms
  maxCLS: number;        // score
}

export const DEFAULT_PERFORMANCE_BUDGET: PerformanceBudget = {
  maxBundleSize: 150,  // 150KB
  maxImageSize: 500,   // 500KB
  maxFontSize: 100,    // 100KB
  maxLCP: 2500,        // 2.5s
  maxFID: 100,         // 100ms
  maxCLS: 0.1,         // 0.1 score
};

/**
 * Check if performance metrics meet budget requirements
 */
export function checkPerformanceBudget(
  metrics: Record<string, number>,
  budget: PerformanceBudget = DEFAULT_PERFORMANCE_BUDGET
): { passed: boolean; violations: string[] } {
  const violations: string[] = [];

  if (metrics.LCP > budget.maxLCP) {
    violations.push(`LCP (${metrics.LCP}ms) exceeds budget (${budget.maxLCP}ms)`);
  }

  if (metrics.FID > budget.maxFID) {
    violations.push(`FID (${metrics.FID}ms) exceeds budget (${budget.maxFID}ms)`);
  }

  if (metrics.CLS > budget.maxCLS) {
    violations.push(`CLS (${metrics.CLS}) exceeds budget (${budget.maxCLS})`);
  }

  return {
    passed: violations.length === 0,
    violations,
  };
}

/**
 * Location Hero Banner Component
 * Bali Property Scout Website
 * 
 * Full-width hero banner with high-resolution location imagery,
 * overlay text, and enhanced visual presentation for location pages.
 */

'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { ChatbotCTA } from '@/components/ui/ChatbotCTA';

export interface LocationHeroBannerProps {
  /** Location data */
  location: {
    name: string;
    slug: string;
    description: string;
    priority: string;
    priceRanges?: {
      villa?: {
        longTermRental?: string;
        forSaleFreehold?: string;
      };
      guesthouse?: {
        longTermRental?: string;
      };
    };
  };
}

export const LocationHeroBanner: React.FC<LocationHeroBannerProps> = ({ location }) => {
  const [imageLoaded, setImageLoaded] = useState(false);

  // High-quality location images
  const locationImages = {
    'canggu': 'https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?w=1920&auto=format&fit=crop&q=90',
    'ubud': 'https://images.unsplash.com/photo-1518548419970-58e3b4079ab2?w=1920&auto=format&fit=crop&q=90',
    'seminyak': 'https://images.unsplash.com/photo-1540541338287-41700207dee6?w=1920&auto=format&fit=crop&q=90',
    'sanur': 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=1920&auto=format&fit=crop&q=90',
    'jimbaran': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&auto=format&fit=crop&q=90',
    'nusa-dua': 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=1920&auto=format&fit=crop&q=90',
    'uluwatu': 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=1920&auto=format&fit=crop&q=90',
    'denpasar': 'https://images.unsplash.com/photo-1555400082-8c5cd5b3c3d1?w=1920&auto=format&fit=crop&q=90',
  };

  // Location-specific taglines
  const locationTaglines = {
    'canggu': 'Surf, Work, Live - The Digital Nomad Paradise',
    'ubud': 'Cultural Heart of Bali - Yoga, Art & Nature',
    'seminyak': 'Luxury Beach Living - Clubs, Dining & Style',
    'sanur': 'Family-Friendly Serenity - Calm Beach & Culture',
    'jimbaran': 'Sunset Views & Seafood - Luxury Resort Living',
    'nusa-dua': 'Premium Resort Area - Golf, Beaches & Luxury',
    'uluwatu': 'Clifftop Paradise - World-Class Surf & Views',
    'denpasar': 'Business Hub - Local Culture & Urban Living',
  };

  const heroImage = locationImages[location.slug as keyof typeof locationImages] || locationImages.canggu;
  const tagline = locationTaglines[location.slug as keyof typeof locationTaglines] || 'Discover Your Perfect Bali Home';

  // Priority badge styling
  const getPriorityBadge = () => {
    const priority = location.priority === 'zeer-hoog' ? 'ZEER HOOG' :
                    location.priority === 'hoog' ? 'HOOG' : 'GEMIDDELD';
    
    const badgeStyle = location.priority === 'zeer-hoog' 
      ? 'bg-gradient-to-r from-red-500 to-pink-500 text-white'
      : location.priority === 'hoog'
      ? 'bg-gradient-to-r from-orange-500 to-amber-500 text-white'
      : 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white';

    return (
      <span className={cn(
        'inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-bold shadow-lg',
        badgeStyle
      )}>
        <span className="text-lg">⭐</span>
        {priority}
      </span>
    );
  };

  return (
    <section className="relative min-h-[70vh] flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <img
          src={heroImage}
          alt={`${location.name} - Premium Properties and Rentals`}
          className={cn(
            'w-full h-full object-cover transition-all duration-1000',
            imageLoaded ? 'scale-100 opacity-100' : 'scale-105 opacity-0'
          )}
          onLoad={() => setImageLoaded(true)}
          loading="eager"
        />
        
        {/* Enhanced Gradient Overlays */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/60 via-black/40 to-black/60" />
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-black/30" />
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-64 h-64 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-80 h-80 bg-emerald-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-6xl mx-auto px-4 text-center text-white">
        <div className="max-w-4xl mx-auto">
          {/* Priority Badge */}
          <div className="mb-6">
            {getPriorityBadge()}
          </div>

          {/* Main Headline */}
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold mb-6 leading-tight">
            <span className="block">{location.name}</span>
            <span className="block text-3xl md:text-4xl lg:text-5xl font-light text-emerald-200 mt-2">
              Properties & Rentals
            </span>
          </h1>

          {/* Tagline */}
          <p className="text-xl md:text-2xl mb-8 text-emerald-100 font-light leading-relaxed">
            {tagline}
          </p>

          {/* Description */}
          <p className="text-lg md:text-xl mb-12 text-white/90 max-w-3xl mx-auto leading-relaxed">
            {location.description}
          </p>

          {/* Key Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12 max-w-2xl mx-auto">
            {location.priceRanges?.villa?.longTermRental && (
              <div className="bg-white/15 backdrop-blur-md rounded-2xl p-6 border border-white/20">
                <div className="text-2xl md:text-3xl font-bold mb-2">
                  {location.priceRanges.villa.longTermRental}
                </div>
                <div className="text-sm text-emerald-200 font-medium">Villa Rental Range</div>
              </div>
            )}
            
            <div className="bg-white/15 backdrop-blur-md rounded-2xl p-6 border border-white/20">
              <div className="text-2xl md:text-3xl font-bold mb-2">
                {location.priority === 'zeer-hoog' ? '🔥 HOT' :
                 location.priority === 'hoog' ? '⭐ HIGH' : '📈 RISING'}
              </div>
              <div className="text-sm text-emerald-200 font-medium">Market Demand</div>
            </div>
          </div>

          {/* CTA Button */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <ChatbotCTA
              query={{ type: 'location', location: location.name }}
              variant="primary"
              size="lg"
              className="bg-white text-emerald-900 hover:bg-emerald-50 border-2 border-white/50 hover:border-white shadow-2xl hover:shadow-3xl px-10 py-5 text-xl font-bold transform hover:scale-110 hover:-translate-y-2 transition-all duration-300"
            >
              🏠 View Available Properties
            </ChatbotCTA>
            
            <button
              onClick={() => {
                const overviewSection = document.getElementById('overview');
                overviewSection?.scrollIntoView({ behavior: 'smooth' });
              }}
              className="bg-emerald-600/90 backdrop-blur-md hover:bg-emerald-700/90 text-white border-2 border-emerald-400/50 hover:border-emerald-300/70 px-10 py-5 rounded-2xl font-bold text-xl transition-all duration-300 hover:scale-110 hover:-translate-y-2 shadow-xl hover:shadow-2xl"
            >
              📖 Learn More
            </button>
          </div>

          {/* Trust Indicators */}
          <div className="mt-12 flex flex-wrap justify-center items-center gap-8 text-emerald-100">
            <div className="flex items-center gap-2">
              <span className="text-2xl">🤖</span>
              <span className="text-sm font-medium">AI-Powered Search</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-2xl">🏆</span>
              <span className="text-sm font-medium">Local Expertise</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-2xl">⚡</span>
              <span className="text-sm font-medium">Instant Matching</span>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/70 animate-bounce">
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
        </svg>
      </div>
    </section>
  );
};

export default LocationHeroBanner;

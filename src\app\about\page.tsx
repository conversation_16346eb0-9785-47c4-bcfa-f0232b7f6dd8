import { Metadata } from 'next'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/Button'
import InteractiveTimeline, { defaultMilestones } from '@/components/about/InteractiveTimeline'
import AITeamSection from '@/components/about/AITeamSection'

export const metadata: Metadata = {
  title: 'About Bali Property Scout | AI-Powered Real Estate, Human-Centered Service',
  description: 'Meet the Bali Property Scout team combining cutting-edge AI technology with deep local expertise. Discover how we revolutionize property search in Bali with personalized AI matching and human insight since 2019.',
  keywords: 'Bali Property Scout, AI real estate, Bali property experts, AI property matching, real estate technology, Bali local expertise, property scout AI',
  robots: {
    index: true,
    follow: true,
  },
}

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-emerald-600 to-emerald-700 text-white py-20">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                AI-Powered Real Estate,<br />
                <span className="text-emerald-200">Human-Centered Service</span>
              </h1>
              <p className="text-xl mb-8 text-emerald-100 leading-relaxed">
                Bali Property Scout's mission: to help expats, digital nomads and investors find their perfect home or investment in Bali since 2019.
                We combine cutting-edge AI technology with deep local expertise, creating personalized property matches
                that traditional methods simply can't achieve.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button asChild size="lg">
                  <a href="https://app.bali-realestate.com/chatbot?query=general-consultation">
                    Start Your Property Journey
                  </a>
                </Button>
                <Button variant="outline" size="lg" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                  <Link href="/contact">
                    Meet Our Team
                  </Link>
                </Button>
              </div>
            </div>
            <div className="relative">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <div className="flex items-center mb-4">
                  <div className="w-3 h-3 bg-emerald-400 rounded-full mr-2"></div>
                  <div className="w-3 h-3 bg-yellow-400 rounded-full mr-2"></div>
                  <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                </div>
                <div className="text-emerald-100 font-mono text-sm">
                  <div className="mb-2">{'>'} Analyzing your preferences...</div>
                  <div className="mb-2">{'>'} Matching 2,847 properties...</div>
                  <div className="mb-2">{'>'} Found 3 perfect matches!</div>
                  <div className="text-emerald-300">{'>'} Ready to show you ✨</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-20">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              The Future of Real Estate is Here
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Born from frustration with traditional property search, we've created an AI-powered platform
              that understands your unique needs and matches you with perfect properties in paradise.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
            <div>
              <h3 className="text-3xl font-bold text-gray-900 mb-6">
                Why We Built This
              </h3>
              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <svg className="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Traditional Search is Broken</h4>
                    <p className="text-gray-600">Endless scrolling through irrelevant listings, generic recommendations, and wasted time on properties that don't match your actual needs.</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <svg className="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Information Overload</h4>
                    <p className="text-gray-600">Complex legal requirements, hidden costs, and overwhelming choices that make property decisions stressful instead of exciting.</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center mr-4 mt-1">
                    <svg className="w-4 h-4 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Bali Property Scout's AI Solution</h4>
                    <p className="text-gray-600">Intelligent matching that learns your preferences, transparent guidance through every step, and personalized recommendations that actually fit your lifestyle and investment goals.</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-emerald-50 to-blue-50 rounded-2xl p-8">
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <h4 className="text-2xl font-bold text-gray-900 mb-4">The "Aha!" Moment</h4>
              </div>

              <blockquote className="text-gray-700 italic text-center mb-6">
                "What if we could understand exactly what someone needs in a property - not just bedrooms and price,
                but lifestyle, investment goals, and dreams - then instantly match them with perfect options?"
              </blockquote>

              <div className="text-center">
                <p className="text-sm text-gray-600">— Our founding insight, 2024</p>
              </div>
            </div>
          </div>

          {/* Team Section */}
          <div className="bg-white rounded-2xl shadow-xl p-12">
            <div className="text-center mb-12">
              <h3 className="text-3xl font-bold text-gray-900 mb-4">
                Meet Your AI-Enhanced Team
              </h3>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Our unique approach combines artificial intelligence with genuine human expertise,
                local knowledge, and personalized service.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8 mb-12">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-3">AI Property Matcher</h4>
                <p className="text-gray-600 mb-4">
                  Our intelligent system analyzes thousands of properties and your unique preferences
                  to find perfect matches in seconds, not weeks.
                </p>
                <div className="text-sm text-emerald-600 font-medium">
                  ✓ 24/7 availability ✓ Instant responses ✓ Learning algorithm
                </div>
              </div>

              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-3">Local Experts</h4>
                <p className="text-gray-600 mb-4">
                  Our human team brings years of Bali experience, legal knowledge, and cultural
                  insights that no AI can replace.
                </p>
                <div className="text-sm text-blue-600 font-medium">
                  ✓ 10+ years experience ✓ Legal expertise ✓ Cultural knowledge
                </div>
              </div>

              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-3">Personal Concierge</h4>
                <p className="text-gray-600 mb-4">
                  From first contact to keys in hand, we provide personalized support for every
                  step of your property journey.
                </p>
                <div className="text-sm text-purple-600 font-medium">
                  ✓ Personal attention ✓ End-to-end support ✓ After-sale care
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-emerald-50 to-blue-50 rounded-xl p-8 text-center">
              <h4 className="text-2xl font-bold text-gray-900 mb-4">
                The Perfect Balance
              </h4>
              <p className="text-lg text-gray-600 mb-6 max-w-3xl mx-auto">
                AI handles the heavy lifting of property matching and data analysis, while our human experts
                provide the personal touch, local insights, and emotional intelligence that make your
                property journey smooth and successful.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg">
                  <Link href="https://app.balipropertyscout.com?query=how-it-works">
                    Try Our AI Assistant
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/contact">
                    Talk to Our Team
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Interactive Company Timeline */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-6">
              Our Journey
            </h3>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From local real estate service to AI-powered property platform.
              Discover the key milestones that shaped Bali Property Scout into Bali's leading property technology company.
            </p>
          </div>

          <InteractiveTimeline milestones={defaultMilestones} />
        </div>
      </section>

      {/* AI-Enhanced Team Section */}
      <AITeamSection />
    </div>
  )
}

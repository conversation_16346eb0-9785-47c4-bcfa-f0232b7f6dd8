'use client'

import { useState } from 'react'

export default function TestContactPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const testAPI = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/leads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'Test User',
          email: '<EMAIL>',
          phone: '+1234567890',
          message: 'This is a test message from the contact form',
          timeline: 'short-term',
          leadType: 'general',
          source: 'test-page',
          gdprConsent: true,
          emailOptIn: true,
        }),
      })

      const data = await response.json()
      setResult({
        status: response.status,
        data: data,
      })
    } catch (error) {
      setResult({
        status: 'error',
        error: error.message,
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchLeads = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/leads')
      const data = await response.json()
      setResult({
        status: response.status,
        data: data,
      })
    } catch (error) {
      setResult({
        status: 'error',
        error: error.message,
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Contact API Test</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Contact Form Submission</h2>
          <div className="space-x-4">
            <button
              onClick={testAPI}
              disabled={loading}
              className="bg-emerald-600 text-white px-6 py-2 rounded-md hover:bg-emerald-700 disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Test POST /api/leads'}
            </button>
            
            <button
              onClick={fetchLeads}
              disabled={loading}
              className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Loading...' : 'Test GET /api/leads'}
            </button>
          </div>
        </div>

        {result && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold mb-4">API Response</h3>
            <div className="bg-gray-100 p-4 rounded-md">
              <p className="text-sm text-gray-600 mb-2">Status: {result.status}</p>
              <pre className="text-sm overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

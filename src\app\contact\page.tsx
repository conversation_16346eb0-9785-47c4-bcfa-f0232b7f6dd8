import { Metadata } from 'next'
import Link from 'next/link'
import ContactForm from '@/components/forms/ContactForm'
import EnhancedContactForms from '@/components/contact/EnhancedContactForms'
import InteractiveOfficeMap from '@/components/contact/InteractiveOfficeMap'
import FloatingChatWidget from '@/components/contact/FloatingChatWidget'
import ContactFAQAccordions from '@/components/contact/ContactFAQAccordions'

export const metadata: Metadata = {
  title: 'Contact Bali Property Scout | AI-Powered Real Estate Experts | Free Consultation',
  description: 'Contact Bali Property Scout for AI-powered property matching and expert advice on rentals, purchases, and investments. Free consultation with local specialists.',
  keywords: ['contact bali property scout', 'AI property consultation bali', 'real estate advice bali', 'bali property expert', 'AI property matching'],
  openGraph: {
    title: 'Contact Bali Property Scout - AI-Powered Real Estate Experts',
    description: 'Get AI-powered property matching and professional advice on Bali property rentals, purchases, and investments. Free consultation available.',
    type: 'website',
  },
}

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-emerald-600 to-teal-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Contact Bali Property Scout
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-emerald-100 max-w-3xl mx-auto">
              Get AI-powered property matching combined with local expertise. Whether you're looking to rent, buy, or invest in Bali property, our team is here to guide you.
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <div className="flex items-center">
                <span className="w-2 h-2 bg-emerald-300 rounded-full mr-2"></span>
                AI-Powered Matching
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-emerald-300 rounded-full mr-2"></span>
                Local Market Expertise
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 bg-emerald-300 rounded-full mr-2"></span>
                24/7 AI Support
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Options */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-3 gap-8">
            
            {/* Enhanced Contact Forms */}
            <div className="lg:col-span-2">
              <EnhancedContactForms />
            </div>

            {/* Contact Information & Quick Form */}
            <div className="space-y-8">
              
              {/* Contact Information */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-6">Get In Touch</h3>
                
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 text-emerald-600 mt-1">
                      <svg fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">Email</p>
                      <p className="text-sm text-gray-600"><EMAIL></p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 text-emerald-600 mt-1">
                      <svg fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">Phone</p>
                      <p className="text-sm text-gray-600">+62 ************</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 text-emerald-600 mt-1">
                      <svg fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">Office</p>
                      <p className="text-sm text-gray-600">Canggu, Bali, Indonesia</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 text-emerald-600 mt-1">
                      <svg fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">Hours</p>
                      <p className="text-sm text-gray-600">Mon-Fri: 9AM-6PM WITA</p>
                      <p className="text-sm text-gray-600">Sat: 9AM-2PM WITA</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Contact Section */}
              <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-200">
                <h3 className="text-xl font-bold text-gray-900 mb-2">Quick Inquiry</h3>
                <p className="text-gray-600 text-sm mb-6">Need a fast response? Use this form</p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <a
                    href="https://app.bali-realestate.com/chatbot?query=general-consultation"
                    className="flex-1 bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-xl font-semibold text-center transition-all duration-300 hover:scale-105"
                    target="_self"
                  >
                    Start Quick Chat
                  </a>
                  <a
                    href="tel:+6281234567890"
                    className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 px-6 py-3 rounded-xl font-semibold text-center transition-all duration-300 hover:scale-105"
                  >
                    Call Now
                  </a>
                </div>
              </div>

              {/* Services Overview */}
              <div className="bg-emerald-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-emerald-900 mb-4">Our Services</h3>
                <ul className="space-y-2 text-sm text-emerald-800">
                  <li className="flex items-center">
                    <span className="w-1.5 h-1.5 bg-emerald-600 rounded-full mr-3"></span>
                    Long-term Villa Rentals
                  </li>
                  <li className="flex items-center">
                    <span className="w-1.5 h-1.5 bg-emerald-600 rounded-full mr-3"></span>
                    Property Purchase Assistance
                  </li>
                  <li className="flex items-center">
                    <span className="w-1.5 h-1.5 bg-emerald-600 rounded-full mr-3"></span>
                    Investment Consultation
                  </li>
                  <li className="flex items-center">
                    <span className="w-1.5 h-1.5 bg-emerald-600 rounded-full mr-3"></span>
                    Legal & Visa Support
                  </li>
                  <li className="flex items-center">
                    <span className="w-1.5 h-1.5 bg-emerald-600 rounded-full mr-3"></span>
                    Market Analysis
                  </li>
                  <li className="flex items-center">
                    <span className="w-1.5 h-1.5 bg-emerald-600 rounded-full mr-3"></span>
                    Property Management
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Interactive Office Map */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4">
          <InteractiveOfficeMap />
        </div>
      </section>

      {/* Enhanced FAQ Accordions */}
      <ContactFAQAccordions />

      {/* Floating Chat Widget */}
      <FloatingChatWidget showOnContactPage={true} />
    </div>
  )
}

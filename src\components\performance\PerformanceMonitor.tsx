/**
 * Performance Monitor Component
 * 
 * Client-side component for monitoring and reporting performance metrics
 */

'use client';

import { useEffect, useState } from 'react';
import { initPerformanceMonitoring, onWebVitals } from '@/lib/performance';

export interface PerformanceMonitorProps {
  /** Whether to enable performance monitoring */
  enabled?: boolean;
  /** Whether to log metrics to console in development */
  debug?: boolean;
  /** Custom analytics endpoint */
  analyticsEndpoint?: string;
  /** Sample rate for performance monitoring (0-1) */
  sampleRate?: number;
}

/**
 * Performance Monitor Component
 * 
 * This component should be included once in your app root to enable
 * performance monitoring across the entire application.
 */
export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  enabled = true,
  debug = process.env.NODE_ENV === 'development',
  analyticsEndpoint,
  sampleRate = 1.0,
}) => {
  useEffect(() => {
    if (!enabled || typeof window === 'undefined') {
      return;
    }

    // Sample rate check
    if (Math.random() > sampleRate) {
      return;
    }

    // Initialize performance monitoring
    initPerformanceMonitoring();

    // Set up Web Vitals monitoring
    if (typeof window !== 'undefined') {
      // Dynamic import to avoid SSR issues
      import('web-vitals').then(({ onCLS, onFID, onFCP, onLCP, onTTFB }) => {
        onCLS(onWebVitals);
        onFID(onWebVitals);
        onFCP(onWebVitals);
        onLCP(onWebVitals);
        onTTFB(onWebVitals);
      }).catch((error) => {
        if (debug) {
          console.warn('[Performance] Failed to load web-vitals:', error);
        }
      });
    }

    // Monitor resource loading performance
    const resourceObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.duration > 1000) { // Resources taking more than 1 second
          console.warn(`[Performance] Slow resource: ${entry.name} took ${entry.duration.toFixed(2)}ms`);
        }
      }
    });

    try {
      resourceObserver.observe({ entryTypes: ['resource'] });
    } catch (error) {
      if (debug) {
        console.warn('[Performance] Resource observer not supported');
      }
    }

    // Monitor navigation performance
    const navigationObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        const navigation = entry as PerformanceNavigationTiming;
        
        if (debug) {
          console.log('[Performance] Navigation timing:', {
            'DNS Lookup': navigation.domainLookupEnd - navigation.domainLookupStart,
            'TCP Connection': navigation.connectEnd - navigation.connectStart,
            'TLS Handshake': navigation.secureConnectionStart > 0 
              ? navigation.connectEnd - navigation.secureConnectionStart 
              : 0,
            'Request': navigation.responseStart - navigation.requestStart,
            'Response': navigation.responseEnd - navigation.responseStart,
            'DOM Processing': navigation.domContentLoadedEventStart - navigation.responseEnd,
            'Resource Loading': navigation.loadEventStart - navigation.domContentLoadedEventEnd,
          });
        }
      }
    });

    try {
      navigationObserver.observe({ entryTypes: ['navigation'] });
    } catch (error) {
      if (debug) {
        console.warn('[Performance] Navigation observer not supported');
      }
    }

    // Cleanup observers
    return () => {
      resourceObserver.disconnect();
      navigationObserver.disconnect();
    };
  }, [enabled, debug, analyticsEndpoint, sampleRate]);

  // This component doesn't render anything
  return null;
};

/**
 * Performance Debug Panel
 * 
 * Development-only component that shows real-time performance metrics
 */
export const PerformanceDebugPanel: React.FC = () => {
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs font-mono z-50 max-w-sm">
      <div className="font-bold mb-2">Performance Debug</div>
      <div className="space-y-1">
        <div>Memory: {typeof window !== 'undefined' && 'memory' in performance 
          ? `${Math.round((performance as any).memory.usedJSHeapSize / 1024 / 1024)}MB`
          : 'N/A'}</div>
        <div>Timing: {typeof window !== 'undefined' 
          ? `${Math.round(performance.now())}ms`
          : 'N/A'}</div>
      </div>
    </div>
  );
};

/**
 * Performance Budget Warning
 * 
 * Component that shows warnings when performance budgets are exceeded
 */
export const PerformanceBudgetWarning: React.FC<{
  budgetViolations?: string[];
}> = ({ budgetViolations = [] }) => {
  if (process.env.NODE_ENV !== 'development' || budgetViolations.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 bg-red-500 text-white p-4 rounded-lg text-sm z-50 max-w-md">
      <div className="font-bold mb-2">⚠️ Performance Budget Exceeded</div>
      <ul className="space-y-1">
        {budgetViolations.map((violation, index) => (
          <li key={index} className="text-xs">• {violation}</li>
        ))}
      </ul>
    </div>
  );
};

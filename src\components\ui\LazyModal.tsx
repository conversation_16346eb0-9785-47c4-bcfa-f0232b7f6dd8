/**
 * Lazy-loaded Modal Component
 * 
 * This component dynamically imports the Modal component to reduce initial bundle size
 */

'use client';

import { Suspense, lazy } from 'react';
import { ModalProps } from './Modal';

// Lazy load the Modal component
const Modal = lazy(() => import('./Modal').then(module => ({ default: module.Modal })));

// Loading component for Modal (minimal since modals are usually quick)
function ModalLoading() {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-4">
        <div className="animate-pulse">Loading...</div>
      </div>
    </div>
  );
}

// Lazy Modal wrapper component
export function LazyModal(props: ModalProps) {
  // Only render if modal should be open
  if (!props.isOpen) {
    return null;
  }

  return (
    <Suspense fallback={<ModalLoading />}>
      <Modal {...props} />
    </Suspense>
  );
}

export default LazyModal;

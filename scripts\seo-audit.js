#!/usr/bin/env node

/**
 * SEO Audit Script
 * 
 * Comprehensive SEO analysis and recommendations
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

// SEO audit configuration
const SEO_CONFIG = {
  // Core SEO elements to check
  requiredElements: [
    'title',
    'meta[name="description"]',
    'meta[name="keywords"]',
    'meta[property="og:title"]',
    'meta[property="og:description"]',
    'meta[property="og:image"]',
    'meta[name="twitter:card"]',
    'link[rel="canonical"]',
    'script[type="application/ld+json"]',
  ],
  
  // Performance thresholds
  performance: {
    titleLength: { min: 30, max: 60 },
    descriptionLength: { min: 120, max: 160 },
    keywordDensity: { max: 3 }, // percentage
    imageAltText: { required: true },
    headingStructure: { required: true },
  },
  
  // Target keywords for different page types
  targetKeywords: {
    homepage: ['bali real estate', 'property for sale bali', 'villa for sale bali'],
    location: ['property for sale', 'villa for sale', 'real estate'],
    propertyType: ['for sale bali', 'property bali', 'investment'],
    contact: ['real estate agent', 'property consultation', 'contact'],
  },
};

// URLs to audit
const AUDIT_URLS = [
  'http://localhost:3001',
  'http://localhost:3001/locations/canggu',
  'http://localhost:3001/property-types/villa',
  'http://localhost:3001/contact',
];

async function runSEOAudit(url) {
  console.log(`🔍 SEO Audit: ${url}`);
  
  const browser = await chromium.launch({ headless: true });
  const page = await browser.newPage();

  try {
    // Navigate to the page
    await page.goto(url, { waitUntil: 'networkidle' });
    
    // Wait for page to be fully loaded
    await page.waitForTimeout(2000);
    
    const audit = {
      url,
      timestamp: new Date().toISOString(),
      seo: {},
      performance: {},
      issues: [],
      recommendations: [],
      score: 0,
    };

    // Check basic SEO elements
    audit.seo = await checkBasicSEO(page);
    
    // Check meta tags
    audit.seo.metaTags = await checkMetaTags(page);
    
    // Check structured data
    audit.seo.structuredData = await checkStructuredData(page);
    
    // Check content optimization
    audit.seo.content = await checkContentOptimization(page);
    
    // Check technical SEO
    audit.seo.technical = await checkTechnicalSEO(page);
    
    // Calculate overall score
    audit.score = calculateSEOScore(audit.seo);
    
    // Generate issues and recommendations
    audit.issues = generateIssues(audit.seo);
    audit.recommendations = generateRecommendations(audit.seo);
    
    await browser.close();
    return audit;
    
  } catch (error) {
    await browser.close();
    throw error;
  }
}

async function checkBasicSEO(page) {
  const title = await page.title();
  const description = await page.$eval('meta[name="description"]', el => el.content).catch(() => '');
  const keywords = await page.$eval('meta[name="keywords"]', el => el.content).catch(() => '');
  const canonical = await page.$eval('link[rel="canonical"]', el => el.href).catch(() => '');
  
  return {
    title: {
      content: title,
      length: title.length,
      present: !!title,
    },
    description: {
      content: description,
      length: description.length,
      present: !!description,
    },
    keywords: {
      content: keywords,
      present: !!keywords,
    },
    canonical: {
      url: canonical,
      present: !!canonical,
    },
  };
}

async function checkMetaTags(page) {
  const ogTitle = await page.$eval('meta[property="og:title"]', el => el.content).catch(() => '');
  const ogDescription = await page.$eval('meta[property="og:description"]', el => el.content).catch(() => '');
  const ogImage = await page.$eval('meta[property="og:image"]', el => el.content).catch(() => '');
  const twitterCard = await page.$eval('meta[name="twitter:card"]', el => el.content).catch(() => '');
  
  return {
    openGraph: {
      title: { content: ogTitle, present: !!ogTitle },
      description: { content: ogDescription, present: !!ogDescription },
      image: { content: ogImage, present: !!ogImage },
    },
    twitter: {
      card: { content: twitterCard, present: !!twitterCard },
    },
  };
}

async function checkStructuredData(page) {
  const structuredDataElements = await page.$$('script[type="application/ld+json"]');
  const structuredData = [];
  
  for (const element of structuredDataElements) {
    try {
      const content = await element.textContent();
      const parsed = JSON.parse(content);
      structuredData.push(parsed);
    } catch (error) {
      // Invalid JSON
    }
  }
  
  return {
    present: structuredData.length > 0,
    count: structuredData.length,
    types: structuredData.map(data => data['@type']).filter(Boolean),
    data: structuredData,
  };
}

async function checkContentOptimization(page) {
  const headings = await page.$$eval('h1, h2, h3, h4, h5, h6', elements => 
    elements.map(el => ({ tag: el.tagName.toLowerCase(), text: el.textContent.trim() }))
  );
  
  const images = await page.$$eval('img', elements => 
    elements.map(el => ({ src: el.src, alt: el.alt, hasAlt: !!el.alt }))
  );
  
  const links = await page.$$eval('a', elements => 
    elements.map(el => ({ href: el.href, text: el.textContent.trim(), internal: el.href.includes(el.baseURI) }))
  );
  
  return {
    headings: {
      structure: headings,
      h1Count: headings.filter(h => h.tag === 'h1').length,
      hasProperStructure: headings.filter(h => h.tag === 'h1').length === 1,
    },
    images: {
      total: images.length,
      withAlt: images.filter(img => img.hasAlt).length,
      withoutAlt: images.filter(img => !img.hasAlt).length,
      altTextCoverage: images.length > 0 ? (images.filter(img => img.hasAlt).length / images.length) * 100 : 0,
    },
    links: {
      total: links.length,
      internal: links.filter(link => link.internal).length,
      external: links.filter(link => !link.internal).length,
    },
  };
}

async function checkTechnicalSEO(page) {
  const viewport = await page.$eval('meta[name="viewport"]', el => el.content).catch(() => '');
  const robots = await page.$eval('meta[name="robots"]', el => el.content).catch(() => '');
  const lang = await page.$eval('html', el => el.lang).catch(() => '');
  
  return {
    viewport: { content: viewport, present: !!viewport },
    robots: { content: robots, present: !!robots },
    htmlLang: { content: lang, present: !!lang },
  };
}

function calculateSEOScore(seo) {
  let score = 0;
  let maxScore = 0;
  
  // Title (20 points)
  maxScore += 20;
  if (seo.title.present) {
    score += 10;
    if (seo.title.length >= 30 && seo.title.length <= 60) score += 10;
  }
  
  // Description (20 points)
  maxScore += 20;
  if (seo.description.present) {
    score += 10;
    if (seo.description.length >= 120 && seo.description.length <= 160) score += 10;
  }
  
  // Meta tags (20 points)
  maxScore += 20;
  if (seo.metaTags.openGraph.title.present) score += 5;
  if (seo.metaTags.openGraph.description.present) score += 5;
  if (seo.metaTags.openGraph.image.present) score += 5;
  if (seo.metaTags.twitter.card.present) score += 5;
  
  // Structured data (15 points)
  maxScore += 15;
  if (seo.structuredData.present) score += 15;
  
  // Content optimization (15 points)
  maxScore += 15;
  if (seo.content.headings.hasProperStructure) score += 5;
  if (seo.content.images.altTextCoverage >= 90) score += 10;
  
  // Technical SEO (10 points)
  maxScore += 10;
  if (seo.technical.viewport.present) score += 3;
  if (seo.technical.robots.present) score += 3;
  if (seo.technical.htmlLang.present) score += 4;
  
  return Math.round((score / maxScore) * 100);
}

function generateIssues(seo) {
  const issues = [];
  
  if (!seo.title.present) issues.push({ type: 'critical', message: 'Missing page title' });
  if (seo.title.length < 30 || seo.title.length > 60) issues.push({ type: 'warning', message: 'Title length not optimal (30-60 chars)' });
  
  if (!seo.description.present) issues.push({ type: 'critical', message: 'Missing meta description' });
  if (seo.description.length < 120 || seo.description.length > 160) issues.push({ type: 'warning', message: 'Description length not optimal (120-160 chars)' });
  
  if (!seo.metaTags.openGraph.title.present) issues.push({ type: 'warning', message: 'Missing Open Graph title' });
  if (!seo.metaTags.openGraph.image.present) issues.push({ type: 'warning', message: 'Missing Open Graph image' });
  
  if (!seo.structuredData.present) issues.push({ type: 'warning', message: 'No structured data found' });
  
  if (seo.content.headings.h1Count !== 1) issues.push({ type: 'warning', message: 'Should have exactly one H1 tag' });
  if (seo.content.images.altTextCoverage < 90) issues.push({ type: 'warning', message: 'Some images missing alt text' });
  
  return issues;
}

function generateRecommendations(seo) {
  const recommendations = [];
  
  if (seo.title.length < 30) recommendations.push('Expand title to include more relevant keywords');
  if (seo.title.length > 60) recommendations.push('Shorten title to prevent truncation in search results');
  
  if (seo.description.length < 120) recommendations.push('Expand meta description to better describe page content');
  if (seo.description.length > 160) recommendations.push('Shorten meta description to prevent truncation');
  
  if (!seo.keywords.present) recommendations.push('Add meta keywords for better content categorization');
  
  if (seo.structuredData.count === 0) recommendations.push('Add structured data for rich snippets');
  
  if (seo.content.images.withoutAlt > 0) recommendations.push('Add alt text to all images for accessibility and SEO');
  
  return recommendations;
}

async function generateReport(audits) {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalPages: audits.length,
      averageScore: Math.round(audits.reduce((sum, audit) => sum + audit.score, 0) / audits.length),
      criticalIssues: audits.reduce((sum, audit) => sum + audit.issues.filter(i => i.type === 'critical').length, 0),
      warnings: audits.reduce((sum, audit) => sum + audit.issues.filter(i => i.type === 'warning').length, 0),
    },
    audits,
  };

  const reportsDir = path.join(process.cwd(), 'seo-reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `seo-audit-${timestamp}.json`;
  const filepath = path.join(reportsDir, filename);

  fs.writeFileSync(filepath, JSON.stringify(report, null, 2));

  // Also save as latest
  const latestPath = path.join(reportsDir, 'latest.json');
  fs.writeFileSync(latestPath, JSON.stringify(report, null, 2));

  return { report, filepath };
}

function printSummary(report) {
  console.log('\n🎯 SEO AUDIT SUMMARY');
  console.log('====================');
  console.log(`📅 Timestamp: ${report.timestamp}`);
  console.log(`📄 Pages Audited: ${report.summary.totalPages}`);
  console.log(`📊 Average Score: ${report.summary.averageScore}/100`);
  console.log(`🚨 Critical Issues: ${report.summary.criticalIssues}`);
  console.log(`⚠️  Warnings: ${report.summary.warnings}`);

  console.log('\n📄 PAGE RESULTS:');
  report.audits.forEach(audit => {
    const status = audit.score >= 80 ? '✅' : audit.score >= 60 ? '⚠️' : '❌';
    console.log(`  ${status} ${audit.url}: ${audit.score}/100`);
    
    if (audit.issues.length > 0) {
      audit.issues.slice(0, 3).forEach(issue => {
        const icon = issue.type === 'critical' ? '🚨' : '⚠️';
        console.log(`     ${icon} ${issue.message}`);
      });
    }
  });

  if (report.summary.averageScore >= 80) {
    console.log('\n✅ SEO audit passed! Good optimization.');
  } else if (report.summary.averageScore >= 60) {
    console.log('\n⚠️ SEO audit shows room for improvement.');
  } else {
    console.log('\n❌ SEO audit failed. Significant optimization needed.');
  }
}

async function main() {
  console.log('🚀 Starting SEO Audit...\n');

  try {
    const audits = [];

    for (const url of AUDIT_URLS) {
      const audit = await runSEOAudit(url);
      audits.push(audit);
      
      const status = audit.score >= 80 ? '✅' : audit.score >= 60 ? '⚠️' : '❌';
      console.log(`${status} ${url}: ${audit.score}/100`);
    }

    const { report, filepath } = await generateReport(audits);
    
    console.log(`\n📊 Report saved: ${filepath}`);
    printSummary(report);

    // Exit with error code if average score is too low
    if (report.summary.averageScore < 70) {
      console.log('\n❌ SEO audit failed - optimization needed');
      process.exit(1);
    } else {
      console.log('\n✅ SEO audit passed!');
      process.exit(0);
    }

  } catch (error) {
    console.error('❌ SEO audit failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  runSEOAudit,
  SEO_CONFIG,
};

/**
 * Playwright Global Setup
 * 
 * Global setup for E2E tests
 */

import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global test setup...');
  
  // Launch browser for setup
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    // Wait for dev server to be ready
    console.log('⏳ Waiting for dev server...');
    await page.goto('http://localhost:3001', { timeout: 60000 });
    
    // Verify the app is working
    await page.waitForSelector('h1', { timeout: 10000 });
    console.log('✅ Dev server is ready');
    
    // Pre-warm the application
    console.log('🔥 Pre-warming application...');
    await page.goto('http://localhost:3001/locations/canggu');
    await page.goto('http://localhost:3001/contact');
    await page.goto('http://localhost:3001');
    
    console.log('✅ Application pre-warmed');
    
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
  
  console.log('✅ Global setup completed');
}

export default globalSetup;

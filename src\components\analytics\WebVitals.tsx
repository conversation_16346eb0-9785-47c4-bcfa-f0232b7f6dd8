/**
 * Web Vitals Monitoring Component
 * 
 * Tracks Core Web Vitals and sends data to analytics
 */

'use client';

import { useEffect } from 'react';
import { onCLS, onINP, onFCP, onLCP, onTTFB } from 'web-vitals';

interface WebVitalsMetric {
  id: string;
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  entries: PerformanceEntry[];
}

// Thresholds for Core Web Vitals (Google's recommendations)
const VITALS_THRESHOLDS = {
  CLS: { good: 0.1, poor: 0.25 },
  INP: { good: 200, poor: 500 }, // Interaction to Next Paint (replaces FID)
  FCP: { good: 1800, poor: 3000 },
  LCP: { good: 2500, poor: 4000 },
  TTFB: { good: 800, poor: 1800 },
};

function getRating(name: string, value: number): 'good' | 'needs-improvement' | 'poor' {
  const thresholds = VITALS_THRESHOLDS[name as keyof typeof VITALS_THRESHOLDS];
  if (!thresholds) return 'good';
  
  if (value <= thresholds.good) return 'good';
  if (value <= thresholds.poor) return 'needs-improvement';
  return 'poor';
}

function sendToAnalytics(metric: WebVitalsMetric) {
  // Send to Google Analytics 4 if available
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', metric.name, {
      event_category: 'Web Vitals',
      event_label: metric.id,
      value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
      custom_map: {
        metric_rating: metric.rating,
        metric_delta: metric.delta,
      },
    });
  }

  // Send to console in development
  if (process.env.NODE_ENV === 'development') {
    console.log('Web Vitals:', {
      name: metric.name,
      value: metric.value,
      rating: metric.rating,
      id: metric.id,
    });
  }

  // Send to custom analytics endpoint if configured
  if (process.env.NEXT_PUBLIC_ANALYTICS_ENDPOINT) {
    fetch(process.env.NEXT_PUBLIC_ANALYTICS_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'web-vitals',
        metric: {
          name: metric.name,
          value: metric.value,
          rating: metric.rating,
          id: metric.id,
          url: window.location.href,
          timestamp: Date.now(),
        },
      }),
    }).catch((error) => {
      console.error('Failed to send Web Vitals to analytics:', error);
    });
  }
}

export function WebVitals() {
  useEffect(() => {
    // Track Core Web Vitals
    onCLS((metric) => {
      sendToAnalytics({
        ...metric,
        rating: getRating('CLS', metric.value),
      });
    });

    onINP((metric) => {
      sendToAnalytics({
        ...metric,
        rating: getRating('INP', metric.value),
      });
    });

    onFCP((metric) => {
      sendToAnalytics({
        ...metric,
        rating: getRating('FCP', metric.value),
      });
    });

    onLCP((metric) => {
      sendToAnalytics({
        ...metric,
        rating: getRating('LCP', metric.value),
      });
    });

    onTTFB((metric) => {
      sendToAnalytics({
        ...metric,
        rating: getRating('TTFB', metric.value),
      });
    });
  }, []);

  return null; // This component doesn't render anything
}

// Performance monitoring hook
export function usePerformanceMonitoring() {
  useEffect(() => {
    // Monitor navigation timing
    if (typeof window !== 'undefined' && window.performance) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigation) {
        const metrics = {
          dns: navigation.domainLookupEnd - navigation.domainLookupStart,
          tcp: navigation.connectEnd - navigation.connectStart,
          ssl: navigation.connectEnd - navigation.secureConnectionStart,
          ttfb: navigation.responseStart - navigation.requestStart,
          download: navigation.responseEnd - navigation.responseStart,
          domInteractive: navigation.domInteractive - navigation.navigationStart,
          domComplete: navigation.domComplete - navigation.navigationStart,
          loadComplete: navigation.loadEventEnd - navigation.navigationStart,
        };

        // Send navigation timing to analytics
        if (window.gtag) {
          Object.entries(metrics).forEach(([name, value]) => {
            if (value > 0) {
              window.gtag('event', 'navigation_timing', {
                event_category: 'Performance',
                event_label: name,
                value: Math.round(value),
              });
            }
          });
        }

        // Log in development
        if (process.env.NODE_ENV === 'development') {
          console.log('Navigation Timing:', metrics);
        }
      }
    }

    // Monitor resource timing for large resources
    if (typeof window !== 'undefined' && window.performance) {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      
      resources.forEach((resource) => {
        // Track slow resources (>1s)
        const duration = resource.responseEnd - resource.requestStart;
        if (duration > 1000) {
          if (window.gtag) {
            window.gtag('event', 'slow_resource', {
              event_category: 'Performance',
              event_label: resource.name,
              value: Math.round(duration),
            });
          }

          if (process.env.NODE_ENV === 'development') {
            console.warn('Slow resource detected:', {
              name: resource.name,
              duration: Math.round(duration),
              size: resource.transferSize,
            });
          }
        }
      });
    }
  }, []);
}

// Performance budget monitoring
export function checkPerformanceBudget() {
  if (typeof window === 'undefined' || !window.performance) return;

  const budget = {
    maxLCP: 2500, // 2.5s
    maxINP: 200,  // 200ms (replaces FID)
    maxCLS: 0.1,  // 0.1
    maxTTFB: 800, // 800ms
    maxBundleSize: 150 * 1024, // 150KB
  };

  // Check if we're exceeding performance budget
  onLCP((metric) => {
    if (metric.value > budget.maxLCP) {
      console.warn(`LCP budget exceeded: ${metric.value}ms > ${budget.maxLCP}ms`);

      if (window.gtag) {
        window.gtag('event', 'performance_budget_exceeded', {
          event_category: 'Performance',
          event_label: 'LCP',
          value: Math.round(metric.value),
        });
      }
    }
  });

  onINP((metric) => {
    if (metric.value > budget.maxINP) {
      console.warn(`INP budget exceeded: ${metric.value}ms > ${budget.maxINP}ms`);

      if (window.gtag) {
        window.gtag('event', 'performance_budget_exceeded', {
          event_category: 'Performance',
          event_label: 'INP',
          value: Math.round(metric.value),
        });
      }
    }
  });

  onCLS((metric) => {
    if (metric.value > budget.maxCLS) {
      console.warn(`CLS budget exceeded: ${metric.value} > ${budget.maxCLS}`);

      if (window.gtag) {
        window.gtag('event', 'performance_budget_exceeded', {
          event_category: 'Performance',
          event_label: 'CLS',
          value: Math.round(metric.value * 1000),
        });
      }
    }
  });
}

export default WebVitals;

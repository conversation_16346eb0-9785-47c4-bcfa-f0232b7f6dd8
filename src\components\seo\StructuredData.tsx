/**
 * Structured Data Component
 * 
 * Generates JSON-LD structured data for rich snippets and SEO
 */

import Head from 'next/head';
import { STRUCTURED_DATA, SITE_CONFIG } from '@/lib/seo';

interface StructuredDataProps {
  data: Record<string, any> | Record<string, any>[];
}

export function StructuredData({ data }: StructuredDataProps) {
  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(Array.isArray(data) ? data : [data]),
        }}
      />
    </Head>
  );
}

// Organization structured data
export function OrganizationStructuredData() {
  return <StructuredData data={STRUCTURED_DATA.organization} />;
}

// Website structured data
export function WebsiteStructuredData() {
  return <StructuredData data={STRUCTURED_DATA.website} />;
}

// Breadcrumb structured data
export function BreadcrumbStructuredData({ 
  items 
}: { 
  items: Array<{ name: string; url: string }> 
}) {
  return <StructuredData data={STRUCTURED_DATA.breadcrumbList(items)} />;
}

// Property listing structured data
export function PropertyStructuredData({
  property
}: {
  property: {
    name: string;
    description: string;
    price: number;
    currency: string;
    location: string;
    propertyType: string;
    bedrooms?: number;
    bathrooms?: number;
    floorSize?: number;
    images: string[];
    url?: string;
  }
}) {
  const structuredProperty = {
    ...property,
    url: property.url || `${SITE_CONFIG.url}/properties/${property.name.toLowerCase().replace(/\s+/g, '-')}`,
  };

  return <StructuredData data={STRUCTURED_DATA.realEstateProperty(structuredProperty)} />;
}

// FAQ structured data
export function FAQStructuredData({
  faqs
}: {
  faqs: Array<{ question: string; answer: string }>
}) {
  const faqData = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  };

  return <StructuredData data={faqData} />;
}

// Local business structured data
export function LocalBusinessStructuredData({
  location,
  address,
  phone,
  email,
}: {
  location: string;
  address?: {
    street?: string;
    city: string;
    region: string;
    postalCode?: string;
    country: string;
  };
  phone?: string;
  email?: string;
}) {
  const businessData = {
    '@context': 'https://schema.org',
    '@type': 'RealEstateAgent',
    name: `${SITE_CONFIG.name} - ${location}`,
    url: SITE_CONFIG.url,
    logo: `${SITE_CONFIG.url}/images/logo.png`,
    description: `Professional real estate services in ${location}, Bali`,
    email: email || SITE_CONFIG.contactEmail,
    telephone: phone,
    address: address ? {
      '@type': 'PostalAddress',
      streetAddress: address.street,
      addressLocality: address.city,
      addressRegion: address.region,
      postalCode: address.postalCode,
      addressCountry: address.country,
    } : {
      '@type': 'PostalAddress',
      addressLocality: location,
      addressRegion: 'Bali',
      addressCountry: 'ID',
    },
    areaServed: {
      '@type': 'City',
      name: location,
      addressCountry: 'ID',
    },
    sameAs: [
      'https://www.facebook.com/BaliRealEstate',
      'https://www.instagram.com/BaliRealEstate',
      'https://twitter.com/BaliRealEstate',
    ],
  };

  return <StructuredData data={businessData} />;
}

// Article structured data (for blog posts)
export function ArticleStructuredData({
  title,
  description,
  author,
  datePublished,
  dateModified,
  image,
  url,
}: {
  title: string;
  description: string;
  author: string;
  datePublished: string;
  dateModified?: string;
  image: string;
  url: string;
}) {
  const articleData = {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: title,
    description: description,
    image: image.startsWith('http') ? image : `${SITE_CONFIG.url}${image}`,
    url: url.startsWith('http') ? url : `${SITE_CONFIG.url}${url}`,
    datePublished: datePublished,
    dateModified: dateModified || datePublished,
    author: {
      '@type': 'Person',
      name: author,
    },
    publisher: {
      '@type': 'Organization',
      name: SITE_CONFIG.name,
      logo: {
        '@type': 'ImageObject',
        url: `${SITE_CONFIG.url}/images/logo.png`,
      },
    },
  };

  return <StructuredData data={articleData} />;
}

// Review structured data
export function ReviewStructuredData({
  reviews
}: {
  reviews: Array<{
    author: string;
    rating: number;
    reviewBody: string;
    datePublished: string;
  }>
}) {
  const reviewData = reviews.map(review => ({
    '@context': 'https://schema.org',
    '@type': 'Review',
    author: {
      '@type': 'Person',
      name: review.author,
    },
    reviewRating: {
      '@type': 'Rating',
      ratingValue: review.rating,
      bestRating: 5,
    },
    reviewBody: review.reviewBody,
    datePublished: review.datePublished,
    itemReviewed: {
      '@type': 'RealEstateAgent',
      name: SITE_CONFIG.name,
    },
  }));

  return <StructuredData data={reviewData} />;
}

// Service structured data
export function ServiceStructuredData({
  services
}: {
  services: Array<{
    name: string;
    description: string;
    areaServed: string[];
  }>
}) {
  const serviceData = services.map(service => ({
    '@context': 'https://schema.org',
    '@type': 'Service',
    name: service.name,
    description: service.description,
    provider: {
      '@type': 'RealEstateAgent',
      name: SITE_CONFIG.name,
      url: SITE_CONFIG.url,
    },
    areaServed: service.areaServed.map(area => ({
      '@type': 'City',
      name: area,
      addressCountry: 'ID',
    })),
  }));

  return <StructuredData data={serviceData} />;
}

// Event structured data (for property viewings, open houses)
export function EventStructuredData({
  name,
  description,
  startDate,
  endDate,
  location,
  organizer,
}: {
  name: string;
  description: string;
  startDate: string;
  endDate?: string;
  location: {
    name: string;
    address: string;
  };
  organizer?: string;
}) {
  const eventData = {
    '@context': 'https://schema.org',
    '@type': 'Event',
    name: name,
    description: description,
    startDate: startDate,
    endDate: endDate || startDate,
    location: {
      '@type': 'Place',
      name: location.name,
      address: {
        '@type': 'PostalAddress',
        streetAddress: location.address,
        addressLocality: 'Bali',
        addressCountry: 'ID',
      },
    },
    organizer: {
      '@type': 'Organization',
      name: organizer || SITE_CONFIG.name,
      url: SITE_CONFIG.url,
    },
  };

  return <StructuredData data={eventData} />;
}

export default StructuredData;

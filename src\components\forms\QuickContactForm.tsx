'use client'

import { useState } from 'react'
import { trackConversion } from '@/components/analytics/GoogleAnalytics'

interface QuickContactFormProps {
  title?: string
  subtitle?: string
  source?: string
  locationSlug?: string
  propertyType?: string
  className?: string
}

export default function QuickContactForm({ 
  title = "Get Expert Advice",
  subtitle = "Connect with our Bali real estate specialists",
  source = 'quick-contact',
  locationSlug,
  propertyType,
  className = ""
}: QuickContactFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    message: '',
    timeline: '',
    gdprConsent: false,
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked
      setFormData(prev => ({ ...prev, [name]: checked }))
    } else {
      setFormData(prev => ({ ...prev, [name]: value }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setSubmitStatus('idle')

    try {
      const submitData = {
        ...formData,
        leadType: 'general',
        source: source,
        propertyPreferences: {
          preferredLocations: locationSlug ? [locationSlug] : [],
          propertyType: propertyType ? [propertyType] : [],
          transactionType: 'undecided',
        },
        emailOptIn: true,
      }

      const response = await fetch('/api/leads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (response.ok) {
        setSubmitStatus('success')
        trackConversion('quick_contact_form')
        
        // Reset form
        setFormData({
          name: '',
          email: '',
          phone: '',
          message: '',
          timeline: '',
          gdprConsent: false,
        })
      } else {
        setSubmitStatus('error')
      }
    } catch (error) {
      console.error('Form submission error:', error)
      setSubmitStatus('error')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (submitStatus === 'success') {
    return (
      <div className={`bg-emerald-50 border border-emerald-200 rounded-lg p-6 text-center ${className}`}>
        <div className="text-emerald-600 text-3xl mb-3">✓</div>
        <h3 className="text-lg font-semibold text-emerald-800 mb-2">Message Sent!</h3>
        <p className="text-emerald-700 text-sm mb-4">
          We'll get back to you within 24 hours.
        </p>
        <button
          onClick={() => setSubmitStatus('idle')}
          className="text-emerald-600 hover:text-emerald-800 font-medium text-sm"
        >
          Send Another Message
        </button>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      <div className="mb-6 text-center">
        <h3 className="text-xl font-bold text-gray-900 mb-2">{title}</h3>
        <p className="text-gray-600 text-sm">{subtitle}</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <input
            type="text"
            name="name"
            required
            value={formData.name}
            onChange={handleInputChange}
            placeholder="Your Name *"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-sm"
          />
        </div>
        
        <div>
          <input
            type="email"
            name="email"
            required
            value={formData.email}
            onChange={handleInputChange}
            placeholder="Email Address *"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-sm"
          />
        </div>

        <div>
          <input
            type="tel"
            name="phone"
            value={formData.phone}
            onChange={handleInputChange}
            placeholder="Phone Number (optional)"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-sm"
          />
        </div>

        <div>
          <select
            name="timeline"
            required
            value={formData.timeline}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-sm"
          >
            <option value="">When are you looking? *</option>
            <option value="immediate">Immediate (within 1 month)</option>
            <option value="short-term">Short-term (1-3 months)</option>
            <option value="medium-term">Medium-term (3-6 months)</option>
            <option value="long-term">Long-term (6+ months)</option>
            <option value="research">Just researching</option>
          </select>
        </div>

        <div>
          <textarea
            name="message"
            required
            rows={3}
            value={formData.message}
            onChange={handleInputChange}
            placeholder="Tell us about your requirements... *"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-sm"
          />
        </div>

        <div>
          <label className="flex items-start text-xs">
            <input
              type="checkbox"
              name="gdprConsent"
              required
              checked={formData.gdprConsent}
              onChange={handleInputChange}
              className="mt-0.5 mr-2 h-3 w-3 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"
            />
            <span className="text-gray-600">
              I consent to processing of my data for this inquiry. *
            </span>
          </label>
        </div>

        <div>
          <button
            type="submit"
            disabled={isSubmitting || !formData.gdprConsent}
            className="w-full bg-emerald-600 text-white px-4 py-2 rounded-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium text-sm"
          >
            {isSubmitting ? 'Sending...' : 'Get Expert Advice'}
          </button>
        </div>

        {submitStatus === 'error' && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-red-700 text-xs">
              Error sending message. Please try again.
            </p>
          </div>
        )}
      </form>
    </div>
  )
}

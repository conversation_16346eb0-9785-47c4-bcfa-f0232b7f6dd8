'use client'

import Link from 'next/link'
import { useState, useEffect } from 'react'

const propertyTypes = [
  {
    id: 1,
    name: 'Villas',
    slug: 'villas',
    description: 'Private luxury villas with pools, gardens, and full amenities. Perfect for families and groups seeking privacy and comfort.',
    icon: (
      <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
      </svg>
    ),
    features: ['Private Pool', 'Garden', 'Full Kitchen', 'Multiple Bedrooms'],
    priceRange: '$800-4000/month',
    color: 'emerald'
  },
  {
    id: 2,
    name: 'Guesthouses',
    slug: 'guesthouses',
    description: 'Authentic Balinese accommodations with local charm. Budget-friendly options with community atmosphere.',
    icon: (
      <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
      </svg>
    ),
    features: ['Shared Spaces', 'Local Experience', 'Budget-Friendly', 'Cultural Immersion'],
    priceRange: '$300-1200/month',
    color: 'blue'
  },
  {
    id: 3,
    name: 'Apartments',
    slug: 'apartments',
    description: 'Modern apartments with contemporary amenities. Ideal for professionals and couples seeking convenience.',
    icon: (
      <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
      </svg>
    ),
    features: ['Modern Amenities', 'Security', 'Maintenance Included', 'Central Location'],
    priceRange: '$500-2500/month',
    color: 'purple'
  },
  {
    id: 4,
    name: 'Land & Commercial',
    slug: 'land-commercial',
    description: 'Investment opportunities in land and commercial properties. Perfect for business ventures and development.',
    icon: (
      <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 21l-7-5-7 5V5a2 2 0 012-2h10a2 2 0 012 2v16z" />
      </svg>
    ),
    features: ['Investment Potential', 'Development Rights', 'Strategic Locations', 'Business Opportunities'],
    priceRange: '$50k-2M purchase',
    color: 'orange'
  }
]

const PropertyTypeCard = ({ propertyType, index }: { propertyType: typeof propertyTypes[0], index: number }) => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), index * 150)
    return () => clearTimeout(timer)
  }, [index])

  const colorClasses = {
    emerald: 'bg-emerald-50 text-emerald-600 border-emerald-200 hover:bg-emerald-100',
    blue: 'bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100',
    purple: 'bg-purple-50 text-purple-600 border-purple-200 hover:bg-purple-100',
    orange: 'bg-orange-50 text-orange-600 border-orange-200 hover:bg-orange-100'
  }

  return (
    <div className={`transition-all duration-700 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
      <Link href={`/property-types/${propertyType.slug}`} className="block group">
        <div className={`p-8 rounded-xl border-2 transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl ${colorClasses[propertyType.color as keyof typeof colorClasses]}`}>
          {/* Icon */}
          <div className="mb-6">
            <div className="inline-flex p-3 rounded-lg bg-white shadow-sm">
              {propertyType.icon}
            </div>
          </div>

          {/* Content */}
          <h3 className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-current transition-colors">
            {propertyType.name}
          </h3>

          <p className="text-gray-600 mb-6 leading-relaxed">
            {propertyType.description}
          </p>

          {/* Features */}
          <div className="mb-6">
            <div className="grid grid-cols-2 gap-2">
              {propertyType.features.map((feature, idx) => (
                <div key={idx} className="flex items-center gap-2 text-sm text-gray-700">
                  <svg className="w-4 h-4 text-current" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{feature}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Price Range */}
          <div className="flex items-center justify-between">
            <span className="text-lg font-semibold text-gray-900">
              {propertyType.priceRange}
            </span>
            <span className="text-current font-semibold group-hover:translate-x-1 transition-transform">
              Explore →
            </span>
          </div>
        </div>
      </Link>
    </div>
  )
}

const PropertyTypesSection = () => {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            Choose Your Property Type
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            From luxury villas to budget-friendly guesthouses, find the perfect accommodation 
            that matches your lifestyle and budget in Bali.
          </p>
        </div>

        {/* Property Types Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {propertyTypes.map((propertyType, index) => (
            <PropertyTypeCard key={propertyType.id} propertyType={propertyType} index={index} />
          ))}
        </div>

        {/* Additional Info */}
        <div className="bg-gray-50 rounded-2xl p-8 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Not Sure Which Type is Right for You?
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Our property experts can help you find the perfect match based on your budget, 
            lifestyle, and preferences. Get personalized recommendations today.
          </p>
          <Link
            href="/contact"
            className="inline-flex items-center gap-2 bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105"
          >
            Get Expert Advice
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  )
}

export default PropertyTypesSection

import { NextRequest, NextResponse } from 'next/server'
import { getPayloadHMR } from '@payloadcms/next/utilities'
import configPromise from '../../../../../payload.config'
import { v4 as uuidv4 } from 'uuid'

// GET /api/ai/conversations - Retrieve conversations by session
export async function GET(request: NextRequest) {
  try {
    const payload = await getPayloadHMR({ config: configPromise })
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      )
    }

    // Find conversation by session ID
    const conversations = await payload.find({
      collection: 'ai-conversations',
      where: {
        sessionId: {
          equals: sessionId,
        },
        status: {
          not_equals: 'expired',
        },
      },
      limit: 1,
      sort: '-createdAt',
    })

    if (conversations.docs.length === 0) {
      return NextResponse.json(
        { conversation: null, exists: false },
        { status: 200 }
      )
    }

    const conversation = conversations.docs[0]

    // Check if conversation has expired
    const now = new Date()
    const expiresAt = new Date(conversation.expiresAt)
    
    if (now > expiresAt) {
      // Mark as expired
      await payload.update({
        collection: 'ai-conversations',
        id: conversation.id,
        data: {
          status: 'expired',
        },
      })

      return NextResponse.json(
        { conversation: null, exists: false, expired: true },
        { status: 200 }
      )
    }

    return NextResponse.json({
      conversation,
      exists: true,
    })

  } catch (error) {
    console.error('Error retrieving conversation:', error)
    return NextResponse.json(
      { error: 'Failed to retrieve conversation' },
      { status: 500 }
    )
  }
}

// POST /api/ai/conversations - Create new conversation
export async function POST(request: NextRequest) {
  try {
    const payload = await getPayloadHMR({ config: configPromise })
    const body = await request.json()
    
    const {
      sessionId,
      userId,
      initialMessage,
      context,
      userAgent,
    } = body

    // Generate session ID if not provided
    const finalSessionId = sessionId || uuidv4()

    // Create initial messages array
    const messages = []
    if (initialMessage) {
      messages.push({
        id: uuidv4(),
        role: 'user',
        content: initialMessage,
        timestamp: new Date(),
      })
    }

    // Set expiration date (24 hours from now)
    const expiresAt = new Date()
    expiresAt.setHours(expiresAt.getHours() + 24)

    // Create conversation
    const conversation = await payload.create({
      collection: 'ai-conversations',
      data: {
        sessionId: finalSessionId,
        userId,
        messages,
        userPreferences: {
          budget: {},
          propertyTypes: [],
          locations: [],
          lifestyle: [],
          priorities: [],
        },
        context: {
          currentLocation: context?.location,
          currentPropertyType: context?.propertyType,
          referrerPage: context?.page,
          userAgent,
        },
        status: 'active',
        messageCount: messages.length,
        expiresAt,
      },
    })

    return NextResponse.json({
      conversation,
      sessionId: finalSessionId,
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating conversation:', error)
    return NextResponse.json(
      { error: 'Failed to create conversation' },
      { status: 500 }
    )
  }
}

// PUT /api/ai/conversations - Update conversation with new message
export async function PUT(request: NextRequest) {
  try {
    const payload = await getPayloadHMR({ config: configPromise })
    const body = await request.json()
    
    const {
      sessionId,
      message,
      userPreferences,
      context,
    } = body

    if (!sessionId || !message) {
      return NextResponse.json(
        { error: 'Session ID and message are required' },
        { status: 400 }
      )
    }

    // Find existing conversation
    const conversations = await payload.find({
      collection: 'ai-conversations',
      where: {
        sessionId: {
          equals: sessionId,
        },
        status: {
          not_equals: 'expired',
        },
      },
      limit: 1,
    })

    if (conversations.docs.length === 0) {
      return NextResponse.json(
        { error: 'Conversation not found' },
        { status: 404 }
      )
    }

    const conversation = conversations.docs[0]

    // Check if conversation has expired
    const now = new Date()
    const expiresAt = new Date(conversation.expiresAt)
    
    if (now > expiresAt) {
      return NextResponse.json(
        { error: 'Conversation has expired' },
        { status: 410 }
      )
    }

    // Add new message to existing messages
    const newMessage = {
      id: uuidv4(),
      role: message.role,
      content: message.content,
      timestamp: new Date(),
      metadata: message.metadata || {},
    }

    const updatedMessages = [...conversation.messages, newMessage]

    // Prepare update data
    const updateData: any = {
      messages: updatedMessages,
      messageCount: updatedMessages.length,
    }

    // Update preferences if provided
    if (userPreferences) {
      updateData.userPreferences = {
        ...conversation.userPreferences,
        ...userPreferences,
      }
    }

    // Update context if provided
    if (context) {
      updateData.context = {
        ...conversation.context,
        ...context,
      }
    }

    // Update conversation
    const updatedConversation = await payload.update({
      collection: 'ai-conversations',
      id: conversation.id,
      data: updateData,
    })

    return NextResponse.json({
      conversation: updatedConversation,
      message: newMessage,
    })

  } catch (error) {
    console.error('Error updating conversation:', error)
    return NextResponse.json(
      { error: 'Failed to update conversation' },
      { status: 500 }
    )
  }
}

// DELETE /api/ai/conversations - Clear conversation
export async function DELETE(request: NextRequest) {
  try {
    const payload = await getPayloadHMR({ config: configPromise })
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      )
    }

    // Find and delete conversation
    const conversations = await payload.find({
      collection: 'ai-conversations',
      where: {
        sessionId: {
          equals: sessionId,
        },
      },
      limit: 1,
    })

    if (conversations.docs.length > 0) {
      await payload.delete({
        collection: 'ai-conversations',
        id: conversations.docs[0].id,
      })
    }

    return NextResponse.json({
      success: true,
      message: 'Conversation cleared',
    })

  } catch (error) {
    console.error('Error deleting conversation:', error)
    return NextResponse.json(
      { error: 'Failed to delete conversation' },
      { status: 500 }
    )
  }
}

/**
 * Google Analytics 4 Component
 *
 * Comprehensive GA4 tracking with enhanced ecommerce and custom events
 */

'use client'

import Script from 'next/script'
import { usePathname, useSearchParams } from 'next/navigation'
import { useEffect, Suspense } from 'react'

// Google Analytics tracking ID
const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_ID

// Enhanced ecommerce event types
export interface PropertyViewEvent {
  currency: string;
  value: number;
  items: Array<{
    item_id: string;
    item_name: string;
    item_category: string;
    item_variant?: string;
    price: number;
    quantity?: number;
  }>;
}

export interface LeadGenerationEvent {
  currency: string;
  value: number;
  lead_type: 'property_inquiry' | 'contact_form' | 'phone_call' | 'email';
  property_id?: string;
  property_type?: string;
  location?: string;
}

// Track page views
export const pageview = (url: string) => {
  if (typeof window !== 'undefined' && GA_TRACKING_ID) {
    window.gtag('config', GA_TRACKING_ID, {
      page_path: url,
    })
  }
}

// Track custom events
export const event = ({ action, category, label, value }: {
  action: string
  category: string
  label?: string
  value?: number
}) => {
  if (typeof window !== 'undefined' && GA_TRACKING_ID) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    })
  }
}

// Enhanced ecommerce tracking
export const trackPropertyView = (data: PropertyViewEvent) => {
  if (typeof window !== 'undefined' && GA_TRACKING_ID) {
    window.gtag('event', 'view_item', {
      currency: data.currency,
      value: data.value,
      items: data.items,
    })
  }
}

export const trackLeadGeneration = (data: LeadGenerationEvent) => {
  if (typeof window !== 'undefined' && GA_TRACKING_ID) {
    window.gtag('event', 'generate_lead', {
      currency: data.currency,
      value: data.value,
      lead_type: data.lead_type,
      property_id: data.property_id,
      property_type: data.property_type,
      location: data.location,
    })
  }
}

export const trackPropertyInquiry = (propertyId: string, propertyType: string, location: string) => {
  event({
    action: 'property_inquiry',
    category: 'engagement',
    label: `${propertyType} in ${location}`,
    value: 1,
  })

  // Also track as lead generation
  trackLeadGeneration({
    currency: 'USD',
    value: 100, // Estimated lead value
    lead_type: 'property_inquiry',
    property_id: propertyId,
    property_type: propertyType,
    location: location,
  })
}

export const trackContactForm = (formType: string) => {
  event({
    action: 'contact_form_submit',
    category: 'engagement',
    label: formType,
    value: 1,
  })

  trackLeadGeneration({
    currency: 'USD',
    value: 150, // Estimated lead value
    lead_type: 'contact_form',
  })
}

// Track conversions
export const trackConversion = (conversionType: string, value?: number) => {
  event({
    action: 'conversion',
    category: 'engagement',
    label: conversionType,
    value: value,
  })
}

// Internal component that uses useSearchParams
function GoogleAnalyticsInternal() {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    if (GA_TRACKING_ID) {
      const url = pathname + searchParams.toString()
      pageview(url)
    }
  }, [pathname, searchParams])

  return null
}

// Main Google Analytics component
export function GoogleAnalytics() {

  if (!GA_TRACKING_ID) {
    return null
  }

  return (
    <>
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`}
      />
      <Script
        id="google-analytics"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_TRACKING_ID}', {
              page_path: window.location.pathname,
              anonymize_ip: true,
              allow_google_signals: false,
              allow_ad_personalization_signals: false
            });
          `,
        }}
      />
      <Suspense fallback={null}>
        <GoogleAnalyticsInternal />
      </Suspense>
    </>
  )
}

// Extend Window interface for TypeScript
declare global {
  interface Window {
    gtag: (...args: any[]) => void
  }
}

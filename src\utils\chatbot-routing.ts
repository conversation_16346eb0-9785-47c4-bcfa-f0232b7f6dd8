/**
 * Chatbot Routing Utilities
 * Bali Property Scout Website
 * 
 * Utilities for generating chatbot URLs with appropriate query parameters
 * for different contexts (location, property type, service).
 */

import { SITE_CONFIG } from '@/lib/seo';

export interface ChatbotQuery {
  type: 'general' | 'location' | 'property-type' | 'service';
  context?: string;
  location?: string;
  propertyType?: string;
  service?: string;
}

/**
 * Generate chatbot URL with query parameters
 */
export function generateChatbotUrl(query: ChatbotQuery): string {
  const baseUrl = SITE_CONFIG.chatbotUrl;
  const params = new URLSearchParams();

  switch (query.type) {
    case 'general':
      params.set('query', 'general-consultation');
      break;
    
    case 'location':
      if (query.location) {
        params.set('query', `rent-villa-in-${query.location.toLowerCase().replace(/\s+/g, '-')}`);
      } else {
        params.set('query', 'location-search');
      }
      break;
    
    case 'property-type':
      if (query.propertyType && query.location) {
        params.set('query', `${query.propertyType.toLowerCase().replace(/\s+/g, '-')}-in-${query.location.toLowerCase().replace(/\s+/g, '-')}`);
      } else if (query.propertyType) {
        params.set('query', `${query.propertyType.toLowerCase().replace(/\s+/g, '-')}-search`);
      } else {
        params.set('query', 'property-search');
      }
      break;
    
    case 'service':
      if (query.service) {
        params.set('query', `${query.service.toLowerCase().replace(/\s+/g, '-')}-consultation`);
      } else {
        params.set('query', 'service-consultation');
      }
      break;
    
    default:
      params.set('query', 'general-consultation');
  }

  return `${baseUrl}?${params.toString()}`;
}

/**
 * Predefined chatbot routes for common use cases
 */
export const CHATBOT_ROUTES = {
  // General routes
  general: () => generateChatbotUrl({ type: 'general' }),
  
  // Location-specific routes
  locations: {
    canggu: () => generateChatbotUrl({ type: 'location', location: 'Canggu' }),
    seminyak: () => generateChatbotUrl({ type: 'location', location: 'Seminyak' }),
    ubud: () => generateChatbotUrl({ type: 'location', location: 'Ubud' }),
    sanur: () => generateChatbotUrl({ type: 'location', location: 'Sanur' }),
    jimbaran: () => generateChatbotUrl({ type: 'location', location: 'Jimbaran' }),
  },
  
  // Property type routes
  propertyTypes: {
    villa: (location?: string) => generateChatbotUrl({ 
      type: 'property-type', 
      propertyType: 'Villa',
      location 
    }),
    guesthouse: (location?: string) => generateChatbotUrl({ 
      type: 'property-type', 
      propertyType: 'Guesthouse',
      location 
    }),
  },
  
  // Service routes
  services: {
    investment: () => generateChatbotUrl({ type: 'service', service: 'Investment' }),
    purchase: () => generateChatbotUrl({ type: 'service', service: 'Purchase Assistance' }),
    consultation: () => generateChatbotUrl({ type: 'service', service: 'Consultation' }),
    viewing: () => generateChatbotUrl({ type: 'service', service: 'Viewing' }),
  },
} as const;

/**
 * Handle chatbot navigation with fallback
 */
export function navigateToChatbot(query: ChatbotQuery, fallbackUrl: string = '/contact'): void {
  try {
    const chatbotUrl = generateChatbotUrl(query);
    
    // Open in same tab for seamless UX
    window.location.href = chatbotUrl;
  } catch (error) {
    console.error('Failed to navigate to chatbot:', error);
    
    // Fallback to contact page
    window.location.href = fallbackUrl;
  }
}

/**
 * React hook for chatbot navigation
 */
export function useChatbotNavigation() {
  const navigateToGeneral = () => navigateToChatbot({ type: 'general' });
  
  const navigateToLocation = (location: string) => 
    navigateToChatbot({ type: 'location', location });
  
  const navigateToPropertyType = (propertyType: string, location?: string) => 
    navigateToChatbot({ type: 'property-type', propertyType, location });
  
  const navigateToService = (service: string) => 
    navigateToChatbot({ type: 'service', service });

  return {
    navigateToGeneral,
    navigateToLocation,
    navigateToPropertyType,
    navigateToService,
    generateUrl: generateChatbotUrl,
  };
}

export default {
  generateChatbotUrl,
  CHATBOT_ROUTES,
  navigateToChatbot,
  useChatbotNavigation,
};

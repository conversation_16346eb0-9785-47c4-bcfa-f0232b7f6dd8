/**
 * Design System - Color Palette
 * Bali Property Scout Website
 *
 * This file defines the complete color system for the website,
 * including semantic color tokens and theme variations.
 */

export const colors = {
  // Primary Brand Colors - Emerald (PRD v3)
  primary: {
    50: '#ecfdf5',   // Emerald lightest
    100: '#d1fae5',  // Emerald lighter
    200: '#a7f3d0',  // Emerald light
    300: '#6ee7b7',  // Emerald light-medium
    400: '#34d399',  // Emerald medium
    500: '#10b981',  // Emerald (main brand)
    600: '#059669',  // Emerald dark
    700: '#047857',  // Emerald darker
    800: '#065f46',  // Emerald very dark
    900: '#064e3b',  // Emerald darkest
    950: '#022c22',  // Almost black emerald
    // Semantic naming
    DEFAULT: '#10b981',
    foreground: '#ffffff',
    light: '#34d399',
    lighter: '#a7f3d0',
    lightest: '#ecfdf5',
    dark: '#047857',
    darker: '#065f46',
  },

  // Secondary Colors - Sand (PRD v3)
  secondary: {
    50: '#fefdfb',   // Sand lightest
    100: '#fef7ed',  // Sand lighter
    200: '#fed7aa',  // Sand light
    300: '#fdba74',  // Sand light-medium
    400: '#fb923c',  // Sand medium
    500: '#f97316',  // Sand (main secondary)
    600: '#ea580c',  // Sand dark
    700: '#c2410c',  // Sand darker
    800: '#9a3412',  // Sand very dark
    900: '#7c2d12',  // Sand darkest
    950: '#431407',  // Almost black sand
  },

  // Accent Colors - Sunset (PRD v3)
  accent: {
    50: '#fff7ed',   // Sunset lightest
    100: '#ffedd5',  // Sunset lighter
    200: '#fed7aa',  // Sunset light
    300: '#fdba74',  // Sunset light-medium
    400: '#fb923c',  // Sunset medium
    500: '#f97316',  // Sunset (main accent)
    600: '#ea580c',  // Sunset dark
    700: '#c2410c',  // Sunset darker
    800: '#9a3412',  // Sunset very dark
    900: '#7c2d12',  // Sunset darkest
    950: '#431407',  // Almost black sunset
  },

  // Neutral Colors - Professional Grays
  neutral: {
    50: '#fafafa',   // Almost white
    100: '#f5f5f5',  // Very light gray
    200: '#e5e5e5',  // Light gray
    300: '#d4d4d4',  // Lighter gray
    400: '#a3a3a3',  // Medium light gray
    500: '#737373',  // Medium gray
    600: '#525252',  // Medium dark gray
    700: '#404040',  // Dark gray
    800: '#262626',  // Very dark gray
    900: '#171717',  // Almost black
    950: '#0a0a0a',  // Black
  },

  // Semantic Colors
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    500: '#22c55e',  // Success green
    600: '#16a34a',
    700: '#15803d',
  },

  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    500: '#f59e0b',  // Warning orange
    600: '#d97706',
    700: '#b45309',
  },

  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    500: '#ef4444',  // Error red
    600: '#dc2626',
    700: '#b91c1c',
  },

  info: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    500: '#0ea5e9',  // Info blue
    600: '#0284c7',
    700: '#0369a1',
  },

  // Special Colors
  white: '#ffffff',
  black: '#000000',
  transparent: 'transparent',
  current: 'currentColor',
} as const;

// Semantic Color Mappings
export const semanticColors = {
  // Background colors
  background: {
    primary: colors.white,
    secondary: colors.neutral[50],
    tertiary: colors.neutral[100],
    inverse: colors.neutral[900],
  },

  // Text colors
  text: {
    primary: colors.neutral[900],
    secondary: colors.neutral[700],
    tertiary: colors.neutral[500],
    inverse: colors.white,
    muted: colors.neutral[400],
  },

  // Border colors
  border: {
    primary: colors.neutral[200],
    secondary: colors.neutral[300],
    focus: colors.primary[500],
    error: colors.error[500],
  },

  // Interactive colors
  interactive: {
    primary: colors.primary[500],
    primaryHover: colors.primary[600],
    primaryActive: colors.primary[700],
    secondary: colors.secondary[500],
    secondaryHover: colors.secondary[600],
    accent: colors.accent[500],
    accentHover: colors.accent[600],
  },

  // Status colors
  status: {
    success: colors.success[500],
    warning: colors.warning[500],
    error: colors.error[500],
    info: colors.info[500],
  },
} as const;

// Color utility functions
export const colorUtils = {
  /**
   * Get color with opacity
   */
  withOpacity: (color: string, opacity: number) => {
    return `${color}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`;
  },

  /**
   * Get contrasting text color for a background
   */
  getContrastText: (backgroundColor: string) => {
    // Simple contrast logic - in production, use a proper contrast calculation
    const darkColors = [
      colors.primary[600], colors.primary[700], colors.primary[800], colors.primary[900],
      colors.secondary[600], colors.secondary[700], colors.secondary[800], colors.secondary[900],
      colors.neutral[600], colors.neutral[700], colors.neutral[800], colors.neutral[900],
    ];
    
    return darkColors.includes(backgroundColor) ? colors.white : colors.neutral[900];
  },
} as const;

// Export color tokens for Tailwind CSS configuration
export const tailwindColors = {
  primary: colors.primary,
  secondary: colors.secondary,
  accent: colors.accent,
  neutral: colors.neutral,
  success: colors.success,
  warning: colors.warning,
  error: colors.error,
  info: colors.info,
} as const;

export type ColorScale = typeof colors.primary;
export type SemanticColor = keyof typeof semanticColors;
export type ColorToken = keyof typeof colors;

/**
 * Lazy-loaded Contact Form Component
 * 
 * This component dynamically imports the ContactForm to reduce initial bundle size
 */

'use client';

import { Suspense, lazy } from 'react';

// Lazy load the ContactForm component
const ContactForm = lazy(() => import('./ContactForm').then(module => ({ default: module.default })));

// Loading component for ContactForm
function ContactFormLoading() {
  return (
    <div className="w-full max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-sm border">
      <div className="animate-pulse">
        {/* Form title skeleton */}
        <div className="h-8 bg-gray-200 rounded mb-6"></div>
        
        {/* Form fields skeleton */}
        <div className="space-y-4">
          <div className="h-12 bg-gray-200 rounded"></div>
          <div className="h-12 bg-gray-200 rounded"></div>
          <div className="h-12 bg-gray-200 rounded"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
          <div className="h-12 bg-gray-200 rounded w-32"></div>
        </div>
      </div>
    </div>
  );
}

// Lazy ContactForm wrapper component
export function LazyContactForm(props: any) {
  return (
    <Suspense fallback={<ContactFormLoading />}>
      <ContactForm {...props} />
    </Suspense>
  );
}

export default LazyContactForm;

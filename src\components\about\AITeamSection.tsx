/**
 * AI-Enhanced Team Section Component
 * Bali Property Scout Website
 * 
 * Team section showcasing AI Property Matcher, Local Experts, and Personal Concierge
 * with photos/avatars, role descriptions, and hover micro-interactions.
 */

'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import ChatbotCTA from '@/components/ui/ChatbotCTA';

export interface TeamMember {
  id: string;
  name: string;
  role: string;
  description: string;
  expertise: string[];
  avatar: string;
  color: string;
  icon: React.ReactNode;
  stats?: {
    label: string;
    value: string;
  };
}

export interface AITeamSectionProps {
  className?: string;
}

const teamMembers: TeamMember[] = [
  {
    id: 'ai-matcher',
    name: 'AI Property Matcher',
    role: 'Intelligent Property Discovery',
    description: 'Our AI system analyzes your preferences, budget, and lifestyle to match you with perfect properties in seconds. Learns from 500+ successful transactions to provide personalized recommendations.',
    expertise: ['Property Matching', 'Preference Learning', 'Market Analysis', 'Instant Recommendations'],
    avatar: '/images/team/ai-property-matcher.jpg',
    color: 'from-blue-500 to-purple-600',
    stats: {
      label: 'Match Accuracy',
      value: '95%'
    },
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
      </svg>
    )
  },
  {
    id: 'local-experts',
    name: 'Local Experts',
    role: 'Bali Market Specialists',
    description: 'Indonesian and international team members with deep local knowledge. We understand Bali\'s unique property market, legal requirements, and cultural nuances to guide you safely.',
    expertise: ['Local Market Knowledge', 'Legal Compliance', 'Cultural Insights', 'Negotiation'],
    avatar: '/images/team/local-experts.jpg',
    color: 'from-emerald-500 to-teal-600',
    stats: {
      label: 'Years Experience',
      value: '5+'
    },
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
      </svg>
    )
  },
  {
    id: 'personal-concierge',
    name: 'Personal Concierge',
    role: 'End-to-End Support',
    description: 'From first contact to keys in hand, our personal concierge team provides white-glove service. Available 24/7 to answer questions, arrange viewings, and support your property journey.',
    expertise: ['Personal Support', 'Viewing Coordination', '24/7 Availability', 'After-Sale Care'],
    avatar: '/images/team/personal-concierge.jpg',
    color: 'from-orange-500 to-red-600',
    stats: {
      label: 'Response Time',
      value: '<2min'
    },
    icon: (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
      </svg>
    )
  }
];

export const AITeamSection: React.FC<AITeamSectionProps> = ({ className }) => {
  const [hoveredMember, setHoveredMember] = useState<string | null>(null);

  return (
    <section className={cn('py-20 bg-gradient-to-br from-gray-50 to-white', className)}>
      <div className="max-w-6xl mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Meet Your AI-Enhanced Team
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            The perfect blend of artificial intelligence and human expertise. 
            Our team combines cutting-edge AI technology with deep local knowledge to deliver exceptional results.
          </p>
        </div>

        {/* Team Members Grid */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {teamMembers.map((member) => (
            <div
              key={member.id}
              className={cn(
                'relative group cursor-pointer transition-all duration-500 ease-out',
                'hover:scale-105 hover:-translate-y-2'
              )}
              onMouseEnter={() => setHoveredMember(member.id)}
              onMouseLeave={() => setHoveredMember(null)}
            >
              {/* Card */}
              <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden h-full">
                {/* Avatar Section */}
                <div className="relative h-48 overflow-hidden">
                  <div className={cn('absolute inset-0 bg-gradient-to-br', member.color)} />
                  
                  {/* Avatar Image */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-24 h-24 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center border-4 border-white/30">
                      <div className="text-white">
                        {member.icon}
                      </div>
                    </div>
                  </div>

                  {/* Floating Stats */}
                  {member.stats && (
                    <div className="absolute top-4 right-4">
                      <div className="bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 text-center">
                        <div className="text-lg font-bold text-gray-900">{member.stats.value}</div>
                        <div className="text-xs text-gray-600">{member.stats.label}</div>
                      </div>
                    </div>
                  )}

                  {/* Hover Overlay */}
                  <div className={cn(
                    'absolute inset-0 bg-black/20 transition-opacity duration-300',
                    hoveredMember === member.id ? 'opacity-100' : 'opacity-0'
                  )} />
                </div>

                {/* Content */}
                <div className="p-6 space-y-4">
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-1">
                      {member.name}
                    </h3>
                    <p className="text-sm font-medium text-gray-500 mb-3">
                      {member.role}
                    </p>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {member.description}
                    </p>
                  </div>

                  {/* Expertise Tags */}
                  <div>
                    <h4 className="text-sm font-semibold text-gray-800 mb-2">Expertise:</h4>
                    <div className="flex flex-wrap gap-2">
                      {member.expertise.map((skill, index) => (
                        <span
                          key={index}
                          className={cn(
                            'px-2 py-1 rounded-md text-xs font-medium transition-all duration-300',
                            'bg-gray-100 text-gray-700',
                            hoveredMember === member.id && 'bg-gradient-to-r text-white',
                            hoveredMember === member.id && member.color
                          )}
                        >
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Hover Glow Effect */}
                <div className={cn(
                  'absolute inset-0 rounded-2xl transition-all duration-300 pointer-events-none',
                  hoveredMember === member.id 
                    ? 'shadow-2xl ring-2 ring-blue-200' 
                    : 'shadow-lg'
                )} />
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Experience AI-Powered Property Search?
            </h3>
            <p className="text-gray-600 mb-6 leading-relaxed">
              Our AI-enhanced team is ready to help you find your perfect property in Bali. 
              Start a conversation and experience the future of real estate.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <ChatbotCTA
                query={{ 
                  type: 'team-introduction', 
                  context: 'about-page-team-section'
                }}
                variant="primary"
                size="lg"
                className="btn-primary text-lg font-semibold px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
              >
                Meet Your AI Team
              </ChatbotCTA>
              
              <button
                onClick={() => {
                  const servicesSection = document.getElementById('services');
                  if (servicesSection) {
                    servicesSection.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
                className="btn-secondary text-base font-medium px-6 py-3 border-2 border-gray-300 text-gray-700 hover:border-gray-400 hover:text-gray-900 transition-all duration-300"
              >
                Learn About Our Services
              </button>
            </div>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="mt-16 text-center">
          <div className="flex flex-wrap justify-center items-center gap-8 text-gray-500">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm font-medium">AI-Powered Matching</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-sm font-medium">Local Expertise</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span className="text-sm font-medium">24/7 Support</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              <span className="text-sm font-medium">500+ Happy Clients</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AITeamSection;

/**
 * Cache System Tests
 * 
 * Unit and integration tests for the multi-layer caching system
 */

import { cache, cacheKeys, CACHE_CONFIG } from '@/lib/cache';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn(),
};
Object.defineProperty(window, 'localStorage', { value: localStorageMock });

describe('Cache System', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    cache.clear();
  });

  describe('Memory Cache', () => {
    it('stores and retrieves data from memory cache', async () => {
      const testData = { id: 1, name: 'Test Property' };
      const key = 'test-property';
      
      cache.set(key, testData);
      const result = await cache.get(key);
      
      expect(result).toEqual(testData);
    });

    it('returns null for non-existent keys', async () => {
      const result = await cache.get('non-existent-key');
      expect(result).toBeNull();
    });

    it('respects TTL for memory cache', async () => {
      const testData = { id: 1, name: 'Test Property' };
      const key = 'test-property';
      const shortTTL = 100; // 100ms
      
      cache.set(key, testData, shortTTL);
      
      // Should be available immediately
      let result = await cache.get(key);
      expect(result).toEqual(testData);
      
      // Wait for TTL to expire
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // Should be null after TTL
      result = await cache.get(key);
      expect(result).toBeNull();
    });

    it('updates access statistics on get', async () => {
      const testData = { id: 1, name: 'Test Property' };
      const key = 'test-property';
      
      cache.set(key, testData);
      
      // Access multiple times
      await cache.get(key);
      await cache.get(key);
      await cache.get(key);
      
      const stats = cache.getStats();
      expect(stats.memory.size).toBe(1);
    });
  });

  describe('LocalStorage Cache', () => {
    it('falls back to localStorage when memory cache misses', async () => {
      const testData = { id: 1, name: 'Test Property' };
      const key = 'test-property';
      const cacheEntry = {
        data: testData,
        timestamp: Date.now(),
        ttl: CACHE_CONFIG.localStorage.maxAge,
        size: 0,
        hits: 0,
        lastAccessed: Date.now(),
      };
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(cacheEntry));
      
      const result = await cache.get(key);
      
      expect(localStorageMock.getItem).toHaveBeenCalledWith(
        CACHE_CONFIG.localStorage.keyPrefix + key
      );
      expect(result).toEqual(testData);
    });

    it('promotes localStorage data to memory cache', async () => {
      const testData = { id: 1, name: 'Test Property' };
      const key = 'test-property';
      const cacheEntry = {
        data: testData,
        timestamp: Date.now(),
        ttl: CACHE_CONFIG.localStorage.maxAge,
        size: 0,
        hits: 0,
        lastAccessed: Date.now(),
      };
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(cacheEntry));
      
      // First get should promote to memory
      await cache.get(key);
      
      // Second get should come from memory (no localStorage call)
      localStorageMock.getItem.mockClear();
      const result = await cache.get(key);
      
      expect(localStorageMock.getItem).not.toHaveBeenCalled();
      expect(result).toEqual(testData);
    });

    it('handles localStorage errors gracefully', async () => {
      const key = 'test-property';
      
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('localStorage error');
      });
      
      const result = await cache.get(key);
      expect(result).toBeNull();
    });

    it('handles invalid JSON in localStorage', async () => {
      const key = 'test-property';
      
      localStorageMock.getItem.mockReturnValue('invalid json');
      
      const result = await cache.get(key);
      expect(result).toBeNull();
    });
  });

  describe('Fallback Strategy', () => {
    it('uses fallback function when cache misses', async () => {
      const testData = { id: 1, name: 'Test Property' };
      const key = 'test-property';
      const fallback = jest.fn().mockResolvedValue(testData);
      
      const result = await cache.get(key, fallback);
      
      expect(fallback).toHaveBeenCalled();
      expect(result).toEqual(testData);
    });

    it('caches fallback result', async () => {
      const testData = { id: 1, name: 'Test Property' };
      const key = 'test-property';
      const fallback = jest.fn().mockResolvedValue(testData);
      
      // First call should use fallback
      await cache.get(key, fallback);
      expect(fallback).toHaveBeenCalledTimes(1);
      
      // Second call should use cache
      const result = await cache.get(key, fallback);
      expect(fallback).toHaveBeenCalledTimes(1);
      expect(result).toEqual(testData);
    });

    it('handles fallback errors gracefully', async () => {
      const key = 'test-property';
      const fallback = jest.fn().mockRejectedValue(new Error('Fallback error'));
      
      const result = await cache.get(key, fallback);
      
      expect(fallback).toHaveBeenCalled();
      expect(result).toBeNull();
    });
  });

  describe('Cache Management', () => {
    it('deletes entries from both caches', () => {
      const testData = { id: 1, name: 'Test Property' };
      const key = 'test-property';
      
      cache.set(key, testData);
      cache.delete(key);
      
      expect(cache.has(key)).toBe(false);
      expect(localStorageMock.removeItem).toHaveBeenCalledWith(
        CACHE_CONFIG.localStorage.keyPrefix + key
      );
    });

    it('clears both caches', () => {
      const testData1 = { id: 1, name: 'Test Property 1' };
      const testData2 = { id: 2, name: 'Test Property 2' };
      
      cache.set('key1', testData1);
      cache.set('key2', testData2);
      
      cache.clear();
      
      expect(cache.has('key1')).toBe(false);
      expect(cache.has('key2')).toBe(false);
    });

    it('provides cache statistics', () => {
      const testData = { id: 1, name: 'Test Property' };
      cache.set('test-key', testData);
      
      const stats = cache.getStats();
      
      expect(stats).toHaveProperty('memory');
      expect(stats.memory).toHaveProperty('size');
      expect(stats.memory).toHaveProperty('currentSize');
      expect(stats.memory).toHaveProperty('maxSize');
      expect(stats.memory).toHaveProperty('utilization');
    });
  });

  describe('Cache Key Generation', () => {
    it('generates location cache keys', () => {
      const key = cacheKeys.location('canggu');
      expect(key).toBe('location:canggu');
    });

    it('generates property cache keys', () => {
      const key = cacheKeys.property('123');
      expect(key).toBe('property:123');
    });

    it('generates search cache keys', () => {
      const key = cacheKeys.search('villa canggu');
      expect(key).toBe('search:villa canggu');
    });

    it('generates API cache keys with parameters', () => {
      const key = cacheKeys.api('/properties', { location: 'canggu', type: 'villa' });
      expect(key).toBe('api:/properties:{"location":"canggu","type":"villa"}');
    });

    it('generates API cache keys without parameters', () => {
      const key = cacheKeys.api('/locations');
      expect(key).toBe('api:/locations');
    });
  });

  describe('Memory Management', () => {
    it('enforces memory size limits', () => {
      // Create large data that exceeds memory limit
      const largeData = 'x'.repeat(CACHE_CONFIG.memory.maxSize / 2);
      
      cache.set('key1', largeData);
      cache.set('key2', largeData);
      cache.set('key3', largeData); // This should trigger cleanup
      
      const stats = cache.getStats();
      expect(stats.memory.currentSize).toBeLessThanOrEqual(CACHE_CONFIG.memory.maxSize);
    });

    it('uses LRU eviction strategy', async () => {
      const data1 = 'x'.repeat(CACHE_CONFIG.memory.maxSize / 3);
      const data2 = 'y'.repeat(CACHE_CONFIG.memory.maxSize / 3);
      const data3 = 'z'.repeat(CACHE_CONFIG.memory.maxSize / 3);
      const data4 = 'w'.repeat(CACHE_CONFIG.memory.maxSize / 3);
      
      cache.set('key1', data1);
      cache.set('key2', data2);
      cache.set('key3', data3);
      
      // Access key1 to make it recently used
      await cache.get('key1');
      
      // Add key4, which should evict key2 (least recently used)
      cache.set('key4', data4);
      
      expect(cache.has('key1')).toBe(true); // Recently accessed
      expect(cache.has('key2')).toBe(false); // Should be evicted
      expect(cache.has('key3')).toBe(true); // Still fits
      expect(cache.has('key4')).toBe(true); // Newly added
    });
  });

  describe('Performance', () => {
    it('handles concurrent cache operations', async () => {
      const promises = [];
      
      for (let i = 0; i < 100; i++) {
        promises.push(cache.set(`key${i}`, { id: i, name: `Item ${i}` }));
      }
      
      await Promise.all(promises);
      
      const getPromises = [];
      for (let i = 0; i < 100; i++) {
        getPromises.push(cache.get(`key${i}`));
      }
      
      const results = await Promise.all(getPromises);
      
      expect(results).toHaveLength(100);
      expect(results[0]).toEqual({ id: 0, name: 'Item 0' });
      expect(results[99]).toEqual({ id: 99, name: 'Item 99' });
    });

    it('performs cache operations efficiently', async () => {
      const startTime = performance.now();
      
      // Perform many cache operations
      for (let i = 0; i < 1000; i++) {
        cache.set(`key${i}`, { id: i });
        await cache.get(`key${i}`);
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should complete within reasonable time (adjust threshold as needed)
      expect(duration).toBeLessThan(1000); // 1 second
    });
  });
});

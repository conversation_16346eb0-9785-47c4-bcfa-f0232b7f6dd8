'use client'

import Image from 'next/image'
import Link from 'next/link'
import { useState, useEffect } from 'react'

// Mock data - will be replaced with CMS data
const recentGuides = [
  {
    id: 1,
    title: 'Complete Guide to Buying Property in Bali as a Foreigner',
    slug: 'buying-property-bali-foreigner-guide',
    excerpt: 'Everything you need to know about freehold vs leasehold, legal requirements, and the step-by-step purchase process for international buyers.',
    category: 'Legal & Regulatory',
    publishDate: '2025-08-15',
    readTime: 12,
    author: 'Legal Team',
    image: '/images/guides/buying-property-guide.jpg',
    featured: true
  },
  {
    id: 2,
    title: 'Canggu vs Ubud vs Seminyak: Which Area is Right for You?',
    slug: 'canggu-ubud-seminyak-comparison',
    excerpt: 'Detailed comparison of Bali\'s most popular expat destinations, covering lifestyle, costs, amenities, and community for each area.',
    category: 'Location Guide',
    publishDate: '2025-08-12',
    readTime: 8,
    author: 'Location Expert',
    image: '/images/guides/location-comparison.jpg',
    featured: false
  },
  {
    id: 3,
    title: 'Long-term Rental Costs in Bali: 2025 Market Analysis',
    slug: 'bali-rental-costs-2025-analysis',
    excerpt: 'Comprehensive breakdown of rental prices across different areas and property types, plus tips for negotiating better deals.',
    category: 'Market Analysis',
    publishDate: '2025-08-10',
    readTime: 6,
    author: 'Market Analyst',
    image: '/images/guides/rental-costs-analysis.jpg',
    featured: false
  },
  {
    id: 4,
    title: 'Digital Nomad\'s Guide to Setting Up in Bali',
    slug: 'digital-nomad-setup-guide-bali',
    excerpt: 'From visa requirements to finding the perfect coworking space, everything remote workers need to know about relocating to Bali.',
    category: 'Lifestyle',
    publishDate: '2025-08-08',
    readTime: 10,
    author: 'Nomad Expert',
    image: '/images/guides/digital-nomad-guide.jpg',
    featured: false
  }
]

const GuideCard = ({ guide, index, featured = false }: { guide: typeof recentGuides[0], index: number, featured?: boolean }) => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), index * 150)
    return () => clearTimeout(timer)
  }, [index])

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    })
  }

  if (featured) {
    return (
      <div className={`transition-all duration-700 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
        <Link href={`/guides/${guide.slug}`} className="block group">
          <div className="bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden">
            {/* Featured Badge */}
            <div className="absolute top-4 left-4 z-10 bg-emerald-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
              Featured Guide
            </div>
            
            {/* Image */}
            <div className="relative h-64 overflow-hidden">
              <Image
                src={guide.image}
                alt={guide.title}
                fill
                className="object-cover group-hover:scale-110 transition-transform duration-500"
                placeholder="blur"
                blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
              />
            </div>

            {/* Content */}
            <div className="p-8">
              <div className="flex items-center gap-4 mb-4 text-sm text-gray-500">
                <span className="bg-emerald-100 text-emerald-700 px-2 py-1 rounded-full font-medium">
                  {guide.category}
                </span>
                <span>{formatDate(guide.publishDate)}</span>
                <span>{guide.readTime} min read</span>
              </div>

              <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-emerald-600 transition-colors leading-tight">
                {guide.title}
              </h3>

              <p className="text-gray-600 mb-6 leading-relaxed">
                {guide.excerpt}
              </p>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">By {guide.author}</span>
                <span className="text-emerald-600 font-semibold group-hover:text-emerald-700 transition-colors">
                  Read More →
                </span>
              </div>
            </div>
          </div>
        </Link>
      </div>
    )
  }

  return (
    <div className={`transition-all duration-700 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
      <Link href={`/guides/${guide.slug}`} className="block group">
        <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 overflow-hidden">
          {/* Image */}
          <div className="relative h-48 overflow-hidden">
            <Image
              src={guide.image}
              alt={guide.title}
              fill
              className="object-cover group-hover:scale-110 transition-transform duration-500"
              placeholder="blur"
              blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
            />
          </div>

          {/* Content */}
          <div className="p-6">
            <div className="flex items-center gap-3 mb-3 text-sm text-gray-500">
              <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full font-medium">
                {guide.category}
              </span>
              <span>{guide.readTime} min read</span>
            </div>

            <h3 className="text-lg font-bold text-gray-900 mb-3 group-hover:text-emerald-600 transition-colors leading-tight">
              {guide.title}
            </h3>

            <p className="text-gray-600 text-sm mb-4 leading-relaxed line-clamp-3">
              {guide.excerpt}
            </p>

            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-500">{formatDate(guide.publishDate)}</span>
              <span className="text-emerald-600 font-semibold group-hover:text-emerald-700 transition-colors">
                Read More →
              </span>
            </div>
          </div>
        </div>
      </Link>
    </div>
  )
}

const RecentGuides = () => {
  const featuredGuide = recentGuides.find(guide => guide.featured)
  const regularGuides = recentGuides.filter(guide => !guide.featured)

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            Latest Guides & Insights
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Stay informed with our expert guides covering everything from legal requirements 
            to lifestyle tips for living and investing in Bali.
          </p>
        </div>

        {/* Featured Guide */}
        {featuredGuide && (
          <div className="mb-16">
            <GuideCard guide={featuredGuide} index={0} featured={true} />
          </div>
        )}

        {/* Regular Guides Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {regularGuides.map((guide, index) => (
            <GuideCard key={guide.id} guide={guide} index={index + 1} />
          ))}
        </div>

        {/* View All CTA */}
        <div className="text-center">
          <Link
            href="/guides"
            className="inline-flex items-center gap-2 bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
          >
            View All Guides
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>

        {/* Newsletter Signup */}
        <div className="mt-16 bg-white rounded-2xl p-8 shadow-lg text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Stay Updated with Bali Property Insights
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Get the latest market updates, new property listings, and expert tips 
            delivered directly to your inbox. No spam, just valuable insights.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email address"
              className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 outline-none"
            />
            <button className="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 whitespace-nowrap">
              Subscribe
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-3">
            By subscribing, you agree to our Privacy Policy and Terms of Service.
          </p>
        </div>
      </div>
    </section>
  )
}

export default RecentGuides

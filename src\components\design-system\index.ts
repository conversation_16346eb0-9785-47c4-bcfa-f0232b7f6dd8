/**
 * Design System - Main Export
 * Bali Real Estate Website
 * 
 * This file exports all design system tokens and utilities
 * for use throughout the application.
 */

// Export all color tokens and utilities
export {
  colors,
  semanticColors,
  colorUtils,
  tailwindColors,
  type ColorScale,
  type SemanticColor,
  type ColorToken,
} from './colors';

// Export all typography tokens and utilities
export {
  fontFamilies,
  fontWeights,
  fontSizes,
  letterSpacing,
  textStyles,
  responsiveText,
  typographyUtils,
  tailwindTypography,
  type FontSize,
  type FontWeight,
  type TextStyle,
} from './typography';

// Export all spacing tokens and utilities
export {
  spacing,
  semanticSpacing,
  responsiveSpacing,
  dimensions,
  borderRadius,
  shadows,
  zIndex,
  spacingUtils,
  tailwindSpacing,
  type SpacingKey,
  type SemanticSpacingCategory,
  type ResponsiveBreakpoint,
} from './spacing';

// Export all breakpoint tokens and utilities
export {
  breakpoints,
  breakpointRanges,
  deviceCategories,
  containerMaxWidths,
  gridColumns,
  mediaQueries,
  responsiveUtils,
  responsiveProps,
  tailwindBreakpoints,
  breakpointHooks,
  type Breakpoint,
  type DeviceCategory,
  type ResponsiveValue,
} from './breakpoints';

// Design system configuration
export const designSystem = {
  // Version
  version: '1.0.0',
  
  // Theme configuration
  theme: {
    name: 'Bali Property Scout',
    description: 'AI-powered tropical design system for property search',
    primaryColor: '#22c55e', // Green
    secondaryColor: '#0ea5e9', // Blue
    accentColor: '#f59e0b', // Orange
  },
  
  // Component defaults
  defaults: {
    borderRadius: '4px',
    fontSize: '16px',
    fontFamily: 'Inter, sans-serif',
    lineHeight: '1.5',
    spacing: '16px',
    transition: '150ms ease-in-out',
  },
  
  // Animation settings
  animation: {
    duration: {
      fast: '150ms',
      normal: '250ms',
      slow: '350ms',
    },
    easing: {
      ease: 'ease',
      easeIn: 'ease-in',
      easeOut: 'ease-out',
      easeInOut: 'ease-in-out',
    },
  },
} as const;

// CSS Custom Properties generator
export const generateCSSCustomProperties = () => {
  const cssVars: Record<string, string> = {};
  
  // Add color variables
  Object.entries(colors).forEach(([colorName, colorScale]) => {
    if (typeof colorScale === 'object') {
      Object.entries(colorScale).forEach(([shade, value]) => {
        cssVars[`--color-${colorName}-${shade}`] = value;
      });
    } else {
      cssVars[`--color-${colorName}`] = colorScale;
    }
  });
  
  // Add spacing variables
  Object.entries(spacing).forEach(([key, value]) => {
    cssVars[`--spacing-${key}`] = value;
  });
  
  // Add font size variables
  Object.entries(fontSizes).forEach(([key, value]) => {
    cssVars[`--font-size-${key}`] = value.fontSize;
    cssVars[`--line-height-${key}`] = value.lineHeight;
  });
  
  // Add border radius variables
  Object.entries(borderRadius).forEach(([key, value]) => {
    cssVars[`--border-radius-${key}`] = value;
  });
  
  // Add shadow variables
  Object.entries(shadows).forEach(([key, value]) => {
    cssVars[`--shadow-${key}`] = value;
  });
  
  return cssVars;
};

// Tailwind CSS configuration helper
export const getTailwindConfig = () => {
  return {
    theme: {
      extend: {
        colors: tailwindColors,
        fontFamily: fontFamilies,
        fontSize: tailwindTypography.fontSize,
        fontWeight: tailwindTypography.fontWeight,
        letterSpacing: tailwindTypography.letterSpacing,
        spacing: tailwindSpacing.spacing,
        borderRadius: tailwindSpacing.borderRadius,
        boxShadow: tailwindSpacing.boxShadow,
        zIndex: tailwindSpacing.zIndex,
        maxWidth: tailwindSpacing.maxWidth,
        screens: tailwindBreakpoints.screens,
      },
    },
  };
};

// Component variant helpers
export const createVariants = <T extends Record<string, any>>(variants: T) => {
  return variants;
};

// Responsive helper for components
export const responsive = <T>(values: Partial<Record<Breakpoint, T>>) => {
  return values;
};

// Design token validation
export const validateDesignTokens = () => {
  const errors: string[] = [];
  
  // Validate color contrast ratios (simplified)
  // In a real implementation, you'd use a proper contrast calculation library
  
  // Validate spacing scale consistency
  const spacingValues = Object.values(spacing).map(v => parseInt(v));
  const hasConsistentScale = spacingValues.every((value, index) => {
    if (index === 0) return true;
    return value >= spacingValues[index - 1];
  });
  
  if (!hasConsistentScale) {
    errors.push('Spacing scale is not consistent');
  }
  
  // Validate font size scale
  const fontSizeValues = Object.values(fontSizes).map(v => parseFloat(v.fontSize));
  const hasConsistentFontScale = fontSizeValues.every((value, index) => {
    if (index === 0) return true;
    return value >= fontSizeValues[index - 1];
  });
  
  if (!hasConsistentFontScale) {
    errors.push('Font size scale is not consistent');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Design system utilities
export const designSystemUtils = {
  generateCSSCustomProperties,
  getTailwindConfig,
  createVariants,
  responsive,
  validateDesignTokens,
} as const;

// Export everything as default for convenience
export default {
  colors,
  semanticColors,
  fontFamilies,
  fontSizes,
  textStyles,
  spacing,
  semanticSpacing,
  breakpoints,
  mediaQueries,
  designSystem,
  utils: designSystemUtils,
} as const;

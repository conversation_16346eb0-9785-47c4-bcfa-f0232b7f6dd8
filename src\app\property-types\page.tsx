// STORY-013: Property Types Overview Page
import { Metadata } from 'next'
import Link from 'next/link'
import { getPropertyTypes } from '@/lib/payload'
import { richTextToTruncatedPlainText } from '@/lib/richtext-utils'

export const metadata: Metadata = {
  title: 'Property Types in Bali - Villas, Guesthouses & More | Bali Real Estate',
  description: 'Explore different property types in Bali including luxury villas, authentic guesthouses, modern apartments, and investment opportunities.',
  keywords: 'Bali property types, villa rental Bali, guesthouse Bali, apartment Bali, property investment Bali',
}

export default async function PropertyTypesPage() {
  const propertyTypes = await getPropertyTypes()

  return (
    <main className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-emerald-600 text-white py-20">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Property Types in Bali
            </h1>
            <p className="text-xl md:text-2xl text-emerald-100 max-w-3xl mx-auto mb-8">
              From luxury villas to authentic guesthouses - find the perfect property type 
              for your Bali lifestyle and investment goals
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-white text-emerald-600 px-8 py-3 rounded-lg font-semibold hover:bg-emerald-50 transition-colors"
              >
                Get Expert Guidance
              </Link>
              <Link
                href="/locations"
                className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-emerald-600 transition-colors"
              >
                Browse Locations
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Property Types Grid */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Choose Your Perfect Property Type
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Each property type offers unique advantages for different lifestyles, 
              budgets, and investment strategies in Bali
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {propertyTypes.map((propertyType: any) => (
              <div key={propertyType.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mr-4">
                      {propertyType.slug === 'villa' && (
                        <svg className="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                        </svg>
                      )}
                      {propertyType.slug === 'guesthouse' && (
                        <svg className="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                      )}
                      {propertyType.slug === 'apartment' && (
                        <svg className="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 8h1m-1-4h1m4 4h1m-1-4h1" />
                        </svg>
                      )}
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900">{propertyType.name}</h3>
                      <span className="text-sm text-emerald-600 font-medium capitalize">{propertyType.category}</span>
                    </div>
                  </div>

                  <p className="text-gray-600 mb-6 line-clamp-3">
                    {richTextToTruncatedPlainText(propertyType.description, 150)}
                  </p>

                  {/* Features Preview */}
                  {propertyType.typicalFeatures && propertyType.typicalFeatures.length > 0 && (
                    <div className="mb-6">
                      <h4 className="text-sm font-semibold text-gray-900 mb-3">Key Features:</h4>
                      <div className="space-y-2">
                        {propertyType.typicalFeatures.slice(0, 3).map((feature: any, index: number) => (
                          <div key={index} className="flex items-center text-sm text-gray-600">
                            <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                            {feature.feature}
                          </div>
                        ))}
                        {propertyType.typicalFeatures.length > 3 && (
                          <div className="text-sm text-emerald-600 font-medium">
                            +{propertyType.typicalFeatures.length - 3} more features
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Price Range */}
                  {propertyType.priceRange && (
                    <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                      <h4 className="text-sm font-semibold text-gray-900 mb-2">Price Range:</h4>
                      {propertyType.priceRange.rental && (
                        <div className="text-sm text-gray-600">
                          <span className="font-medium">Rental:</span> {propertyType.priceRange.rental.longTermRental || 'Contact for pricing'}
                        </div>
                      )}
                      {propertyType.priceRange.purchase && (
                        <div className="text-sm text-gray-600">
                          <span className="font-medium">Purchase:</span> {propertyType.priceRange.purchase.forSaleFreehold || 'Contact for pricing'}
                        </div>
                      )}
                    </div>
                  )}

                  <Link
                    href={`/property-types/${propertyType.slug}`}
                    className="block w-full bg-emerald-600 hover:bg-emerald-700 text-white text-center py-3 rounded-lg font-semibold transition-colors"
                  >
                    Learn More
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Different Property Types */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Why Property Type Matters
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Understanding different property types helps you make the right choice 
              for your lifestyle, budget, and investment goals
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Budget Optimization</h3>
              <p className="text-gray-600">
                Different property types offer various price points and value propositions, 
                helping you maximize your budget and investment returns.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Lifestyle Match</h3>
              <p className="text-gray-600">
                Each property type caters to different lifestyles - from luxury privacy 
                to cultural immersion and community living experiences.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Investment Strategy</h3>
              <p className="text-gray-600">
                Understanding property types helps you choose the right investment strategy 
                based on rental yields, appreciation potential, and market demand.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-emerald-600 to-teal-600 text-white">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">
            Ready to Find Your Perfect Property?
          </h2>
          <p className="text-xl text-emerald-100 mb-8">
            Get personalized recommendations based on your lifestyle, budget, and investment goals
          </p>
          <Link
            href="/chatbot?query=property-consultation"
            className="inline-flex items-center bg-white text-emerald-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-emerald-50 transition-colors"
          >
            Get Expert Consultation
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </section>
    </main>
  )
}

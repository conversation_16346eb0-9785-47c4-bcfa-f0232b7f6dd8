/**
 * Loading Components
 * 
 * Various loading indicators and skeleton components for different use cases.
 * Includes spinners, progress bars, and skeleton loaders.
 */

import React from 'react';
import { cn } from '@/lib/utils';

export type LoadingSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export type LoadingVariant = 'spinner' | 'dots' | 'pulse' | 'bars';

export interface LoadingProps {
  /** Loading indicator size */
  size?: LoadingSize;
  /** Loading indicator variant */
  variant?: LoadingVariant;
  /** Loading text */
  text?: string;
  /** Custom className */
  className?: string;
  /** Color variant */
  color?: 'primary' | 'secondary' | 'neutral';
}

// Loading size styles
const loadingSizes = {
  xs: 'w-4 h-4',
  sm: 'w-6 h-6',
  md: 'w-8 h-8',
  lg: 'w-12 h-12',
  xl: 'w-16 h-16',
};

// Color variants
const colorVariants = {
  primary: 'text-primary-600',
  secondary: 'text-secondary-600',
  neutral: 'text-neutral-600',
};

// Spinner component
const Spinner: React.FC<{ size: LoadingSize; color: string }> = ({ size, color }) => (
  <svg
    className={cn('animate-spin', loadingSizes[size], color)}
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
  >
    <circle
      className="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="4"
    />
    <path
      className="opacity-75"
      fill="currentColor"
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    />
  </svg>
);

// Dots component
const Dots: React.FC<{ size: LoadingSize; color: string }> = ({ size, color }) => {
  const dotSizes = {
    xs: 'w-1 h-1',
    sm: 'w-1.5 h-1.5',
    md: 'w-2 h-2',
    lg: 'w-3 h-3',
    xl: 'w-4 h-4',
  };

  return (
    <div className="flex space-x-1">
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={cn(
            'rounded-full animate-pulse',
            dotSizes[size],
            color.replace('text-', 'bg-')
          )}
          style={{
            animationDelay: `${i * 0.2}s`,
            animationDuration: '1s',
          }}
        />
      ))}
    </div>
  );
};

// Pulse component
const Pulse: React.FC<{ size: LoadingSize; color: string }> = ({ size, color }) => (
  <div
    className={cn(
      'rounded-full animate-pulse',
      loadingSizes[size],
      color.replace('text-', 'bg-')
    )}
  />
);

// Bars component
const Bars: React.FC<{ size: LoadingSize; color: string }> = ({ size, color }) => {
  const barHeights = {
    xs: ['h-2', 'h-3', 'h-2'],
    sm: ['h-3', 'h-4', 'h-3'],
    md: ['h-4', 'h-6', 'h-4'],
    lg: ['h-6', 'h-8', 'h-6'],
    xl: ['h-8', 'h-12', 'h-8'],
  };

  const barWidth = {
    xs: 'w-0.5',
    sm: 'w-1',
    md: 'w-1',
    lg: 'w-1.5',
    xl: 'w-2',
  };

  return (
    <div className="flex items-end space-x-1">
      {barHeights[size].map((height, i) => (
        <div
          key={i}
          className={cn(
            'animate-pulse',
            barWidth[size],
            height,
            color.replace('text-', 'bg-')
          )}
          style={{
            animationDelay: `${i * 0.15}s`,
            animationDuration: '0.8s',
          }}
        />
      ))}
    </div>
  );
};

export const Loading: React.FC<LoadingProps> = ({
  size = 'md',
  variant = 'spinner',
  text,
  className,
  color = 'primary',
}) => {
  const colorClass = colorVariants[color];

  const renderLoadingIndicator = () => {
    switch (variant) {
      case 'spinner':
        return <Spinner size={size} color={colorClass} />;
      case 'dots':
        return <Dots size={size} color={colorClass} />;
      case 'pulse':
        return <Pulse size={size} color={colorClass} />;
      case 'bars':
        return <Bars size={size} color={colorClass} />;
      default:
        return <Spinner size={size} color={colorClass} />;
    }
  };

  return (
    <div className={cn('flex flex-col items-center justify-center gap-3', className)}>
      {renderLoadingIndicator()}
      {text && (
        <p className={cn('text-sm font-medium', colorClass)}>
          {text}
        </p>
      )}
    </div>
  );
};

// Skeleton components for content loading
export interface SkeletonProps {
  /** Skeleton width */
  width?: string | number;
  /** Skeleton height */
  height?: string | number;
  /** Whether skeleton is circular */
  circle?: boolean;
  /** Custom className */
  className?: string;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width,
  height = '1rem',
  circle = false,
  className,
}) => {
  return (
    <div
      className={cn(
        'animate-pulse bg-neutral-200',
        circle ? 'rounded-full' : 'rounded',
        className
      )}
      style={{
        width: typeof width === 'number' ? `${width}px` : width,
        height: typeof height === 'number' ? `${height}px` : height,
      }}
    />
  );
};

// Skeleton Text - for text content
export interface SkeletonTextProps {
  /** Number of lines */
  lines?: number;
  /** Custom className */
  className?: string;
}

export const SkeletonText: React.FC<SkeletonTextProps> = ({
  lines = 3,
  className,
}) => {
  return (
    <div className={cn('space-y-2', className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <Skeleton
          key={i}
          height="1rem"
          width={i === lines - 1 ? '75%' : '100%'}
        />
      ))}
    </div>
  );
};

// Skeleton Card - for card content
export interface SkeletonCardProps {
  /** Whether to show image skeleton */
  showImage?: boolean;
  /** Whether to show avatar skeleton */
  showAvatar?: boolean;
  /** Number of text lines */
  textLines?: number;
  /** Custom className */
  className?: string;
}

export const SkeletonCard: React.FC<SkeletonCardProps> = ({
  showImage = true,
  showAvatar = false,
  textLines = 3,
  className,
}) => {
  return (
    <div className={cn('p-6 bg-white rounded-lg border border-neutral-200', className)}>
      {/* Image skeleton */}
      {showImage && (
        <Skeleton height="12rem" className="mb-4" />
      )}
      
      {/* Header with avatar */}
      {showAvatar && (
        <div className="flex items-center mb-4">
          <Skeleton circle width="2.5rem" height="2.5rem" className="mr-3" />
          <div className="flex-1">
            <Skeleton height="1rem" width="60%" className="mb-1" />
            <Skeleton height="0.75rem" width="40%" />
          </div>
        </div>
      )}
      
      {/* Text content */}
      <SkeletonText lines={textLines} />
      
      {/* Action buttons */}
      <div className="flex gap-2 mt-4">
        <Skeleton height="2.5rem" width="5rem" />
        <Skeleton height="2.5rem" width="4rem" />
      </div>
    </div>
  );
};

// Loading Overlay - for full page or section loading
export interface LoadingOverlayProps {
  /** Whether overlay is visible */
  visible: boolean;
  /** Loading text */
  text?: string;
  /** Loading variant */
  variant?: LoadingVariant;
  /** Whether to blur background */
  blur?: boolean;
  /** Custom className */
  className?: string;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  visible,
  text = 'Loading...',
  variant = 'spinner',
  blur = true,
  className,
}) => {
  if (!visible) return null;

  return (
    <div
      className={cn(
        'absolute inset-0 z-50',
        'flex items-center justify-center',
        'bg-white/80',
        blur && 'backdrop-blur-sm',
        className
      )}
    >
      <Loading
        variant={variant}
        text={text}
        size="lg"
        className="bg-white p-6 rounded-lg shadow-lg"
      />
    </div>
  );
};

// Progress Bar component
export interface ProgressBarProps {
  /** Progress value (0-100) */
  value: number;
  /** Progress bar size */
  size?: 'sm' | 'md' | 'lg';
  /** Progress bar color */
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  /** Whether to show percentage text */
  showPercentage?: boolean;
  /** Custom className */
  className?: string;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  size = 'md',
  color = 'primary',
  showPercentage = false,
  className,
}) => {
  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3',
  };

  const colorClasses = {
    primary: 'bg-primary-600',
    secondary: 'bg-secondary-600',
    success: 'bg-green-600',
    warning: 'bg-yellow-600',
    error: 'bg-red-600',
  };

  const clampedValue = Math.min(100, Math.max(0, value));

  return (
    <div className={cn('w-full', className)}>
      {showPercentage && (
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-neutral-700">Progress</span>
          <span className="text-sm font-medium text-neutral-700">{Math.round(clampedValue)}%</span>
        </div>
      )}
      
      <div className={cn('w-full bg-neutral-200 rounded-full overflow-hidden', sizeClasses[size])}>
        <div
          className={cn('h-full transition-all duration-300 ease-out', colorClasses[color])}
          style={{ width: `${clampedValue}%` }}
        />
      </div>
    </div>
  );
};

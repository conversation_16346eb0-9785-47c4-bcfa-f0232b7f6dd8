import { CollectionConfig } from 'payload/types'

export const AIConversations: CollectionConfig = {
  slug: 'ai-conversations',
  labels: {
    singular: 'AI Conversation',
    plural: 'AI Conversations',
  },
  admin: {
    useAsTitle: 'sessionId',
    defaultColumns: ['sessionId', 'status', 'messageCount', 'createdAt'],
    group: 'AI System',
  },
  access: {
    read: () => true, // Allow reading for conversation retrieval
    create: () => true, // Allow creation for new conversations
    update: () => true, // Allow updates for adding messages
    delete: () => true, // Allow deletion for cleanup
  },
  fields: [
    {
      name: 'sessionId',
      type: 'text',
      required: true,
      unique: true,
      index: true,
      admin: {
        description: 'Unique session identifier for conversation tracking',
      },
    },
    {
      name: 'userId',
      type: 'text',
      index: true,
      admin: {
        description: 'Optional user identifier for authenticated users',
      },
    },
    {
      name: 'messages',
      type: 'array',
      required: true,
      fields: [
        {
          name: 'id',
          type: 'text',
          required: true,
        },
        {
          name: 'role',
          type: 'select',
          required: true,
          options: [
            { label: 'User', value: 'user' },
            { label: 'Assistant', value: 'assistant' },
            { label: 'System', value: 'system' },
          ],
        },
        {
          name: 'content',
          type: 'textarea',
          required: true,
        },
        {
          name: 'timestamp',
          type: 'date',
          required: true,
          admin: {
            date: {
              pickerAppearance: 'dayAndTime',
            },
          },
        },
        {
          name: 'metadata',
          type: 'json',
          admin: {
            description: 'Additional message metadata (property matches, etc.)',
          },
        },
      ],
    },
    {
      name: 'userPreferences',
      type: 'group',
      fields: [
        {
          name: 'budget',
          type: 'group',
          fields: [
            {
              name: 'min',
              type: 'number',
              admin: {
                description: 'Minimum budget in USD',
              },
            },
            {
              name: 'max',
              type: 'number',
              admin: {
                description: 'Maximum budget in USD',
              },
            },
            {
              name: 'currency',
              type: 'select',
              defaultValue: 'USD',
              options: [
                { label: 'USD', value: 'USD' },
                { label: 'EUR', value: 'EUR' },
                { label: 'IDR', value: 'IDR' },
              ],
            },
          ],
        },
        {
          name: 'duration',
          type: 'select',
          options: [
            { label: 'Short-term (1-6 months)', value: 'short-term' },
            { label: 'Long-term (6+ months)', value: 'long-term' },
            { label: 'Flexible', value: 'flexible' },
          ],
        },
        {
          name: 'propertyTypes',
          type: 'array',
          fields: [
            {
              name: 'type',
              type: 'text',
            },
          ],
        },
        {
          name: 'locations',
          type: 'array',
          fields: [
            {
              name: 'location',
              type: 'text',
            },
          ],
        },
        {
          name: 'lifestyle',
          type: 'array',
          fields: [
            {
              name: 'preference',
              type: 'select',
              options: [
                { label: 'Beach Access', value: 'beach' },
                { label: 'Cultural Experience', value: 'cultural' },
                { label: 'Nightlife', value: 'nightlife' },
                { label: 'Quiet/Peaceful', value: 'quiet' },
                { label: 'Family-Friendly', value: 'family' },
                { label: 'Digital Nomad Hub', value: 'nomad' },
                { label: 'Luxury Amenities', value: 'luxury' },
                { label: 'Local Community', value: 'community' },
              ],
            },
          ],
        },
        {
          name: 'priorities',
          type: 'array',
          fields: [
            {
              name: 'priority',
              type: 'text',
            },
          ],
        },
      ],
    },
    {
      name: 'context',
      type: 'group',
      fields: [
        {
          name: 'currentLocation',
          type: 'text',
          admin: {
            description: 'Current page location context',
          },
        },
        {
          name: 'currentPropertyType',
          type: 'text',
          admin: {
            description: 'Current page property type context',
          },
        },
        {
          name: 'referrerPage',
          type: 'text',
          admin: {
            description: 'Page where conversation started',
          },
        },
        {
          name: 'userAgent',
          type: 'text',
          admin: {
            description: 'Browser user agent for analytics',
          },
        },
      ],
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      defaultValue: 'active',
      options: [
        { label: 'Active', value: 'active' },
        { label: 'Paused', value: 'paused' },
        { label: 'Completed', value: 'completed' },
        { label: 'Expired', value: 'expired' },
      ],
      index: true,
    },
    {
      name: 'messageCount',
      type: 'number',
      defaultValue: 0,
      admin: {
        description: 'Total number of messages in conversation',
        readOnly: true,
      },
    },
    {
      name: 'leadScore',
      type: 'number',
      min: 0,
      max: 100,
      admin: {
        description: 'AI-calculated lead qualification score (0-100)',
      },
    },
    {
      name: 'qualificationStatus',
      type: 'select',
      options: [
        { label: 'Unqualified', value: 'unqualified' },
        { label: 'Qualified', value: 'qualified' },
        { label: 'Hot Lead', value: 'hot_lead' },
      ],
      index: true,
    },
    {
      name: 'expiresAt',
      type: 'date',
      required: true,
      admin: {
        description: 'Conversation expiration date (24 hours from creation)',
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
      index: true,
    },
  ],
  hooks: {
    beforeChange: [
      ({ data, operation }) => {
        // Set expiration date for new conversations (24 hours)
        if (operation === 'create') {
          const expirationDate = new Date()
          expirationDate.setHours(expirationDate.getHours() + 24)
          data.expiresAt = expirationDate
        }

        // Update message count
        if (data.messages) {
          data.messageCount = data.messages.length
        }

        return data
      },
    ],
  },
  timestamps: true,
}

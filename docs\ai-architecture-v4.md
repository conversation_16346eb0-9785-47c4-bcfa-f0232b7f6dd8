# AI-Powered Real Estate Platform - Architecture Document v4

**Project**: Bali Real Estate Website AI Enhancement  
**Version**: 4.0  
**Date**: January 22, 2025  
**Author**: BMAD Architect Agent

---

## Executive Summary

This document defines the architectural approach for integrating AI-powered property matching capabilities into the existing Bali Real Estate website while maintaining current performance, SEO optimization, and user experience standards.

## Current System Architecture

### Existing Technology Stack
- **Frontend**: Next.js 15.5.0 (App Router) + TypeScript + Tailwind CSS 4
- **Backend**: Payload CMS 3.52.0 with PostgreSQL/MongoDB dual support
- **Hosting**: Vercel with Edge Functions and CDN optimization
- **Performance**: Core Web Vitals optimized, <2.5s LCP

### Current Data Models
```typescript
// Existing Collections
Locations {
  name: string
  slug: string
  description: RichText
  lifestyle: RichText
  priceRange: object
  amenities: array
  coordinates: point
  seoFields: group
}

PropertyTypes {
  name: string
  slug: string
  description: RichText
  typicalFeatures: array
  priceRanges: object
  targetAudience: array
  investmentAnalysis: object
}

Leads {
  name: string
  email: string
  phone: string
  message: RichText
  preferences: object
  createdAt: date
}
```

## AI Enhancement Architecture

### New System Components

#### 1. AI Chat Infrastructure
```typescript
// New Collections for AI Features
AIConversations {
  id: string
  sessionId: string
  userId?: string
  messages: array<{
    role: 'user' | 'assistant'
    content: string
    timestamp: date
    metadata?: object
  }>
  userPreferences: {
    budget: { min: number, max: number }
    duration: string
    propertyType: string[]
    locations: string[]
    lifestyle: string[]
  }
  leadScore: number
  status: 'active' | 'qualified' | 'converted' | 'inactive'
  createdAt: date
  updatedAt: date
}

PropertyMatches {
  id: string
  conversationId: string
  propertyId?: string
  matchScore: number
  reasoning: string
  userFeedback?: 'interested' | 'not_interested' | 'maybe'
  createdAt: date
}

LeadScoring {
  id: string
  conversationId: string
  budgetAlignment: number // 1-10
  timelineUrgency: number // 1-10
  locationSpecificity: number // 1-10
  propertyTypeClarity: number // 1-10
  engagementLevel: number // 1-10
  totalScore: number
  qualificationStatus: 'unqualified' | 'qualified' | 'hot_lead'
}
```

#### 2. AI Service Integration Layer
```typescript
// AI Service Abstraction
interface AIService {
  generateResponse(conversation: AIConversation, userInput: string): Promise<AIResponse>
  matchProperties(preferences: UserPreferences): Promise<PropertyMatch[]>
  scoreConversation(conversation: AIConversation): Promise<LeadScore>
}

// Implementation supports multiple AI providers
class OpenAIService implements AIService { ... }
class AnthropicService implements AIService { ... }
```

#### 3. Real-time Communication
```typescript
// WebSocket/SSE for real-time chat
interface ChatConnection {
  sessionId: string
  userId?: string
  onMessage: (message: ChatMessage) => void
  onTyping: (isTyping: boolean) => void
  onDisconnect: () => void
}
```

### API Architecture

#### New API Endpoints
```
POST /api/ai/chat/start
- Initialize new conversation session
- Return sessionId and initial greeting

POST /api/ai/chat/message
- Process user message
- Return AI response with property matches

GET /api/ai/chat/history/:sessionId
- Retrieve conversation history
- Support pagination for long conversations

POST /api/ai/properties/match
- Match properties based on preferences
- Return scored property recommendations

POST /api/ai/leads/qualify
- Qualify lead based on conversation
- Trigger automated follow-up sequences

GET /api/ai/analytics/conversations
- Admin endpoint for conversation analytics
- Support filtering and aggregation
```

#### Enhanced Existing Endpoints
```
POST /api/leads (Enhanced)
- Accept AI-generated lead data
- Include conversation context and scoring
- Maintain backward compatibility

GET /api/property-types/:slug (Enhanced)
- Include AI conversation starters
- Add contextual chat triggers
- Preserve existing SEO structure
```

### Frontend Architecture

#### Component Structure
```
src/components/ai/
├── ChatWidget/
│   ├── ChatWidget.tsx          # Main chat interface
│   ├── ChatBubble.tsx          # Individual message display
│   ├── ChatInput.tsx           # User input handling
│   ├── TypingIndicator.tsx     # AI typing animation
│   └── ChatWidget.module.css   # Scoped styling
├── PropertyRecommendations/
│   ├── PropertyCard.tsx        # AI-recommended property display
│   ├── MatchReasoning.tsx      # Explanation of match logic
│   └── PropertyComparison.tsx  # Side-by-side comparison
├── ConversationFlow/
│   ├── PreferenceCapture.tsx   # Structured preference collection
│   ├── BudgetSelector.tsx      # Budget range input
│   ├── LocationSelector.tsx    # Location preference mapping
│   └── PropertyTypeSelector.tsx # Property type preferences
└── LeadCapture/
    ├── ContactTransition.tsx   # Smooth chat-to-contact transition
    ├── LeadForm.tsx            # Enhanced contact form
    └── FollowUpScheduler.tsx   # Appointment scheduling
```

#### State Management
```typescript
// React Context for AI state
interface AIContextState {
  currentConversation: AIConversation | null
  isTyping: boolean
  propertyMatches: PropertyMatch[]
  userPreferences: UserPreferences
  leadScore: LeadScore | null
}

// Actions for state updates
type AIAction = 
  | { type: 'START_CONVERSATION'; sessionId: string }
  | { type: 'ADD_MESSAGE'; message: ChatMessage }
  | { type: 'UPDATE_PREFERENCES'; preferences: Partial<UserPreferences> }
  | { type: 'SET_PROPERTY_MATCHES'; matches: PropertyMatch[] }
  | { type: 'UPDATE_LEAD_SCORE'; score: LeadScore }
```

### Integration Points

#### 1. Existing Page Enhancement
```typescript
// Location pages (/canggu, /ubud, etc.)
const LocationPage = ({ location }: { location: Location }) => {
  return (
    <div>
      {/* Existing content preserved */}
      <LocationHero location={location} />
      <LocationContent location={location} />
      
      {/* New AI integration points */}
      <AIContextProvider initialContext={{ location: location.slug }}>
        <ChatWidget 
          contextualPrompt={`Find ${location.name} properties`}
          initialPreferences={{ locations: [location.slug] }}
        />
      </AIContextProvider>
    </div>
  )
}
```

#### 2. SEO Preservation
```typescript
// Maintain existing meta generation
export async function generateMetadata({ params }: { params: { slug: string } }) {
  const location = await getLocationBySlug(params.slug)
  
  return {
    title: `${location.name} Properties | AI-Powered Search | Bali Real Estate`,
    description: `Find perfect ${location.name} properties with our AI assistant. ${location.description}`,
    // Preserve existing structured data
    other: {
      'application/ld+json': JSON.stringify(generateLocationSchema(location))
    }
  }
}
```

### Performance Considerations

#### 1. Code Splitting
```typescript
// Lazy load AI components
const ChatWidget = lazy(() => import('@/components/ai/ChatWidget'))
const PropertyRecommendations = lazy(() => import('@/components/ai/PropertyRecommendations'))

// Load only when user interacts
const [showChat, setShowChat] = useState(false)
```

#### 2. Caching Strategy
```typescript
// API response caching
const propertyMatchCache = new Map<string, PropertyMatch[]>()
const conversationCache = new Map<string, AIConversation>()

// Redis caching for production
const cacheKey = `property-matches:${JSON.stringify(preferences)}`
const cachedMatches = await redis.get(cacheKey)
```

#### 3. Database Optimization
```sql
-- Indexes for AI queries
CREATE INDEX idx_conversations_session ON ai_conversations(session_id);
CREATE INDEX idx_conversations_status ON ai_conversations(status);
CREATE INDEX idx_property_matches_score ON property_matches(match_score DESC);
CREATE INDEX idx_lead_scoring_total ON lead_scoring(total_score DESC);
```

### Security & Privacy

#### 1. Data Protection
```typescript
// Conversation data encryption
const encryptConversation = (conversation: AIConversation): EncryptedConversation => {
  return {
    ...conversation,
    messages: encrypt(JSON.stringify(conversation.messages), process.env.ENCRYPTION_KEY)
  }
}

// GDPR compliance
const deleteUserData = async (sessionId: string) => {
  await Promise.all([
    AIConversations.deleteMany({ sessionId }),
    PropertyMatches.deleteMany({ conversationId: { $in: conversationIds } }),
    LeadScoring.deleteMany({ conversationId: { $in: conversationIds } })
  ])
}
```

#### 2. Rate Limiting
```typescript
// AI API rate limiting
const rateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many AI requests, please try again later'
})
```

This architecture ensures the AI enhancement integrates seamlessly with the existing system while providing robust, scalable, and maintainable AI-powered property discovery capabilities.

/**
 * Gallery Component Tests
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Gallery } from '../Gallery';
import type { GalleryImage } from '../Gallery';

// Mock images data
const mockImages: GalleryImage[] = [
  {
    src: '/image1.jpg',
    alt: 'Image 1',
    title: 'First Image',
    caption: 'Caption for first image',
    width: 800,
    height: 600,
  },
  {
    src: '/image2.jpg',
    alt: 'Image 2',
    title: 'Second Image',
    caption: 'Caption for second image',
    width: 800,
    height: 600,
  },
  {
    src: '/image3.jpg',
    alt: 'Image 3',
    title: 'Third Image',
    width: 800,
    height: 600,
  },
];

describe('Gallery Component', () => {
  describe('Basic Rendering', () => {
    it('renders images correctly', () => {
      render(<Gallery images={mockImages} />);
      
      expect(screen.getByAltText('Image 1')).toBeInTheDocument();
      expect(screen.getByAltText('Image 2')).toBeInTheDocument();
      expect(screen.getByAltText('Image 3')).toBeInTheDocument();
    });

    it('renders empty gallery gracefully', () => {
      const { container } = render(<Gallery images={[]} />);
      expect(container.firstChild).toBeNull();
    });

    it('handles undefined images prop', () => {
      // @ts-expect-error Testing undefined prop
      const { container } = render(<Gallery images={undefined} />);
      expect(container.firstChild).toBeNull();
    });
  });

  describe('Layout Variants', () => {
    it('applies grid layout by default', () => {
      const { container } = render(<Gallery images={mockImages} />);
      const galleryContainer = container.querySelector('.gallery > div');
      expect(galleryContainer).toHaveClass('grid');
    });

    it('applies masonry layout', () => {
      const { container } = render(<Gallery images={mockImages} layout="masonry" />);
      const galleryContainer = container.querySelector('.gallery > div');
      expect(galleryContainer).toHaveClass('columns-1', 'sm:columns-2');
    });

    it('applies carousel layout', () => {
      const { container } = render(<Gallery images={mockImages} layout="carousel" />);
      const galleryContainer = container.querySelector('.gallery > div');
      expect(galleryContainer).toHaveClass('flex', 'overflow-x-auto');
    });

    it('applies thumbnails layout', () => {
      render(<Gallery images={mockImages} layout="thumbnails" />);
      // Should render main image and thumbnails
      expect(screen.getAllByAltText(/Image/)).toHaveLength(mockImages.length);
    });
  });

  describe('Grid Columns', () => {
    it('applies correct column classes', () => {
      const { container } = render(<Gallery images={mockImages} columns={4} />);
      const galleryContainer = container.querySelector('.gallery > div');
      expect(galleryContainer).toHaveClass('xl:grid-cols-4');
    });

    it('applies single column layout', () => {
      const { container } = render(<Gallery images={mockImages} columns={1} />);
      const galleryContainer = container.querySelector('.gallery > div');
      expect(galleryContainer).toHaveClass('grid-cols-1');
    });
  });

  describe('Gap Sizes', () => {
    it('applies medium gap by default', () => {
      const { container } = render(<Gallery images={mockImages} />);
      const galleryContainer = container.querySelector('.gallery > div');
      expect(galleryContainer).toHaveClass('gap-4');
    });

    it('applies small gap', () => {
      const { container } = render(<Gallery images={mockImages} gap="sm" />);
      const galleryContainer = container.querySelector('.gallery > div');
      expect(galleryContainer).toHaveClass('gap-2');
    });

    it('applies large gap', () => {
      const { container } = render(<Gallery images={mockImages} gap="lg" />);
      const galleryContainer = container.querySelector('.gallery > div');
      expect(galleryContainer).toHaveClass('gap-6');
    });
  });

  describe('Aspect Ratios', () => {
    it('applies 4/3 aspect ratio by default', () => {
      const { container } = render(<Gallery images={mockImages} />);
      const imageContainer = container.querySelector('[class*="aspect-"]');
      expect(imageContainer).toHaveClass('aspect-[4/3]');
    });

    it('applies square aspect ratio', () => {
      const { container } = render(<Gallery images={mockImages} aspectRatio="square" />);
      const imageContainer = container.querySelector('[class*="aspect-"]');
      expect(imageContainer).toHaveClass('aspect-square');
    });

    it('applies video aspect ratio', () => {
      const { container } = render(<Gallery images={mockImages} aspectRatio="16/9" />);
      const imageContainer = container.querySelector('[class*="aspect-"]');
      expect(imageContainer).toHaveClass('aspect-video');
    });
  });

  describe('Captions', () => {
    it('shows captions by default', () => {
      render(<Gallery images={mockImages} />);
      expect(screen.getByText('First Image')).toBeInTheDocument();
      expect(screen.getByText('Caption for first image')).toBeInTheDocument();
    });

    it('hides captions when showCaptions is false', () => {
      render(<Gallery images={mockImages} showCaptions={false} />);
      expect(screen.queryByText('First Image')).not.toBeInTheDocument();
      expect(screen.queryByText('Caption for first image')).not.toBeInTheDocument();
    });

    it('shows only title when caption is missing', () => {
      render(<Gallery images={mockImages} />);
      expect(screen.getByText('Third Image')).toBeInTheDocument();
    });
  });

  describe('Image Click Handling', () => {
    it('calls onImageClick when provided', () => {
      const onImageClick = vi.fn();
      render(<Gallery images={mockImages} onImageClick={onImageClick} />);
      
      fireEvent.click(screen.getByAltText('Image 1'));
      expect(onImageClick).toHaveBeenCalledWith(mockImages[0], 0);
    });

    it('opens lightbox by default when image is clicked', () => {
      render(<Gallery images={mockImages} />);
      
      fireEvent.click(screen.getByAltText('Image 1'));
      
      // Should show lightbox modal
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    it('does not open lightbox when disabled', () => {
      render(<Gallery images={mockImages} lightbox={false} />);
      
      fireEvent.click(screen.getByAltText('Image 1'));
      
      // Should not show lightbox modal
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });
  });

  describe('Lightbox Functionality', () => {
    beforeEach(() => {
      // Mock scrollY for lightbox functionality
      Object.defineProperty(window, 'scrollY', {
        writable: true,
        value: 0,
      });
    });

    it('opens lightbox with correct image', () => {
      render(<Gallery images={mockImages} />);
      
      fireEvent.click(screen.getByAltText('Image 2'));
      
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      // Should show the clicked image in lightbox
      const lightboxImage = screen.getByRole('dialog').querySelector('img');
      expect(lightboxImage).toHaveAttribute('src', '/image2.jpg');
    });

    it('navigates to next image', () => {
      render(<Gallery images={mockImages} />);
      
      fireEvent.click(screen.getByAltText('Image 1'));
      
      const nextButton = screen.getByLabelText('Next image');
      fireEvent.click(nextButton);
      
      const lightboxImage = screen.getByRole('dialog').querySelector('img');
      expect(lightboxImage).toHaveAttribute('src', '/image2.jpg');
    });

    it('navigates to previous image', () => {
      render(<Gallery images={mockImages} />);
      
      fireEvent.click(screen.getByAltText('Image 2'));
      
      const prevButton = screen.getByLabelText('Previous image');
      fireEvent.click(prevButton);
      
      const lightboxImage = screen.getByRole('dialog').querySelector('img');
      expect(lightboxImage).toHaveAttribute('src', '/image1.jpg');
    });

    it('wraps around when navigating past last image', () => {
      render(<Gallery images={mockImages} />);
      
      fireEvent.click(screen.getByAltText('Image 3'));
      
      const nextButton = screen.getByLabelText('Next image');
      fireEvent.click(nextButton);
      
      const lightboxImage = screen.getByRole('dialog').querySelector('img');
      expect(lightboxImage).toHaveAttribute('src', '/image1.jpg');
    });

    it('wraps around when navigating before first image', () => {
      render(<Gallery images={mockImages} />);
      
      fireEvent.click(screen.getByAltText('Image 1'));
      
      const prevButton = screen.getByLabelText('Previous image');
      fireEvent.click(prevButton);
      
      const lightboxImage = screen.getByRole('dialog').querySelector('img');
      expect(lightboxImage).toHaveAttribute('src', '/image3.jpg');
    });

    it('closes lightbox when close button is clicked', () => {
      render(<Gallery images={mockImages} />);
      
      fireEvent.click(screen.getByAltText('Image 1'));
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      
      const closeButton = screen.getByLabelText('Close lightbox');
      fireEvent.click(closeButton);
      
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });

    it('shows image counter', () => {
      render(<Gallery images={mockImages} />);
      
      fireEvent.click(screen.getByAltText('Image 2'));
      
      expect(screen.getByText('2 / 3')).toBeInTheDocument();
    });

    it('shows thumbnails in lightbox when enabled', () => {
      render(<Gallery images={mockImages} showThumbnails={true} />);
      
      fireEvent.click(screen.getByAltText('Image 1'));
      
      // Should show thumbnail navigation
      const thumbnails = screen.getByRole('dialog').querySelectorAll('button img');
      expect(thumbnails).toHaveLength(mockImages.length);
    });

    it('hides thumbnails in lightbox when disabled', () => {
      render(<Gallery images={mockImages} showThumbnails={false} />);
      
      fireEvent.click(screen.getByAltText('Image 1'));
      
      // Should not show thumbnail navigation
      const thumbnails = screen.getByRole('dialog').querySelectorAll('button img');
      expect(thumbnails).toHaveLength(0);
    });
  });

  describe('Keyboard Navigation', () => {
    it('navigates with arrow keys in lightbox', () => {
      render(<Gallery images={mockImages} />);
      
      fireEvent.click(screen.getByAltText('Image 1'));
      
      // Navigate right
      fireEvent.keyDown(window, { key: 'ArrowRight' });
      
      const lightboxImage = screen.getByRole('dialog').querySelector('img');
      expect(lightboxImage).toHaveAttribute('src', '/image2.jpg');
      
      // Navigate left
      fireEvent.keyDown(window, { key: 'ArrowLeft' });
      
      expect(lightboxImage).toHaveAttribute('src', '/image1.jpg');
    });

    it('closes lightbox with Escape key', () => {
      render(<Gallery images={mockImages} />);
      
      fireEvent.click(screen.getByAltText('Image 1'));
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      
      fireEvent.keyDown(window, { key: 'Escape' });
      
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });
  });

  describe('Custom ClassName', () => {
    it('applies custom className', () => {
      const { container } = render(
        <Gallery images={mockImages} className="custom-gallery" />
      );
      
      expect(container.querySelector('.gallery')).toHaveClass('custom-gallery');
    });
  });
});

import { Metadata } from 'next'
import Link from 'next/link'
import { getLocations } from '@/lib/payload'

export const metadata: Metadata = {
  title: 'All Locations - Bali Real Estate | Long-term Rentals & Property Sales',
  description: 'Explore all available locations in Bali for long-term rentals and property sales. Find your perfect area from Canggu to Ubud, Seminyak and more.',
  keywords: 'Bali locations, property areas Bali, Canggu, Ubud, Seminyak, real estate locations',
}

export default async function LocationsPage() {
  const locations = await getLocations()

  return (
    <main className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-emerald-600 text-white py-16">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Explore Bali Locations
          </h1>
          <p className="text-xl mb-8">
            Discover the perfect area for your long-term rental or property investment in Bali.
            Each location offers unique lifestyle benefits and opportunities.
          </p>
        </div>
      </section>

      {/* Locations Grid */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {locations.map((location: any) => {
              // Location-specific images with Unsplash integration
              const locationImages = {
                'canggu': 'https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?w=800&auto=format&fit=crop&q=80',
                'ubud': 'https://images.unsplash.com/photo-1518548419970-58e3b4079ab2?w=800&auto=format&fit=crop&q=80',
                'seminyak': 'https://images.unsplash.com/photo-1540541338287-41700207dee6?w=800&auto=format&fit=crop&q=80',
                'sanur': 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=800&auto=format&fit=crop&q=80',
                'jimbaran': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&auto=format&fit=crop&q=80',
                'denpasar': 'https://images.unsplash.com/photo-1555400082-8dd4d78c670b?w=800&auto=format&fit=crop&q=80',
                'uluwatu': 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=800&auto=format&fit=crop&q=80'
              };

              const highlights = {
                'canggu': 'Surf & Beach Lifestyle',
                'ubud': 'Wellness & Nature',
                'seminyak': 'Luxury & Nightlife',
                'sanur': 'Family-Friendly Beach',
                'jimbaran': 'Seafood & Sunsets',
                'denpasar': 'Local City Experience',
                'uluwatu': 'Cliffside Views'
              };

              return (
                <div key={location.id} className="relative overflow-hidden bg-white shadow-xl border border-gray-100/50 rounded-2xl transition-all duration-500 ease-out hover:shadow-2xl hover:scale-[1.02] hover:-translate-y-2 hover:border-gray-200/70">
                  {/* Image */}
                  <div className="relative w-full h-48 overflow-hidden rounded-t-2xl">
                    <img
                      src={locationImages[location.slug as keyof typeof locationImages] || locationImages.canggu}
                      alt={`${location.name} location`}
                      className="w-full h-full object-cover transition-transform duration-700 hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
                  </div>

                  {/* Content */}
                  <div className="relative z-10 p-6 space-y-3">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-1">{location.name}</h3>
                      <p className="text-emerald-600 font-semibold text-sm">{location.priceRanges?.villa?.longTermRental || '$800-$2,500/month'}</p>
                    </div>

                    <p className="text-gray-600 text-sm font-medium">{highlights[location.slug as keyof typeof highlights] || 'Beautiful Location'}</p>

                    <p className="text-gray-700 text-sm line-clamp-2 leading-relaxed">
                      {location.description}
                    </p>

                    <Link
                      href={`/locations/${location.slug}`}
                      className="inline-flex items-center text-emerald-600 hover:text-emerald-700 font-medium text-sm transition-colors group"
                    >
                      Explore {location.name}
                      <svg
                        className="w-4 h-4 ml-1 transition-transform group-hover:translate-x-1"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gray-100 py-16">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Need Help Choosing the Right Location?</h2>
          <p className="text-lg text-gray-600 mb-8">
            Our local experts can help you find the perfect area based on your lifestyle, budget, and preferences.
          </p>
          <a
            href="https://app.bali-realestate.com/chatbot?query=location-consultation"
            className="inline-block bg-emerald-600 text-white px-8 py-3 rounded-lg hover:bg-emerald-700 transition-colors font-semibold"
            target="_self"
          >
            Get Expert Advice
          </a>
        </div>
      </section>
    </main>
  )
}

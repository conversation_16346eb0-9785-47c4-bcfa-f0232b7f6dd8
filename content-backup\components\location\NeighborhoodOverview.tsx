interface NeighborhoodOverviewProps {
  location: string
  locationInfo: {
    name: string
    description: string
    community: string
    lifestyle: string
  }
}

// Detailed neighborhood content based on PRD requirements (500+ words per location)
const neighborhoodContent = {
  'canggu': {
    overview: `Canggu has evolved from a quiet rice farming village into Bali's premier digital nomad destination, attracting thousands of remote workers, entrepreneurs, and surf enthusiasts from around the world. This vibrant coastal area offers the perfect blend of traditional Balinese culture and modern international amenities, making it an ideal location for long-term rentals and property investments.

The area is characterized by its stunning black sand beaches, world-class surf breaks, and expansive rice paddies that create a unique landscape where traditional agriculture meets contemporary lifestyle. Canggu's rapid development has been carefully managed to preserve its authentic charm while providing the infrastructure and amenities that international residents require.

What sets Canggu apart is its thriving community of digital nomads and remote workers. The area boasts over 20 coworking spaces, including internationally recognized brands like Dojo Bali and Outsite, providing reliable high-speed internet and professional networking opportunities. This has created a unique ecosystem where business and leisure seamlessly blend.

The dining scene in Canggu is exceptional, featuring everything from traditional warungs serving authentic Indonesian cuisine to trendy beach clubs and health-conscious cafes. Popular spots like La Brisa, Finn's Beach Club, and The Lawn offer world-class dining experiences, while local establishments provide affordable, delicious meals that cater to every budget and dietary preference.

Transportation in Canggu is convenient and affordable. The area is well-connected by scooter-friendly roads, and most daily necessities are within walking or short riding distance. The Ngurah Rai International Airport is approximately 45 minutes away, making international travel convenient for residents and visitors.`,
    
    neighborhoods: [
      {
        name: 'Echo Beach Area',
        description: 'The heart of Canggu\'s surf scene with beachfront restaurants and surf schools.',
        bestFor: 'Surfers and beach lovers'
      },
      {
        name: 'Berawa',
        description: 'Upscale area with luxury villas, fine dining, and beach clubs.',
        bestFor: 'Professionals and luxury seekers'
      },
      {
        name: 'Pererenan',
        description: 'Quieter area with rice field views and authentic Balinese atmosphere.',
        bestFor: 'Families and those seeking tranquility'
      },
      {
        name: 'Batu Bolong',
        description: 'Central area with the highest concentration of coworking spaces and cafes.',
        bestFor: 'Digital nomads and entrepreneurs'
      }
    ],

    accessibility: `Canggu is strategically located on Bali's southwest coast, offering easy access to other popular destinations. Seminyak is just 15 minutes away, while Ubud can be reached in 45 minutes. The area's road infrastructure has significantly improved, with new bypasses reducing traffic congestion during peak hours.`,

    uniqueFeatures: [
      'World-class surf breaks suitable for all skill levels',
      'Largest concentration of coworking spaces in Bali',
      'Vibrant nightlife with beach clubs and rooftop bars',
      'Weekly markets and cultural events',
      'Extensive network of rice paddies for scenic walks and cycling'
    ]
  },

  'ubud': {
    overview: `Ubud stands as Bali's cultural and spiritual heart, nestled among lush rice terraces and tropical forests in the island's central highlands. This enchanting town has long been a magnet for artists, writers, wellness seekers, and those drawn to Balinese culture and spirituality. Unlike the beach destinations, Ubud offers a completely different experience focused on inner growth, cultural immersion, and connection with nature.

The town's elevation of approximately 600 meters above sea level provides a cooler, more comfortable climate year-round, making it particularly appealing for long-term residents. The natural beauty is breathtaking, with the famous Tegallalang Rice Terraces, sacred Monkey Forest Sanctuary, and numerous temples creating a landscape that has inspired visitors for decades.

Ubud's wellness scene is unparalleled in Southeast Asia. The town hosts hundreds of yoga studios, meditation centers, healing practitioners, and wellness retreats. World-renowned establishments like Yoga Barn, Radiantly Alive, and The Practice offer daily classes and teacher training programs that attract practitioners from around the globe.

The cultural richness of Ubud is evident in its numerous art galleries, museums, and traditional craft workshops. The town center features the Ubud Royal Palace and traditional markets where local artisans sell handmade goods. Regular cultural performances, including traditional Balinese dance and music, provide authentic cultural experiences.

Ubud's dining scene emphasizes healthy, organic, and locally-sourced ingredients. The town pioneered Bali's farm-to-table movement, with restaurants like Locavore, Moksa, and Clear Cafe setting international standards for sustainable dining. The abundance of organic farms and health-conscious establishments makes it easy to maintain a healthy lifestyle.`,

    neighborhoods: [
      {
        name: 'Ubud Center',
        description: 'Historic town center with royal palace, traditional markets, and cultural sites.',
        bestFor: 'Culture enthusiasts and first-time visitors'
      },
      {
        name: 'Monkey Forest Road',
        description: 'Main tourist strip with restaurants, shops, and accommodation options.',
        bestFor: 'Convenience seekers and social butterflies'
      },
      {
        name: 'Penestanan',
        description: 'Artistic village with galleries, studios, and traditional architecture.',
        bestFor: 'Artists and creative professionals'
      },
      {
        name: 'Tegallalang',
        description: 'Famous rice terrace area with stunning views and peaceful atmosphere.',
        bestFor: 'Nature lovers and photographers'
      }
    ],

    accessibility: `Ubud is centrally located, making it an excellent base for exploring Bali. The town is approximately 1 hour from Ngurah Rai International Airport and well-connected to other major destinations. While the roads can be winding, the scenic journey through rice paddies and traditional villages is part of Ubud's charm.`,

    uniqueFeatures: [
      'Largest concentration of yoga studios and wellness centers in Asia',
      'UNESCO-recognized rice terrace landscapes',
      'Traditional art markets and craft workshops',
      'Organic farming and farm-to-table dining',
      'Sacred temples and spiritual retreat centers'
    ]
  },

  'seminyak': {
    overview: `Seminyak represents the sophisticated side of Bali, offering luxury accommodations, world-class dining, high-end shopping, and pristine beaches. This upscale destination has evolved into Bali's premier luxury resort area, attracting affluent travelers and residents who appreciate refined experiences and premium amenities.

The area is renowned for its stunning sunsets, which can be enjoyed from numerous beachfront establishments. The wide, sandy beaches are perfect for relaxation and water sports, while the consistent waves attract surfers of all levels. The beachfront is lined with luxury resorts, beach clubs, and restaurants that offer world-class service and amenities.

Seminyak's dining scene is internationally acclaimed, featuring restaurants by celebrity chefs and innovative culinary concepts. Establishments like Merah Putih, Sarong, and Mama San have put Seminyak on the global culinary map, while beachfront venues like Ku De Ta and Potato Head Beach Club offer unforgettable dining experiences with ocean views.

The shopping in Seminyak is exceptional, with a mix of international brands, local designers, and unique boutiques. The area features several high-end shopping complexes and streets lined with fashion stores, art galleries, and lifestyle shops. This makes it easy for residents to access quality goods and services.

For long-term residents, Seminyak offers excellent infrastructure, including reliable internet, quality healthcare facilities, international schools, and professional services. The area's proximity to the airport (20 minutes) makes it convenient for frequent travelers, while its central location provides easy access to other parts of Bali.`,

    neighborhoods: [
      {
        name: 'Seminyak Beach',
        description: 'Beachfront area with luxury resorts, beach clubs, and sunset dining.',
        bestFor: 'Luxury seekers and beach enthusiasts'
      },
      {
        name: 'Oberoi Street',
        description: 'High-end shopping and dining strip with boutique hotels.',
        bestFor: 'Shopping enthusiasts and fine dining lovers'
      },
      {
        name: 'Petitenget',
        description: 'Upscale residential area with luxury villas and private estates.',
        bestFor: 'Families and privacy seekers'
      },
      {
        name: 'Laksmana',
        description: 'Central area with mix of restaurants, shops, and accommodation.',
        bestFor: 'Convenience and variety seekers'
      }
    ],

    accessibility: `Seminyak enjoys excellent connectivity, being just 20 minutes from Ngurah Rai International Airport and well-connected to other major destinations. The area's road infrastructure is well-developed, and transportation options include taxis, ride-sharing services, and scooter rentals.`,

    uniqueFeatures: [
      'World-class beach clubs and sunset venues',
      'International fine dining scene',
      'Luxury shopping and boutique stores',
      'Premium spa and wellness facilities',
      'Proximity to airport and business districts'
    ]
  }
}

const NeighborhoodOverview = ({ location, locationInfo }: NeighborhoodOverviewProps) => {
  const content = neighborhoodContent[location as keyof typeof neighborhoodContent]

  if (!content) {
    return (
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">About {locationInfo.name}</h2>
          <p className="text-gray-600 leading-relaxed">{locationInfo.description}</p>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 bg-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            About {locationInfo.name}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comprehensive guide to living in {locationInfo.name}
          </p>
        </div>

        {/* Main Overview */}
        <div className="prose prose-lg max-w-none mb-16">
          <div className="text-gray-700 leading-relaxed space-y-6">
            {content.overview.split('\n\n').map((paragraph, index) => (
              <p key={index}>{paragraph}</p>
            ))}
          </div>
        </div>

        {/* Neighborhoods */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">Neighborhoods & Areas</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {content.neighborhoods.map((neighborhood, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-6">
                <h4 className="text-xl font-semibold text-gray-900 mb-3">{neighborhood.name}</h4>
                <p className="text-gray-600 mb-4">{neighborhood.description}</p>
                <div className="flex items-center text-emerald-600">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm font-medium">Best for: {neighborhood.bestFor}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Accessibility */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">Location & Accessibility</h3>
          <p className="text-gray-700 leading-relaxed">{content.accessibility}</p>
        </div>

        {/* Unique Features */}
        <div>
          <h3 className="text-2xl font-bold text-gray-900 mb-8">What Makes {locationInfo.name} Special</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {content.uniqueFeatures.map((feature, index) => (
              <div key={index} className="flex items-start gap-3">
                <div className="w-6 h-6 bg-emerald-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <svg className="w-3 h-3 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="text-gray-700">{feature}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default NeighborhoodOverview

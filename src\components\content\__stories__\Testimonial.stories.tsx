/**
 * Testimonial Component Stories
 */

import type { <PERSON>a, StoryObj } from '@storybook/react';
import { 
  Testimonial, 
  TestimonialCard, 
  TestimonialQuote, 
  TestimonialMinimal, 
  TestimonialFeatured,
  TestimonialCarousel 
} from '../Testimonial';
import type { TestimonialData } from '../Testimonial';

const meta: Meta<typeof Testimonial> = {
  title: 'Content/Testimonial',
  component: Testimonial,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'Testimonial display with ratings, avatars, and carousel functionality. Supports multiple layouts and interaction modes.',
      },
    },
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['card', 'quote', 'minimal', 'featured'],
      description: 'Layout variant',
    },
    showRating: {
      control: 'boolean',
      description: 'Whether to show rating',
    },
    showAvatar: {
      control: 'boolean',
      description: 'Whether to show avatar',
    },
    showDate: {
      control: 'boolean',
      description: 'Whether to show date',
    },
    carousel: {
      control: 'boolean',
      description: 'Whether to enable carousel (for multiple testimonials)',
    },
    autoPlay: {
      control: 'number',
      description: 'Auto-play interval for carousel (in ms)',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Testimonial>;

// Sample testimonial data
const sampleTestimonial: TestimonialData = {
  id: '1',
  name: 'Sarah Johnson',
  title: 'Digital Nomad',
  company: 'Remote Tech Solutions',
  avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
  content: 'Amazing service and beautiful properties. The team went above and beyond to help us find the perfect villa for our 3-month stay in Bali. Highly recommended!',
  rating: 5,
  location: 'Canggu, Bali',
  date: '2024-01-15',
  propertyType: 'Villa',
  featured: false,
};

const sampleTestimonials: TestimonialData[] = [
  sampleTestimonial,
  {
    id: '2',
    name: 'Michael Chen',
    title: 'Entrepreneur',
    company: 'StartupCo',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    content: 'Found the perfect investment property with their help. The ROI has exceeded expectations and the property management service is excellent.',
    rating: 5,
    location: 'Seminyak, Bali',
    date: '2024-01-10',
    propertyType: 'Apartment',
  },
  {
    id: '3',
    name: 'Emma Rodriguez',
    title: 'Travel Blogger',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    content: 'Professional service and excellent property selection. They understood exactly what we were looking for and delivered beyond our expectations.',
    rating: 4,
    location: 'Ubud, Bali',
    date: '2024-01-05',
    propertyType: 'Villa',
  },
  {
    id: '4',
    name: 'David Kim',
    title: 'Software Engineer',
    company: 'TechCorp',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    content: 'The rental process was smooth and transparent. Great communication throughout and the property was exactly as described.',
    rating: 5,
    location: 'Canggu, Bali',
    date: '2023-12-28',
    propertyType: 'House',
  },
  {
    id: '5',
    name: 'Lisa Thompson',
    title: 'Marketing Director',
    company: 'Creative Agency',
    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
    content: 'Exceptional customer service from start to finish. They made our relocation to Bali stress-free and enjoyable.',
    rating: 5,
    location: 'Sanur, Bali',
    date: '2023-12-20',
    propertyType: 'Apartment',
  },
];

const testimonialWithoutAvatar: TestimonialData = {
  id: '6',
  name: 'Anonymous Client',
  title: 'Business Owner',
  content: 'Great experience working with this team. Professional, reliable, and results-oriented.',
  rating: 4,
  location: 'Bali',
  date: '2024-01-01',
};

const featuredTestimonial: TestimonialData = {
  ...sampleTestimonial,
  featured: true,
  content: 'This company transformed our Bali property search experience. Their deep local knowledge, professional approach, and genuine care for clients sets them apart. We found our dream villa within days!',
};

// Basic Stories
export const Default: Story = {
  args: {
    testimonial: sampleTestimonial,
  },
};

export const WithoutRating: Story = {
  args: {
    testimonial: sampleTestimonial,
    showRating: false,
  },
};

export const WithoutAvatar: Story = {
  args: {
    testimonial: testimonialWithoutAvatar,
  },
};

export const WithDate: Story = {
  args: {
    testimonial: sampleTestimonial,
    showDate: true,
  },
};

// Variant Styles
export const CardVariant: Story = {
  args: {
    testimonial: sampleTestimonial,
    variant: 'card',
  },
};

export const QuoteVariant: Story = {
  args: {
    testimonial: sampleTestimonial,
    variant: 'quote',
  },
};

export const MinimalVariant: Story = {
  args: {
    testimonial: sampleTestimonial,
    variant: 'minimal',
  },
};

export const FeaturedVariant: Story = {
  args: {
    testimonial: featuredTestimonial,
    variant: 'featured',
  },
};

// Multiple Testimonials Grid
export const MultipleTestimonials: Story = {
  args: {
    testimonials: sampleTestimonials.slice(0, 3),
  },
};

export const TestimonialGrid: Story = {
  args: {
    testimonials: sampleTestimonials,
    className: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  },
};

// Carousel Mode
export const CarouselMode: Story = {
  args: {
    testimonials: sampleTestimonials,
    carousel: true,
  },
};

export const AutoPlayCarousel: Story = {
  args: {
    testimonials: sampleTestimonials,
    carousel: true,
    autoPlay: 3000,
  },
};

// Specialized Components
export const CardComponent: Story = {
  render: () => (
    <TestimonialCard testimonial={sampleTestimonial} />
  ),
};

export const QuoteComponent: Story = {
  render: () => (
    <TestimonialQuote testimonial={sampleTestimonial} />
  ),
};

export const MinimalComponent: Story = {
  render: () => (
    <TestimonialMinimal testimonial={sampleTestimonial} />
  ),
};

export const FeaturedComponent: Story = {
  render: () => (
    <TestimonialFeatured testimonial={featuredTestimonial} />
  ),
};

export const CarouselComponent: Story = {
  render: () => (
    <TestimonialCarousel testimonials={sampleTestimonials} autoPlay={4000} />
  ),
};

// Rating Examples
export const FiveStarRating: Story = {
  args: {
    testimonial: { ...sampleTestimonial, rating: 5 },
    variant: 'card',
  },
};

export const FourStarRating: Story = {
  args: {
    testimonial: { ...sampleTestimonial, rating: 4 },
    variant: 'card',
  },
};

export const ThreeStarRating: Story = {
  args: {
    testimonial: { ...sampleTestimonial, rating: 3 },
    variant: 'card',
  },
};

// Real Estate Examples
export const RentalTestimonial: Story = {
  args: {
    testimonial: {
      ...sampleTestimonial,
      content: 'The rental process was seamless from start to finish. The property exceeded our expectations and the support team was always available when needed.',
      propertyType: 'Villa Rental',
    },
    variant: 'quote',
    showDate: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Testimonial from a satisfied rental client.',
      },
    },
  },
};

export const InvestmentTestimonial: Story = {
  args: {
    testimonial: {
      ...sampleTestimonials[1],
      content: 'Excellent investment guidance and property management. Our ROI has been consistently above market average thanks to their expertise.',
    },
    variant: 'featured',
    showDate: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Testimonial from an investment property client.',
      },
    },
  },
};

export const PropertyManagementTestimonials: Story = {
  args: {
    testimonials: sampleTestimonials,
    carousel: true,
    variant: 'card',
    autoPlay: 5000,
  },
  parameters: {
    docs: {
      description: {
        story: 'Carousel of testimonials for property management services.',
      },
    },
  },
};

// Layout Examples
export const TestimonialSection: Story = {
  render: () => (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">What Our Clients Say</h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Don't just take our word for it. Here's what our satisfied clients have to say about their experience.
        </p>
      </div>
      <TestimonialCarousel testimonials={sampleTestimonials} variant="quote" />
    </div>
  ),
};

// Edge Cases
export const SingleTestimonialCarousel: Story = {
  args: {
    testimonials: [sampleTestimonial],
    carousel: true,
  },
};

export const EmptyTestimonials: Story = {
  args: {
    testimonials: [],
  },
};

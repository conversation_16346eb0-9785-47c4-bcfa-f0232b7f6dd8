/**
 * Gallery Component
 * 
 * Image gallery with lightbox, thumbnails, and responsive design.
 * Supports multiple layouts and interaction modes.
 */

'use client';

import React, { useState, useCallback, useEffect } from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { OptimizedGalleryImage } from '@/components/ui/OptimizedImage';

// Import Modal component directly for better test compatibility
import { Modal } from '@/components/ui/Modal';

export interface GalleryImage {
  /** Image source URL */
  src: string;
  /** Image alt text */
  alt: string;
  /** Image title */
  title?: string;
  /** Image caption */
  caption?: string;
  /** Thumbnail URL (optional, will use src if not provided) */
  thumbnail?: string;
  /** Image width */
  width?: number;
  /** Image height */
  height?: number;
}

export interface GalleryProps {
  /** Array of images to display */
  images: GalleryImage[];
  /** Gallery layout */
  layout?: 'grid' | 'masonry' | 'carousel' | 'thumbnails';
  /** Number of columns for grid layout */
  columns?: 1 | 2 | 3 | 4 | 5 | 6;
  /** Gap between images */
  gap?: 'sm' | 'md' | 'lg';
  /** Whether to enable lightbox */
  lightbox?: boolean;
  /** Whether to show thumbnails in lightbox */
  showThumbnails?: boolean;
  /** Whether to show captions */
  showCaptions?: boolean;
  /** Aspect ratio for images */
  aspectRatio?: 'square' | '4/3' | '16/9' | '3/2' | 'auto';
  /** Custom className */
  className?: string;
  /** Callback when image is clicked */
  onImageClick?: (image: GalleryImage, index: number) => void;
  /** Loading behavior */
  loading?: 'lazy' | 'eager';
  /** Image quality */
  quality?: number;
}

// Layout styles
const layoutStyles = {
  grid: 'grid',
  masonry: 'columns-1 sm:columns-2 lg:columns-3 xl:columns-4 space-y-4',
  carousel: 'flex overflow-x-auto snap-x snap-mandatory',
  thumbnails: 'flex flex-col',
};

// Column styles for grid layout
const columnStyles = {
  1: 'grid-cols-1',
  2: 'grid-cols-1 sm:grid-cols-2',
  3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
  4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  5: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5',
  6: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6',
};

// Gap styles
const gapStyles = {
  sm: 'gap-2',
  md: 'gap-4',
  lg: 'gap-6',
};

// Aspect ratio styles
const aspectRatioStyles = {
  square: 'aspect-square',
  '4/3': 'aspect-[4/3]',
  '16/9': 'aspect-video',
  '3/2': 'aspect-[3/2]',
  auto: '',
};

export const Gallery: React.FC<GalleryProps> = React.memo(({
  images,
  layout = 'grid',
  columns = 3,
  gap = 'md',
  lightbox = true,
  showThumbnails = true,
  showCaptions = true,
  aspectRatio = '4/3',
  className,
  onImageClick,
  loading = 'lazy',
  quality = 85,
}) => {
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Handle image click
  const handleImageClick = useCallback((image: GalleryImage, index: number) => {
    if (onImageClick) {
      onImageClick(image, index);
    }
    
    if (lightbox) {
      setCurrentImageIndex(index);
      setLightboxOpen(true);
    }
  }, [onImageClick, lightbox]);

  // Navigate lightbox
  const navigateLightbox = useCallback((direction: 'prev' | 'next') => {
    if (!images || images.length === 0) return;

    setCurrentImageIndex((current) => {
      if (direction === 'prev') {
        return current > 0 ? current - 1 : images.length - 1;
      } else {
        return current < images.length - 1 ? current + 1 : 0;
      }
    });
  }, [images]);

  // Keyboard navigation
  useEffect(() => {
    if (!lightboxOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowLeft':
          navigateLightbox('prev');
          break;
        case 'ArrowRight':
          navigateLightbox('next');
          break;
        case 'Escape':
          setLightboxOpen(false);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [lightboxOpen, navigateLightbox]);

  // Get container classes
  const containerClasses = cn(
    layoutStyles[layout],
    layout === 'grid' && columnStyles[columns],
    layout !== 'carousel' && gapStyles[gap],
    layout === 'carousel' && 'space-x-4 pb-4'
  );

  // Render single image
  const renderImage = (image: GalleryImage, index: number) => {
    const imageClasses = cn(
      'relative overflow-hidden rounded-lg cursor-pointer transition-transform duration-300 hover:scale-105',
      layout === 'masonry' && 'break-inside-avoid mb-4',
      layout === 'carousel' && 'flex-shrink-0 snap-center',
      aspectRatio !== 'auto' && aspectRatioStyles[aspectRatio]
    );

    return (
      <div
        key={index}
        className={imageClasses}
        onClick={() => handleImageClick(image, index)}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleImageClick(image, index);
          }
        }}
        role="button"
        tabIndex={0}
        aria-label={`View image: ${image.alt}`}
      >
        <OptimizedGalleryImage
          src={image.thumbnail || image.src}
          alt={image.alt}
          width={image.width || 800}
          height={image.height || 600}
          className={cn(
            'w-full h-full object-cover transition-opacity duration-300 hover:opacity-90',
            aspectRatio === 'auto' ? 'h-auto' : 'absolute inset-0'
          )}
          lazy={loading === 'lazy'}
          quality={quality}
          fill={aspectRatio !== 'auto'}
          objectFit="cover"
        />
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors duration-300" />
        
        {/* Caption overlay */}
        {showCaptions && (image.title || image.caption) && (
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
            {image.title && (
              <h3 className="text-white font-semibold text-sm mb-1">
                {image.title}
              </h3>
            )}
            {image.caption && (
              <p className="text-white/90 text-xs">
                {image.caption}
              </p>
            )}
          </div>
        )}
      </div>
    );
  };

  // Render thumbnails layout
  const renderThumbnailsLayout = () => {
    const [mainImage, ...thumbnails] = images;
    
    return (
      <div className="space-y-4">
        {/* Main image */}
        <div className="relative">
          {renderImage(mainImage, 0)}
        </div>
        
        {/* Thumbnails */}
        {thumbnails.length > 0 && (
          <div className="grid grid-cols-4 sm:grid-cols-6 lg:grid-cols-8 gap-2">
            {thumbnails.map((image, index) => (
              <div key={index + 1} className="aspect-square">
                {renderImage(image, index + 1)}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  // Render lightbox
  const renderLightbox = () => {
    if (!lightboxOpen || !images[currentImageIndex]) return null;

    const currentImage = images[currentImageIndex];

    return (
      <Modal
        isOpen={lightboxOpen}
        onClose={() => setLightboxOpen(false)}
        size="full"
        className="bg-black/95"
      >
        <div className="relative w-full h-full flex items-center justify-center p-4">
          {/* Main image */}
          <div className="relative max-w-full max-h-full">
            <Image
              src={currentImage.src}
              alt={currentImage.alt}
              width={currentImage.width || 1200}
              height={currentImage.height || 800}
              className="max-w-full max-h-full object-contain"
              quality={95}
            />
          </div>

          {/* Navigation buttons */}
          {images.length > 1 && (
            <>
              <button
                onClick={() => navigateLightbox('prev')}
                className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-3 rounded-full transition-colors"
                aria-label="Previous image"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              
              <button
                onClick={() => navigateLightbox('next')}
                className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-3 rounded-full transition-colors"
                aria-label="Next image"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </>
          )}

          {/* Close button */}
          <button
            onClick={() => setLightboxOpen(false)}
            className="absolute top-4 right-4 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-colors"
            aria-label="Close lightbox"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* Image info */}
          {(currentImage.title || currentImage.caption) && (
            <div className="absolute bottom-4 left-4 right-4 bg-black/50 text-white p-4 rounded-lg">
              {currentImage.title && (
                <h3 className="font-semibold mb-1">{currentImage.title}</h3>
              )}
              {currentImage.caption && (
                <p className="text-sm text-white/90">{currentImage.caption}</p>
              )}
            </div>
          )}

          {/* Thumbnails */}
          {showThumbnails && images.length > 1 && (
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2 bg-black/50 p-2 rounded-lg">
              {images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentImageIndex(index)}
                  className={cn(
                    'w-12 h-12 rounded overflow-hidden transition-opacity',
                    index === currentImageIndex ? 'opacity-100 ring-2 ring-white' : 'opacity-60 hover:opacity-80'
                  )}
                >
                  <Image
                    src={image.thumbnail || image.src}
                    alt={image.alt}
                    width={48}
                    height={48}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          )}

          {/* Image counter */}
          <div className="absolute top-4 left-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
            {currentImageIndex + 1} / {images.length}
          </div>
        </div>
      </Modal>
    );
  };

  if (!images || images.length === 0) {
    return null;
  }

  return (
    <div className={cn("gallery", className)}>
      {/* Gallery content */}
      {layout === 'thumbnails' ? (
        renderThumbnailsLayout()
      ) : (
        <div className={containerClasses}>
          {images.map((image, index) => renderImage(image, index))}
        </div>
      )}

      {/* Lightbox */}
      {lightbox && renderLightbox()}
    </div>
  );
});

Gallery.displayName = 'Gallery';

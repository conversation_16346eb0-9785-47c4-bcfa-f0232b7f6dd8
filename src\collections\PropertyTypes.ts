import { CollectionConfig } from 'payload'
import { lexicalEditor } from '@payloadcms/richtext-lexical'

export const PropertyTypes: CollectionConfig = {
  slug: 'property-types',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'slug', 'category', 'updatedAt'],
  },
  access: {
    read: () => true, // Allow public read access for API testing
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Property Type Name',
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      label: 'URL Slug',
      admin: {
        description: 'Used in URLs (e.g., /property-types/villa/)',
      },
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        { label: 'Rental', value: 'rental' },
        { label: 'Sale', value: 'sale' },
        { label: 'Both', value: 'both' },
      ],
      defaultValue: 'both',
    },
    {
      name: 'description',
      type: 'richText',
      editor: lexicalEditor({}),
      required: true,
      label: 'Property Type Overview',
      admin: {
        description: 'Comprehensive overview of this property type (500+ words)',
      },
    },
    // STORY-013: Enhanced Property Type Content
    {
      name: 'detailedDescription',
      type: 'richText',
      editor: lexicalEditor({}),
      label: 'Detailed Description',
      admin: {
        description: 'In-depth description covering all aspects (1000+ words)',
      },
    },
    {
      name: 'typicalFeatures',
      type: 'array',
      label: 'Typical Features',
      fields: [
        {
          name: 'feature',
          type: 'text',
          required: true,
        },
        {
          name: 'description',
          type: 'textarea',
          admin: {
            description: 'Optional detailed description of this feature',
          },
        },
      ],
      admin: {
        description: 'Standard features and amenities for this property type',
      },
    },
    {
      name: 'priceRanges',
      type: 'group',
      label: 'Price Ranges',
      fields: [
        {
          name: 'rental',
          type: 'group',
          label: 'Rental Prices',
          fields: [
            {
              name: 'shortTerm',
              type: 'text',
              label: 'Short-term (per night)',
              admin: {
                description: 'e.g., "$50-200/night"',
              },
            },
            {
              name: 'longTerm',
              type: 'text',
              label: 'Long-term (per month)',
              admin: {
                description: 'e.g., "$800-3000/month"',
              },
            },
          ],
        },
        {
          name: 'purchase',
          type: 'group',
          label: 'Purchase Prices',
          fields: [
            {
              name: 'freehold',
              type: 'text',
              label: 'Freehold Price Range',
              admin: {
                description: 'e.g., "$150k-800k"',
              },
            },
            {
              name: 'leasehold',
              type: 'text',
              label: 'Leasehold Price Range',
              admin: {
                description: 'e.g., "$80k-400k"',
              },
            },
          ],
        },
      ],
    },
    {
      name: 'sizeRanges',
      type: 'group',
      label: 'Size Ranges',
      fields: [
        {
          name: 'bedroomsMin',
          type: 'number',
          label: 'Min Bedrooms',
        },
        {
          name: 'bedroomsMax',
          type: 'number',
          label: 'Max Bedrooms',
        },
        {
          name: 'areaMin',
          type: 'number',
          label: 'Min Area (sqm)',
        },
        {
          name: 'areaMax',
          type: 'number',
          label: 'Max Area (sqm)',
        },
      ],
    },

    {
      name: 'targetAudience',
      type: 'array',
      label: 'Target Audience',
      fields: [
        {
          name: 'audience',
          type: 'select',
          options: [
            { label: 'Digital Nomads', value: 'digital-nomads' },
            { label: 'Expat Families', value: 'expat-families' },
            { label: 'Property Investors', value: 'property-investors' },
            { label: 'Retirees', value: 'retirees' },
            { label: 'Surfers', value: 'surfers' },
            { label: 'Budget Travelers', value: 'budget-travelers' },
          ],
          required: true,
        },
      ],
    },
    {
      name: 'rentalTerms',
      type: 'richText',
      editor: lexicalEditor({}),
      label: 'Rental Terms & Conditions',
      admin: {
        description: 'Typical rental terms, deposits, utilities, etc.',
      },
    },
    {
      name: 'purchaseProcess',
      type: 'richText',
      editor: lexicalEditor({}),
      label: 'Purchase Process',
      admin: {
        description: 'Buying process, legal requirements, ownership types',
      },
    },
    {
      name: 'availableLocations',
      type: 'relationship',
      relationTo: 'locations',
      hasMany: true,
      label: 'Available in Locations',
      admin: {
        description: 'Locations where this property type is commonly found',
      },
    },
    {
      name: 'featuredImage',
      type: 'upload',
      relationTo: 'media',
      label: 'Featured Image',
    },
    {
      name: 'gallery',
      type: 'array',
      label: 'Image Gallery',
      fields: [
        {
          name: 'image',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'caption',
          type: 'text',
        },
      ],
    },
    {
      name: 'faq',
      type: 'array',
      label: 'Property Type FAQ',
      fields: [
        {
          name: 'question',
          type: 'text',
          required: true,
        },
        {
          name: 'answer',
          type: 'richText',
          editor: lexicalEditor({}),
          required: true,
        },
      ],
    },
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Auto-generate slug from name if not provided
        if (data.name && !data.slug) {
          data.slug = data.name
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '')
        }
        return data
      },
    ],
  },
}

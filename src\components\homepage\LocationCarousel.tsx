/**
 * Location Carousel Component
 * Bali Property Scout Website
 * 
 * Interactive carousel showcasing high-priority location cards
 * with enhanced animations and responsive design.
 */

'use client';

import React from 'react';
import { InteractiveLocationCard } from './InteractiveLocationCard';

export interface LocationCarouselProps {
  locations: Array<{
    location: string;
    locationName: string;
    priority: string;
    description: string;
    priceRange: string;
  }>;
}

export const LocationCarousel: React.FC<LocationCarouselProps> = ({ locations }) => {
  // Enhanced location images with higher quality
  const locationImages = {
    'canggu': 'https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?w=1200&auto=format&fit=crop&q=90',
    'ubud': 'https://images.unsplash.com/photo-1518548419970-58e3b4079ab2?w=1200&auto=format&fit=crop&q=90',
    'seminyak': 'https://images.unsplash.com/photo-1540541338287-41700207dee6?w=1200&auto=format&fit=crop&q=90',
    'sanur': 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=1200&auto=format&fit=crop&q=90',
    'jimbaran': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1200&auto=format&fit=crop&q=90',
    'nusa-dua': 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=1200&auto=format&fit=crop&q=90',
  };

  // Enhanced features for each location type
  const getLocationFeatures = (location: string): string[] => {
    const baseFeatures = ['Private Pool', 'High-Speed WiFi', 'Air Conditioning', '24/7 Security'];
    
    const locationSpecificFeatures = {
      'canggu': [...baseFeatures, 'Surf Nearby', 'Beach Access', 'Coworking Spaces', 'Trendy Cafes'],
      'ubud': [...baseFeatures, 'Rice Field Views', 'Yoga Studios', 'Organic Gardens', 'Cultural Sites'],
      'seminyak': [...baseFeatures, 'Beach Clubs', 'Fine Dining', 'Shopping', 'Nightlife'],
      'sanur': [...baseFeatures, 'Family Friendly', 'Calm Beach', 'Traditional Market', 'Cycling Paths'],
      'jimbaran': [...baseFeatures, 'Seafood Restaurants', 'Sunset Views', 'Golf Course', 'Luxury Resorts'],
      'nusa-dua': [...baseFeatures, 'Resort Area', 'Water Sports', 'Convention Center', 'Luxury Shopping'],
    };

    return locationSpecificFeatures[location as keyof typeof locationSpecificFeatures] || baseFeatures;
  };

  return (
    <section id="locations-section" className="section-spacing bg-gradient-to-br from-neutral-50 via-white to-neutral-50">
      <div className="max-w-7xl mx-auto container-padding">
        {/* Clean Section Header */}
        <div className="text-center mb-16">
          <div
            className="inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-semibold mb-6"
            style={{
              backgroundColor: 'var(--brand-primary-bg)',
              color: 'var(--brand-primary)'
            }}
          >
            Premium Locations
          </div>

          <h2 className="heading-2 text-3xl md:text-5xl font-bold mb-6 leading-tight" style={{ color: 'var(--neutral-900)' }}>
            High Priority Bali Locations
          </h2>

          <p className="body-text-large text-lg max-w-3xl mx-auto leading-relaxed" style={{ color: 'var(--neutral-600)' }}>
            Discover our most sought-after properties in Bali's premium locations, carefully selected for investment potential and lifestyle appeal.
          </p>
        </div>

        {/* Interactive Location Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {locations.map((combo, index) => (
            <InteractiveLocationCard
              key={`${combo.location}-${index}`}
              location={combo.location}
              locationName={combo.locationName}
              priority={combo.priority}
              description={combo.description}
              priceRange={combo.priceRange}
              image={locationImages[combo.location as keyof typeof locationImages] || locationImages.canggu}
              features={getLocationFeatures(combo.location)}
              index={index}
            />
          ))}
        </div>

        {/* Single Strong CTA Section */}
        <div className="text-center">
          <div className="card max-w-2xl mx-auto">
            <h3 className="heading-3 text-xl font-bold mb-4" style={{ color: 'var(--neutral-900)' }}>
              Can't Find Your Perfect Location?
            </h3>
            <p className="body-text mb-6" style={{ color: 'var(--neutral-600)' }}>
              Our AI-powered search helps you discover properties that match your exact preferences and budget.
            </p>

            {/* Primary CTA with highest visual weight */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => {
                  window.location.href = 'https://app.balipropertyscout.com?query=custom-location-search';
                }}
                className="btn-primary text-lg font-semibold px-8 py-4 min-h-[56px]"
                style={{
                  backgroundColor: 'var(--brand-primary)',
                  borderColor: 'var(--brand-primary)',
                  color: 'var(--neutral-white)'
                }}
              >
                Start AI Property Search
              </button>

              {/* Secondary action - less prominent */}
              <button
                onClick={() => {
                  const allLocationsSection = document.getElementById('all-locations-section');
                  if (allLocationsSection) {
                    allLocationsSection.scrollIntoView({ behavior: 'smooth' });
                  } else {
                    window.location.href = '/locations';
                  }
                }}
                className="btn-secondary text-base font-medium px-6 py-3"
                style={{
                  borderColor: 'var(--brand-primary)',
                  color: 'var(--brand-primary)'
                }}
              >
                View All Locations
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LocationCarousel;

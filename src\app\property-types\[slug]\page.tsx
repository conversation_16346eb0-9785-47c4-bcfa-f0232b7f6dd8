// STORY-013: Individual Property Type Pages
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { getPropertyTypeBySlug, getPropertyTypes } from '@/lib/payload'
import { richTextToPlainText } from '@/lib/richtext-utils'
// AI components temporarily disabled

interface PropertyTypePageProps {
  params: Promise<{
    slug: string
  }>
}

export async function generateStaticParams() {
  // During build, return static params to avoid database connection issues
  if (process.env.NODE_ENV === 'production' && !process.env.DATABASE_URL?.includes('supabase')) {
    console.log('🔧 Using static params for property-types build (no database connection)')
    return [
      { slug: 'villa' },
      { slug: 'guesthouse' },
      { slug: 'apartment' },
      { slug: 'land' },
    ]
  }

  try {
    const propertyTypes = await getPropertyTypes()
    return propertyTypes.map((propertyType: any) => ({
      slug: propertyType.slug,
    }))
  } catch (error) {
    console.error('Error generating static params for property types:', error)
    // Return fallback params if database is not available
    return [
      { slug: 'villa' },
      { slug: 'guesthouse' },
      { slug: 'apartment' },
      { slug: 'land' },
    ]
  }
}

export async function generateMetadata({ params }: PropertyTypePageProps): Promise<Metadata> {
  const { slug } = await params
  const propertyType = await getPropertyTypeBySlug(slug)
  
  if (!propertyType) {
    return {
      title: 'Property Type Not Found',
    }
  }

  const metaTitle = propertyType.seoContent?.metaTitle || 
    `${propertyType.name} Properties in Bali - Rental & Sale | Bali Real Estate`
  
  const metaDescription = propertyType.seoContent?.metaDescription ||
    richTextToPlainText(propertyType.description).substring(0, 160) ||
    `Discover ${propertyType.name.toLowerCase()} properties in Bali. Expert guidance for rentals, sales, and investment opportunities.`

  const keywords = propertyType.seoContent?.targetKeywords?.map((k: any) => k.keyword).join(', ') ||
    `${propertyType.name} Bali, ${propertyType.name} rental, ${propertyType.name} for sale, Bali property`

  return {
    title: metaTitle,
    description: metaDescription,
    keywords: keywords,
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      type: 'website',
    },
  }
}

export default async function PropertyTypePage({ params }: PropertyTypePageProps) {
  const { slug } = await params
  const propertyType = await getPropertyTypeBySlug(slug)

  if (!propertyType) {
    notFound()
  }

  return (
    <main className="min-h-screen bg-white">
      {/* AI Context temporarily disabled */}
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-emerald-600 to-teal-600 text-white py-20">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center mb-4">
                <div className="w-16 h-16 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-4">
                  {propertyType.slug === 'villa' && (
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                  )}
                  {propertyType.slug === 'guesthouse' && (
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  )}
                  {propertyType.slug === 'apartment' && (
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 8h1m-1-4h1m4 4h1m-1-4h1" />
                    </svg>
                  )}
                </div>
                <div>
                  <h1 className="text-4xl md:text-5xl font-bold">{propertyType.name} Properties</h1>
                  <span className="text-emerald-200 text-lg capitalize">{propertyType.category} Options Available</span>
                </div>
              </div>
              
              <p className="text-xl text-emerald-100 mb-8 leading-relaxed">
                {richTextToPlainText(propertyType.description)}
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="/contact"
                  className="bg-white text-emerald-600 px-8 py-3 rounded-lg font-semibold hover:bg-emerald-50 transition-colors text-center"
                >
                  Get Expert Guidance
                </Link>
                <Link
                  href="/locations"
                  className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-emerald-600 transition-colors text-center"
                >
                  Browse Locations
                </Link>
              </div>
            </div>

            {/* Price Range Card */}
            <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-6">
              <h3 className="text-xl font-semibold text-white mb-4">Price Ranges</h3>
              
              {propertyType.priceRanges?.rental && (
                <div className="mb-4">
                  <h4 className="text-emerald-200 font-medium mb-2">Rental Options</h4>
                  {propertyType.priceRanges.rental.shortTerm && (
                    <div className="flex justify-between text-white mb-1">
                      <span>Short-term:</span>
                      <span className="font-semibold">{propertyType.priceRanges.rental.shortTerm}</span>
                    </div>
                  )}
                  {propertyType.priceRanges.rental.longTerm && (
                    <div className="flex justify-between text-white">
                      <span>Long-term:</span>
                      <span className="font-semibold">{propertyType.priceRanges.rental.longTerm}</span>
                    </div>
                  )}
                </div>
              )}

              {propertyType.priceRanges?.purchase && (
                <div>
                  <h4 className="text-emerald-200 font-medium mb-2">Purchase Options</h4>
                  {propertyType.priceRanges.purchase.freehold && (
                    <div className="flex justify-between text-white mb-1">
                      <span>Freehold:</span>
                      <span className="font-semibold">{propertyType.priceRanges.purchase.freehold}</span>
                    </div>
                  )}
                  {propertyType.priceRanges.purchase.leasehold && (
                    <div className="flex justify-between text-white">
                      <span>Leasehold:</span>
                      <span className="font-semibold">{propertyType.priceRanges.purchase.leasehold}</span>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Detailed Description */}
      {propertyType.detailedDescription && (
        <section className="py-16 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
              About {propertyType.name} Properties in Bali
            </h2>
            <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
              <p>{richTextToPlainText(propertyType.detailedDescription)}</p>
            </div>
          </div>
        </section>
      )}

      {/* Features Section */}
      {propertyType.typicalFeatures && propertyType.typicalFeatures.length > 0 && (
        <section className="py-16 bg-white">
          <div className="max-w-6xl mx-auto px-4">
            <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">
              Typical Features & Amenities
            </h2>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {propertyType.typicalFeatures.map((feature: any, index: number) => (
                <div key={index} className="bg-gray-50 rounded-lg p-6">
                  <div className="flex items-start">
                    <div className="w-3 h-3 bg-emerald-500 rounded-full mt-2 mr-4 flex-shrink-0"></div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">{feature.feature}</h3>
                      {feature.description && (
                        <p className="text-gray-600 text-sm">{feature.description}</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Target Audience */}
      {propertyType.targetAudience && propertyType.targetAudience.length > 0 && (
        <section className="py-16 bg-emerald-50">
          <div className="max-w-4xl mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-8">
              Perfect For
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-3xl mx-auto">
              {propertyType.targetAudience.map((item: any, index: number) => (
                <div key={index} className="bg-white rounded-lg p-4 shadow-sm">
                  <span className="text-emerald-600 font-medium capitalize">
                    {item.audience?.replace('-', ' ') || item}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Investment Analysis */}
      {propertyType.investmentAnalysis && (
        <section className="py-16 bg-white">
          <div className="max-w-6xl mx-auto px-4">
            <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">
              Investment Potential
            </h2>
            
            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-lg p-8">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">Key Metrics</h3>
                
                {propertyType.investmentAnalysis.rentalYield && (
                  <div className="flex justify-between items-center mb-4 pb-4 border-b border-emerald-200">
                    <span className="text-gray-700">Expected Rental Yield:</span>
                    <span className="font-semibold text-emerald-600">{propertyType.investmentAnalysis.rentalYield}</span>
                  </div>
                )}
                
                {propertyType.investmentAnalysis.appreciationRate && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-700">Appreciation Rate:</span>
                    <span className="font-semibold text-emerald-600">{propertyType.investmentAnalysis.appreciationRate}</span>
                  </div>
                )}
              </div>

              {propertyType.investmentAnalysis.investmentHighlights && (
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-6">Investment Highlights</h3>
                  <div className="space-y-4">
                    {propertyType.investmentAnalysis.investmentHighlights.map((highlight: any, index: number) => (
                      <div key={index} className="flex items-start">
                        <div className="w-2 h-2 bg-emerald-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <span className="text-gray-700">{highlight.highlight}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </section>
      )}

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-emerald-600 to-teal-600 text-white">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">
            Ready to Explore {propertyType.name} Options?
          </h2>
          <p className="text-xl text-emerald-100 mb-8">
            Get personalized recommendations and expert guidance for your {propertyType.name.toLowerCase()} search in Bali
          </p>
          <Link
            href={`/chatbot?query=search-${propertyType.slug}-in-bali`}
            className="inline-flex items-center bg-white text-emerald-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-emerald-50 transition-colors"
          >
            Start Your Search
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </section>
    </main>
  )
}

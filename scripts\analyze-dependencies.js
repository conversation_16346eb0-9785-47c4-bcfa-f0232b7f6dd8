#!/usr/bin/env node

/**
 * Dependency Analysis Script
 * 
 * Analyzes package.json dependencies and identifies optimization opportunities
 */

const fs = require('fs');
const path = require('path');

// Read package.json
const packageJsonPath = path.join(process.cwd(), 'package.json');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

// Known heavy packages and their alternatives
const HEAVY_PACKAGES = {
  'moment': { size: '67kB', alternative: 'date-fns (11kB)' },
  'lodash': { size: '71kB', alternative: 'lodash-es + tree shaking' },
  'axios': { size: '15kB', alternative: 'native fetch' },
  'react-router-dom': { size: '32kB', alternative: 'Next.js routing' },
  'styled-components': { size: '51kB', alternative: 'Tailwind CSS' },
  'material-ui': { size: '300kB+', alternative: 'Headless UI' },
  'antd': { size: '500kB+', alternative: 'Headless UI' },
  'chart.js': { size: '200kB+', alternative: 'recharts (smaller)' },
  'three': { size: '600kB+', alternative: 'lazy load' },
  'monaco-editor': { size: '1MB+', alternative: 'lazy load' },
};

// Packages that should be lazy loaded
const LAZY_LOAD_CANDIDATES = [
  'chart.js',
  'recharts',
  'three',
  'monaco-editor',
  'codemirror',
  'react-pdf',
  'react-player',
  'react-map-gl',
  'mapbox-gl',
];

// Analyze dependencies
function analyzeDependencies() {
  const analysis = {
    total: 0,
    production: 0,
    development: 0,
    heavy: [],
    lazyLoadCandidates: [],
    recommendations: [],
  };

  const allDeps = {
    ...packageJson.dependencies,
    ...packageJson.devDependencies,
  };

  analysis.total = Object.keys(allDeps).length;
  analysis.production = Object.keys(packageJson.dependencies || {}).length;
  analysis.development = Object.keys(packageJson.devDependencies || {}).length;

  // Check for heavy packages
  Object.keys(allDeps).forEach((dep) => {
    if (HEAVY_PACKAGES[dep]) {
      analysis.heavy.push({
        name: dep,
        ...HEAVY_PACKAGES[dep],
        version: allDeps[dep],
      });
    }

    if (LAZY_LOAD_CANDIDATES.includes(dep)) {
      analysis.lazyLoadCandidates.push({
        name: dep,
        version: allDeps[dep],
        reason: 'Large package that should be lazy loaded',
      });
    }
  });

  // Generate recommendations
  if (analysis.heavy.length > 0) {
    analysis.recommendations.push({
      type: 'REPLACE_HEAVY_PACKAGES',
      priority: 'HIGH',
      impact: 'Bundle size reduction',
      packages: analysis.heavy,
    });
  }

  if (analysis.lazyLoadCandidates.length > 0) {
    analysis.recommendations.push({
      type: 'LAZY_LOAD_PACKAGES',
      priority: 'MEDIUM',
      impact: 'Initial load time improvement',
      packages: analysis.lazyLoadCandidates,
    });
  }

  // Check for duplicate functionality
  const duplicates = findDuplicateFunctionality(allDeps);
  if (duplicates.length > 0) {
    analysis.recommendations.push({
      type: 'REMOVE_DUPLICATES',
      priority: 'MEDIUM',
      impact: 'Bundle size reduction',
      packages: duplicates,
    });
  }

  return analysis;
}

function findDuplicateFunctionality(deps) {
  const duplicates = [];
  const depNames = Object.keys(deps);

  // Check for common duplicates
  const duplicateGroups = [
    {
      category: 'Date handling',
      packages: ['moment', 'date-fns', 'dayjs'],
    },
    {
      category: 'HTTP client',
      packages: ['axios', 'fetch', 'node-fetch', 'isomorphic-fetch'],
    },
    {
      category: 'Utility library',
      packages: ['lodash', 'underscore', 'ramda'],
    },
    {
      category: 'CSS-in-JS',
      packages: ['styled-components', 'emotion', '@emotion/react'],
    },
    {
      category: 'Form handling',
      packages: ['formik', 'react-hook-form', 'final-form'],
    },
  ];

  duplicateGroups.forEach((group) => {
    const found = group.packages.filter((pkg) => depNames.includes(pkg));
    if (found.length > 1) {
      duplicates.push({
        category: group.category,
        packages: found,
        recommendation: `Keep only one ${group.category} library`,
      });
    }
  });

  return duplicates;
}

function printAnalysis(analysis) {
  console.log('\n📦 DEPENDENCY ANALYSIS REPORT');
  console.log('================================');
  console.log(`📊 Total packages: ${analysis.total}`);
  console.log(`🏭 Production: ${analysis.production}`);
  console.log(`🔧 Development: ${analysis.development}`);

  if (analysis.heavy.length > 0) {
    console.log('\n🚨 HEAVY PACKAGES DETECTED:');
    analysis.heavy.forEach((pkg) => {
      console.log(`  ❌ ${pkg.name} (${pkg.size}) → Consider: ${pkg.alternative}`);
    });
  }

  if (analysis.lazyLoadCandidates.length > 0) {
    console.log('\n⏳ LAZY LOAD CANDIDATES:');
    analysis.lazyLoadCandidates.forEach((pkg) => {
      console.log(`  🔄 ${pkg.name} → ${pkg.reason}`);
    });
  }

  if (analysis.recommendations.length > 0) {
    console.log('\n💡 RECOMMENDATIONS:');
    analysis.recommendations.forEach((rec, index) => {
      console.log(`\n${index + 1}. ${rec.type} (${rec.priority} priority)`);
      console.log(`   Impact: ${rec.impact}`);
      
      if (rec.packages) {
        rec.packages.forEach((pkg) => {
          if (typeof pkg === 'string') {
            console.log(`   - ${pkg}`);
          } else if (pkg.name) {
            console.log(`   - ${pkg.name}${pkg.alternative ? ` → ${pkg.alternative}` : ''}`);
          } else if (pkg.category) {
            console.log(`   - ${pkg.category}: ${pkg.packages.join(', ')}`);
            console.log(`     ${pkg.recommendation}`);
          }
        });
      }
    });
  }

  // Calculate potential savings
  const potentialSavings = analysis.heavy.reduce((total, pkg) => {
    const sizeMatch = pkg.size.match(/(\d+)kB/);
    return total + (sizeMatch ? parseInt(sizeMatch[1]) : 0);
  }, 0);

  if (potentialSavings > 0) {
    console.log(`\n🎯 POTENTIAL BUNDLE SIZE REDUCTION: ~${potentialSavings}kB`);
  }

  console.log('\n📋 NEXT STEPS:');
  console.log('1. Review heavy packages and consider alternatives');
  console.log('2. Implement lazy loading for large packages');
  console.log('3. Remove duplicate functionality');
  console.log('4. Run bundle analyzer after changes');
  console.log('5. Measure performance impact');
}

// Run analysis
const analysis = analyzeDependencies();
printAnalysis(analysis);

// Save detailed report
const reportPath = path.join(process.cwd(), 'performance-reports', 'dependency-analysis.json');
fs.writeFileSync(reportPath, JSON.stringify(analysis, null, 2));
console.log(`\n📄 Detailed report saved: ${reportPath}`);

module.exports = { analyzeDependencies, HEAVY_PACKAGES, LAZY_LOAD_CANDIDATES };

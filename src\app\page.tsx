import { Metadata } from 'next'
import Link from 'next/link'
import { getLocations } from '@/lib/payload'
import { AdvancedSchema } from '@/components/seo/AdvancedSchema'
import { CinematicHero } from '@/components/homepage/CinematicHero'
import { LocationCarousel } from '@/components/homepage/LocationCarousel'
import { CoverageSection } from '@/components/homepage/CoverageSection'

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://balipropertyscout.com'),
  title: {
    default: 'Bali Property Scout | AI-Powered Property Search & Expert Guidance',
    template: '%s | Bali Property Scout'
  },
  description: 'Find your perfect Bali home with AI-powered property search and expert local guidance. Discover premium villas, long-term rentals, and investment opportunities in Canggu, Ubud, Seminyak, and beyond.',
  keywords: [
    'bali property scout',
    'ai property search bali',
    'best areas to live in bali',
    'long term rental bali',
    'bali villa rental',
    'bali property investment',
    'bali digital nomad housing',
    'moving to bali guide',
    'bali property expert',
    'cost of living in bali',
    'bali investment properties',
    'bali property consultation'
  ],
  authors: [{ name: 'Bali Property Scout Team' }],
  creator: 'Bali Property Scout',
  publisher: 'Bali Property Scout',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    title: 'Bali Property Scout - AI-Powered Property Search & Rentals',
    description: 'Find your perfect long-term rental or property in Bali with AI-powered search and expert local guidance. Specializing in premium locations like Canggu, Ubud, and Seminyak.',
    type: 'website',
    locale: 'en_US',
    url: '/',
    siteName: 'Bali Property Scout',
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Bali Property Scout - AI-Powered Premium Villas and Properties',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Bali Real Estate - Long-term Rentals & Property Sales',
    description: 'Find your perfect long-term rental or property in Bali with expert local guidance.',
    images: ['/images/twitter-image.jpg'],
    creator: '@BaliRealEstate',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: '/',
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
}



export default async function HomePage() {
  // Get locations from CMS
  const locations = await getLocations()

  // Create priority combinations from CMS data
  const priorityCombinations = locations.map((location: any) => {
    // Handle price range formatting
    let priceRange = 'Contact for pricing'
    if (location.priceRange?.rentalMin && location.priceRange?.rentalMax) {
      priceRange = `$${location.priceRange.rentalMin}-${location.priceRange.rentalMax}/month`
    }

    return {
      location: location.slug,
      locationName: location.name,
      propertyType: 'villa',
      transactionType: 'long-term-rental',
      priority: location.priority === 'zeer-hoog' ? 'ZEER HOOG' : location.priority === 'hoog' ? 'HOOG' : 'GEMIDDELD',
      description: typeof location.description === 'string' ? location.description : 'Discover this amazing location in Bali',
      priceRange
    }
  })

  // Structured Data for SEO
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "RealEstateAgent",
    "name": "Bali Property Scout",
    "description": "AI-powered real estate services in Bali specializing in long-term rentals and property sales for expats, digital nomads, and investors.",
    "url": process.env.NEXT_PUBLIC_SITE_URL || "https://balipropertyscout.com",
    "logo": `${process.env.NEXT_PUBLIC_SITE_URL || "https://balipropertyscout.com"}/images/logo.png`,
    "image": `${process.env.NEXT_PUBLIC_SITE_URL || "https://balipropertyscout.com"}/images/og-image.jpg`,
    "telephone": "+62-XXX-XXX-XXXX",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "ID",
      "addressRegion": "Bali",
      "addressLocality": "Bali",
      "streetAddress": "Bali, Indonesia"
    },
    "areaServed": [
      {
        "@type": "City",
        "name": "Canggu",
        "addressRegion": "Bali",
        "addressCountry": "Indonesia"
      },
      {
        "@type": "City",
        "name": "Ubud",
        "addressRegion": "Bali",
        "addressCountry": "Indonesia"
      },
      {
        "@type": "City",
        "name": "Seminyak",
        "addressRegion": "Bali",
        "addressCountry": "Indonesia"
      }
    ],
    "serviceType": ["Real Estate Sales", "Property Rental", "Investment Consultation"],
    "priceRange": "$$-$$$"
  }

  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Bali Property Scout",
    "url": process.env.NEXT_PUBLIC_SITE_URL || "https://balipropertyscout.com",
    "description": "Find your perfect long-term rental or property in Bali with AI-powered search and expert local guidance.",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${process.env.NEXT_PUBLIC_SITE_URL || "https://balipropertyscout.com"}/search?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    }
  }

  return (
    <main className="min-h-screen bg-gray-50 pt-20">
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema),
        }}
      />
      {/* Cinematic Hero Section */}
      <CinematicHero />

      {/* Interactive Location Cards */}
      <LocationCarousel locations={priorityCombinations} />

      {/* Interactive Coverage Tags */}
      <CoverageSection />

      {/* Why Choose Bali Property Scout */}
      <section className="py-20 bg-gradient-to-br from-white via-emerald-50/30 to-white">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-emerald-100 text-emerald-700 rounded-full text-sm font-semibold mb-4">
              <span className="text-lg">🤖</span>
              AI-Powered Excellence
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Why Choose
              <span className="block bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                Bali Property Scout
              </span>
            </h2>

            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              Your trusted partner combining AI-powered property discovery with unmatched local expertise
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="group text-center p-8 bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-gray-100">
              <div className="w-20 h-20 bg-gradient-to-br from-emerald-100 to-teal-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <span className="text-3xl">🤖</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-emerald-600 transition-colors">AI-Powered Search</h3>
              <p className="text-gray-600 leading-relaxed">
                Advanced AI algorithms analyze your preferences, budget, and lifestyle to find properties that perfectly match your needs
              </p>
            </div>

            <div className="group text-center p-8 bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-gray-100">
              <div className="w-20 h-20 bg-gradient-to-br from-emerald-100 to-teal-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <span className="text-3xl">🏆</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-emerald-600 transition-colors">Local Expertise</h3>
              <p className="text-gray-600 leading-relaxed">
                Deep knowledge of Bali's neighborhoods, market trends, and hidden gems combined with years of local experience
              </p>
            </div>

            <div className="group text-center p-8 bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-gray-100">
              <div className="w-20 h-20 bg-gradient-to-br from-emerald-100 to-teal-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <span className="text-3xl">🔒</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-emerald-600 transition-colors">Verified Properties</h3>
              <p className="text-gray-600 leading-relaxed">
                All properties are personally inspected, legally verified, and quality-assured for your peace of mind and investment security
              </p>
            </div>
          </div>

          {/* Stats Section */}
          <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-emerald-600 mb-2">500+</div>
              <div className="text-gray-600 font-medium">Happy Clients</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-emerald-600 mb-2">1000+</div>
              <div className="text-gray-600 font-medium">Properties Listed</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-emerald-600 mb-2">95%</div>
              <div className="text-gray-600 font-medium">Success Rate</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-emerald-600 mb-2">24/7</div>
              <div className="text-gray-600 font-medium">AI Support</div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gradient-to-r from-emerald-50 to-teal-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Ready to Find Your Perfect Bali Property?
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                Our local experts are here to help you navigate the Bali real estate market.
                Whether you're looking for a long-term rental, investment property, or your dream home,
                we'll guide you through every step of the process.
              </p>

              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0 w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="ml-3 text-gray-700">Free consultation and market analysis</span>
                </div>

                <div className="flex items-center">
                  <div className="flex-shrink-0 w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="ml-3 text-gray-700">Local expertise and insider knowledge</span>
                </div>

                <div className="flex items-center">
                  <div className="flex-shrink-0 w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="ml-3 text-gray-700">End-to-end support from search to settlement</span>
                </div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-8 border border-white/20 shadow-2xl">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Start Your Bali Journey</h3>
              <p className="text-gray-600 mb-6">Get personalized property recommendations</p>
              <div className="flex flex-col sm:flex-row gap-4">
                <a
                  href="https://app.bali-realestate.com/chatbot?query=general-consultation"
                  className="flex-1 bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-xl font-semibold text-center transition-all duration-300 hover:scale-105"
                  target="_self"
                >
                  Start Consultation
                </a>
                <a
                  href="/contact"
                  className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 px-6 py-3 rounded-xl font-semibold text-center transition-all duration-300 hover:scale-105"
                >
                  Contact Us
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Complete Location Coverage */}
      <section className="py-16 bg-gradient-to-r from-emerald-50 to-teal-50">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Complete Bali Coverage
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From digital nomad hotspots to family-friendly neighborhoods and luxury destinations -
              we cover all of Bali's premier locations with comprehensive local expertise.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Digital Nomad Locations */}
            <div className="bg-white rounded-lg p-6 shadow-lg">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Digital Nomad Hubs</h3>
              <p className="text-gray-600 mb-4">Perfect for remote workers and entrepreneurs</p>
              <ul className="space-y-2">
                <li className="flex items-center text-sm text-gray-700">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                  Canggu - Surf & coworking paradise
                </li>
                <li className="flex items-center text-sm text-gray-700">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                  Ubud - Wellness & creativity center
                </li>
                <li className="flex items-center text-sm text-gray-700">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                  Seminyak - Luxury nomad lifestyle
                </li>
              </ul>
            </div>

            {/* Family Locations */}
            <div className="bg-white rounded-lg p-6 shadow-lg">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Family Destinations</h3>
              <p className="text-gray-600 mb-4">Safe neighborhoods with international schools</p>
              <ul className="space-y-2">
                <li className="flex items-center text-sm text-gray-700">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                  Sanur - Traditional & family-friendly
                </li>
                <li className="flex items-center text-sm text-gray-700">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                  Jimbaran - Luxury family living
                </li>
                <li className="flex items-center text-sm text-gray-700">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                  Seminyak - Upscale family amenities
                </li>
              </ul>
            </div>

            {/* Investment Opportunities */}
            <div className="bg-white rounded-lg p-6 shadow-lg">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Investment Hotspots</h3>
              <p className="text-gray-600 mb-4">High-growth areas with strong ROI potential</p>
              <ul className="space-y-2">
                <li className="flex items-center text-sm text-gray-700">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                  All locations - Comprehensive analysis
                </li>
                <li className="flex items-center text-sm text-gray-700">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                  Market data & rental yields
                </li>
                <li className="flex items-center text-sm text-gray-700">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
                  Legal guidance & compliance
                </li>
              </ul>
            </div>
          </div>

          <div className="text-center mt-12">
            <Link
              href="/locations"
              className="inline-flex items-center bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors"
            >
              Explore All Locations
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </div>
        </div>
      </section>

      {/* STORY-003: Advanced Schema Markup */}
      <AdvancedSchema type="organization" />
      <AdvancedSchema type="website" />
      <AdvancedSchema type="local-business" />
      <AdvancedSchema type="real-estate-agent" />
    </main>
  )
}


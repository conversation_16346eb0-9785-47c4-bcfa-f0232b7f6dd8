/**
 * Interactive FAQ Component
 * Bali Property Scout Website
 * 
 * Collapsible FAQ section with toggles for practical information
 * including visa, cost of living, safety, and internet speed details.
 */

'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import ChatbotCTA from '@/components/ui/ChatbotCTA';

export interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: 'visa' | 'cost' | 'safety' | 'internet' | 'general' | 'legal';
  priority: 'high' | 'medium' | 'low';
  relatedTopics?: string[];
}

export interface InteractiveFAQProps {
  /** Location name for context */
  locationName: string;
  /** FAQ items to display */
  faqItems: FAQItem[];
  /** Additional CSS classes */
  className?: string;
}

export const InteractiveFAQ: React.FC<InteractiveFAQProps> = ({
  locationName,
  faqItems,
  className,
}) => {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [activeCategory, setActiveCategory] = useState<string>('all');

  const toggleItem = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  // Filter items by category
  const filteredItems = activeCategory === 'all' 
    ? faqItems 
    : faqItems.filter(item => item.category === activeCategory);

  // Get category info
  const getCategoryInfo = (category: string) => {
    const categories = {
      all: { label: 'All Questions', icon: '❓', color: 'bg-gray-100 text-gray-700' },
      visa: { label: 'Visa & Legal', icon: '📋', color: 'bg-blue-100 text-blue-700' },
      cost: { label: 'Cost of Living', icon: '💰', color: 'bg-green-100 text-green-700' },
      safety: { label: 'Safety & Security', icon: '🛡️', color: 'bg-red-100 text-red-700' },
      internet: { label: 'Internet & Tech', icon: '📶', color: 'bg-purple-100 text-purple-700' },
      general: { label: 'General Info', icon: '🏠', color: 'bg-orange-100 text-orange-700' },
      legal: { label: 'Legal & Tax', icon: '⚖️', color: 'bg-indigo-100 text-indigo-700' },
    };
    return categories[category as keyof typeof categories] || categories.all;
  };

  // Get priority styling
  const getPriorityStyle = (priority: FAQItem['priority']) => {
    const styles = {
      high: 'border-l-4 border-red-500 bg-red-50',
      medium: 'border-l-4 border-yellow-500 bg-yellow-50',
      low: 'border-l-4 border-green-500 bg-green-50',
    };
    return styles[priority];
  };

  const categories = ['all', 'visa', 'cost', 'safety', 'internet', 'general', 'legal'];

  return (
    <div className={cn('', className)}>
      {/* FAQ Header */}
      <div className="text-center mb-8">
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          Frequently Asked Questions
        </h3>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Get answers to common questions about living, working, and investing in {locationName}. 
          Click on any question to expand the detailed answer.
        </p>
      </div>

      {/* Category Filter */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-2 justify-center">
          {categories.map((category) => {
            const categoryInfo = getCategoryInfo(category);
            const isActive = activeCategory === category;
            
            return (
              <button
                key={category}
                onClick={() => setActiveCategory(category)}
                className={cn(
                  'flex items-center gap-2 px-4 py-2 rounded-xl font-medium text-sm transition-all duration-300 hover:scale-105',
                  isActive 
                    ? categoryInfo.color + ' shadow-lg' 
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                )}
              >
                <span>{categoryInfo.icon}</span>
                <span>{categoryInfo.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* FAQ Items */}
      <div className="space-y-4 mb-8">
        {filteredItems.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>No questions found in this category.</p>
          </div>
        ) : (
          filteredItems.map((item) => {
            const isExpanded = expandedItems.has(item.id);
            const categoryInfo = getCategoryInfo(item.category);
            
            return (
              <div
                key={item.id}
                className={cn(
                  'bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden',
                  getPriorityStyle(item.priority)
                )}
              >
                {/* Question Header */}
                <button
                  onClick={() => toggleItem(item.id)}
                  className="w-full p-6 text-left hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-inset"
                  aria-expanded={isExpanded}
                  aria-controls={`faq-answer-${item.id}`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-start space-x-4 flex-1">
                      {/* Category Icon */}
                      <div className="flex-shrink-0 mt-1">
                        <div className={cn('w-8 h-8 rounded-lg flex items-center justify-center text-sm', categoryInfo.color)}>
                          {categoryInfo.icon}
                        </div>
                      </div>
                      
                      {/* Question */}
                      <div className="flex-1">
                        <h4 className="text-lg font-semibold text-gray-900 mb-1">
                          {item.question}
                        </h4>
                        
                        {/* Priority Badge */}
                        <div className="flex items-center gap-2">
                          <span className={cn(
                            'px-2 py-1 rounded-full text-xs font-medium',
                            item.priority === 'high' ? 'bg-red-100 text-red-700' :
                            item.priority === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                            'bg-green-100 text-green-700'
                          )}>
                            {item.priority === 'high' ? '🔥 High Priority' :
                             item.priority === 'medium' ? '⚡ Medium Priority' :
                             '✅ General Info'}
                          </span>
                          <span className="text-xs text-gray-500">
                            {categoryInfo.label}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    {/* Expand Icon */}
                    <div className="flex-shrink-0 ml-4">
                      <div
                        className={cn(
                          'w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center transition-transform duration-300',
                          isExpanded && 'rotate-180'
                        )}
                      >
                        <svg
                          className="w-4 h-4 text-emerald-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                </button>

                {/* Answer Content */}
                <div
                  id={`faq-answer-${item.id}`}
                  className={cn(
                    'overflow-hidden transition-all duration-300 ease-in-out',
                    isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                  )}
                >
                  <div className="px-6 pb-6">
                    <div className="border-t border-gray-100 pt-4">
                      <div className="prose prose-sm max-w-none">
                        <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                          {item.answer}
                        </p>
                      </div>
                      
                      {/* Related Topics */}
                      {item.relatedTopics && item.relatedTopics.length > 0 && (
                        <div className="mt-4 pt-4 border-t border-gray-100">
                          <p className="text-sm font-medium text-gray-900 mb-2">
                            Related Topics:
                          </p>
                          <div className="flex flex-wrap gap-2">
                            {item.relatedTopics.map((topic, index) => (
                              <span
                                key={index}
                                className="px-2 py-1 bg-gray-100 text-gray-600 rounded-lg text-xs"
                              >
                                {topic}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* FAQ CTA */}
      <div className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-2xl p-8 text-center">
        <h4 className="text-xl font-bold text-gray-900 mb-2">
          Still Have Questions?
        </h4>
        <p className="text-gray-600 mb-6">
          Our AI assistant can provide personalized answers about living and investing in {locationName}.
        </p>
        <ChatbotCTA
          query={{ type: 'faq', location: locationName, context: 'comprehensive-questions' }}
          className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-8 py-4 rounded-2xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
        >
          💬 Ask Our AI Assistant
        </ChatbotCTA>
      </div>
    </div>
  );
};

export default InteractiveFAQ;

/**
 * Testimonial Component
 * 
 * Testimonial display with ratings, avatars, and carousel functionality.
 * Supports multiple layouts and interaction modes.
 */

'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';

export interface TestimonialData {
  /** Unique identifier */
  id: string;
  /** Customer name */
  name: string;
  /** Customer title/role */
  title?: string;
  /** Company name */
  company?: string;
  /** Customer avatar URL */
  avatar?: string;
  /** Testimonial content */
  content: string;
  /** Rating (1-5 stars) */
  rating?: number;
  /** Location */
  location?: string;
  /** Date of testimonial */
  date?: string;
  /** Property type related to testimonial */
  propertyType?: string;
  /** Featured testimonial */
  featured?: boolean;
}

export interface TestimonialProps {
  /** Single testimonial data */
  testimonial?: TestimonialData;
  /** Multiple testimonials for carousel */
  testimonials?: TestimonialData[];
  /** Layout variant */
  variant?: 'card' | 'quote' | 'minimal' | 'featured';
  /** Whether to show rating */
  showRating?: boolean;
  /** Whether to show avatar */
  showAvatar?: boolean;
  /** Whether to show date */
  showDate?: boolean;
  /** Whether to enable carousel (for multiple testimonials) */
  carousel?: boolean;
  /** Auto-play interval for carousel (in ms) */
  autoPlay?: number;
  /** Custom className */
  className?: string;
}

// Variant styles
const variantStyles = {
  card: {
    container: 'bg-white border border-neutral-200 rounded-lg p-6 shadow-sm',
    content: 'text-neutral-700 mb-4',
    author: 'flex items-center space-x-3',
    avatar: 'w-12 h-12 rounded-full',
    name: 'font-semibold text-neutral-900',
    title: 'text-sm text-neutral-600',
  },
  quote: {
    container: 'relative bg-neutral-50 p-8 rounded-lg',
    content: 'text-lg text-neutral-800 italic mb-6 relative z-10',
    author: 'flex items-center space-x-3',
    avatar: 'w-14 h-14 rounded-full',
    name: 'font-semibold text-neutral-900',
    title: 'text-sm text-neutral-600',
  },
  minimal: {
    container: 'text-center',
    content: 'text-neutral-700 mb-4',
    author: 'flex flex-col items-center space-y-2',
    avatar: 'w-16 h-16 rounded-full',
    name: 'font-semibold text-neutral-900',
    title: 'text-sm text-neutral-600',
  },
  featured: {
    container: 'bg-primary-lightest border-l-4 border-primary p-8 rounded-r-lg',
    content: 'text-lg text-neutral-800 mb-6',
    author: 'flex items-center space-x-4',
    avatar: 'w-16 h-16 rounded-full',
    name: 'text-lg font-semibold text-neutral-900',
    title: 'text-neutral-600',
  },
};

// Star rating component
const StarRating: React.FC<{ rating: number; className?: string }> = ({ 
  rating, 
  className 
}) => {
  return (
    <div className={cn('flex items-center space-x-1', className)}>
      {[1, 2, 3, 4, 5].map((star) => (
        <svg
          key={star}
          className={cn(
            'w-4 h-4',
            star <= rating ? 'text-yellow-400 fill-current' : 'text-neutral-300'
          )}
          viewBox="0 0 20 20"
        >
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      ))}
    </div>
  );
};

// Single testimonial component
const TestimonialItem: React.FC<{
  testimonial: TestimonialData;
  variant: TestimonialProps['variant'];
  showRating: boolean;
  showAvatar: boolean;
  showDate: boolean;
}> = ({ testimonial, variant = 'card', showRating, showAvatar, showDate }) => {
  const styles = variantStyles[variant];

  return (
    <div className={styles.container}>
      {/* Quote icon for quote variant */}
      {variant === 'quote' && (
        <div className="absolute top-4 left-4 text-primary-light opacity-20">
          <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
            <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
          </svg>
        </div>
      )}

      {/* Rating */}
      {showRating && testimonial.rating && (
        <div className="mb-3">
          <StarRating rating={testimonial.rating} />
        </div>
      )}

      {/* Content */}
      <div className={styles.content}>
        <p>"{testimonial.content}"</p>
      </div>

      {/* Author info */}
      <div className={styles.author}>
        {/* Avatar */}
        {showAvatar && testimonial.avatar && (
          <div className={cn('flex-shrink-0', styles.avatar, 'overflow-hidden')}>
            <Image
              src={testimonial.avatar}
              alt={testimonial.name}
              width={64}
              height={64}
              className="w-full h-full object-cover"
            />
          </div>
        )}

        {/* Name and details */}
        <div className={variant === 'minimal' ? 'text-center' : ''}>
          <div className={styles.name}>
            {testimonial.name}
          </div>
          
          {(testimonial.title || testimonial.company) && (
            <div className={styles.title}>
              {testimonial.title}
              {testimonial.title && testimonial.company && ' at '}
              {testimonial.company}
            </div>
          )}
          
          {testimonial.location && (
            <div className="text-xs text-neutral-500 mt-1">
              {testimonial.location}
            </div>
          )}
          
          {showDate && testimonial.date && (
            <div className="text-xs text-neutral-500 mt-1">
              {testimonial.date}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export const Testimonial: React.FC<TestimonialProps> = React.memo(({
  testimonial,
  testimonials,
  variant = 'card',
  showRating = true,
  showAvatar = true,
  showDate = false,
  carousel = false,
  autoPlay,
  className,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  // Auto-play functionality
  useEffect(() => {
    if (!carousel || !autoPlay || !testimonials || testimonials.length <= 1) {
      return;
    }

    const interval = setInterval(() => {
      setCurrentIndex((current) => 
        current === testimonials.length - 1 ? 0 : current + 1
      );
    }, autoPlay);

    return () => clearInterval(interval);
  }, [carousel, autoPlay, testimonials]);

  // Handle navigation
  const goToSlide = (index: number) => {
    if (testimonials) {
      setCurrentIndex(Math.max(0, Math.min(index, testimonials.length - 1)));
    }
  };

  const goToPrevious = () => {
    if (testimonials) {
      setCurrentIndex(current => 
        current === 0 ? testimonials.length - 1 : current - 1
      );
    }
  };

  const goToNext = () => {
    if (testimonials) {
      setCurrentIndex(current => 
        current === testimonials.length - 1 ? 0 : current + 1
      );
    }
  };

  // Single testimonial
  if (testimonial && !testimonials) {
    return (
      <div className={className}>
        <TestimonialItem
          testimonial={testimonial}
          variant={variant}
          showRating={showRating}
          showAvatar={showAvatar}
          showDate={showDate}
        />
      </div>
    );
  }

  // Multiple testimonials
  if (testimonials && testimonials.length > 0) {
    // Carousel mode
    if (carousel) {
      const currentTestimonial = testimonials[currentIndex];

      return (
        <div className={cn('relative', className)}>
          {/* Current testimonial */}
          <TestimonialItem
            testimonial={currentTestimonial}
            variant={variant}
            showRating={showRating}
            showAvatar={showAvatar}
            showDate={showDate}
          />

          {/* Navigation */}
          {testimonials.length > 1 && (
            <>
              {/* Previous/Next buttons */}
              <button
                onClick={goToPrevious}
                className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-12 bg-white border border-neutral-200 rounded-full p-2 shadow-sm hover:shadow-md transition-shadow"
                aria-label="Previous testimonial"
              >
                <svg className="w-4 h-4 text-neutral-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>

              <button
                onClick={goToNext}
                className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-12 bg-white border border-neutral-200 rounded-full p-2 shadow-sm hover:shadow-md transition-shadow"
                aria-label="Next testimonial"
              >
                <svg className="w-4 h-4 text-neutral-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>

              {/* Dots indicator */}
              <div className="flex justify-center space-x-2 mt-6">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToSlide(index)}
                    className={cn(
                      'w-2 h-2 rounded-full transition-colors',
                      index === currentIndex ? 'bg-primary' : 'bg-neutral-300'
                    )}
                    aria-label={`Go to testimonial ${index + 1}`}
                  />
                ))}
              </div>
            </>
          )}
        </div>
      );
    }

    // Grid mode
    return (
      <div className={cn('grid gap-6', className)}>
        {testimonials.map((testimonialItem) => (
          <TestimonialItem
            key={testimonialItem.id}
            testimonial={testimonialItem}
            variant={variant}
            showRating={showRating}
            showAvatar={showAvatar}
            showDate={showDate}
          />
        ))}
      </div>
    );
  }

  return null;
});

Testimonial.displayName = 'Testimonial';

// Specialized testimonial variants
export const TestimonialCard: React.FC<Omit<TestimonialProps, 'variant'>> = (props) => (
  <Testimonial {...props} variant="card" />
);

export const TestimonialQuote: React.FC<Omit<TestimonialProps, 'variant'>> = (props) => (
  <Testimonial {...props} variant="quote" />
);

export const TestimonialMinimal: React.FC<Omit<TestimonialProps, 'variant'>> = (props) => (
  <Testimonial {...props} variant="minimal" />
);

export const TestimonialFeatured: React.FC<Omit<TestimonialProps, 'variant'>> = (props) => (
  <Testimonial {...props} variant="featured" />
);

// Testimonial carousel component
export const TestimonialCarousel: React.FC<Omit<TestimonialProps, 'carousel'>> = (props) => (
  <Testimonial {...props} carousel={true} />
);

#!/usr/bin/env node

/**
 * Comprehensive Test Runner
 * 
 * Runs all test suites and generates comprehensive reports
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Test configuration
const TEST_CONFIG = {
  unit: {
    command: 'npm',
    args: ['run', 'test:ci'],
    name: 'Unit Tests',
    timeout: 120000, // 2 minutes
  },
  e2e: {
    command: 'npm',
    args: ['run', 'test:e2e'],
    name: 'E2E Tests',
    timeout: 300000, // 5 minutes
  },
  accessibility: {
    command: 'npm',
    args: ['run', 'test:a11y'],
    name: 'Accessibility Tests',
    timeout: 180000, // 3 minutes
  },
  performance: {
    command: 'npm',
    args: ['run', 'test:perf'],
    name: 'Performance Tests',
    timeout: 240000, // 4 minutes
  },
  seo: {
    command: 'npm',
    args: ['run', 'test:seo'],
    name: 'SEO Tests',
    timeout: 120000, // 2 minutes
  },
};

// Results tracking
const results = {
  passed: [],
  failed: [],
  skipped: [],
  startTime: Date.now(),
  endTime: null,
};

async function runTest(testType, config) {
  console.log(`\n🧪 Running ${config.name}...`);
  console.log(`Command: ${config.command} ${config.args.join(' ')}`);
  
  return new Promise((resolve) => {
    const startTime = Date.now();
    
    const child = spawn(config.command, config.args, {
      stdio: 'pipe',
      shell: true,
      cwd: process.cwd(),
    });
    
    let stdout = '';
    let stderr = '';
    
    child.stdout.on('data', (data) => {
      stdout += data.toString();
      process.stdout.write(data);
    });
    
    child.stderr.on('data', (data) => {
      stderr += data.toString();
      process.stderr.write(data);
    });
    
    const timeout = setTimeout(() => {
      child.kill('SIGTERM');
      console.log(`\n⏰ ${config.name} timed out after ${config.timeout}ms`);
      
      results.failed.push({
        name: config.name,
        type: testType,
        error: 'Test timed out',
        duration: Date.now() - startTime,
        stdout: stdout.slice(-1000), // Last 1000 chars
        stderr: stderr.slice(-1000),
      });
      
      resolve(false);
    }, config.timeout);
    
    child.on('close', (code) => {
      clearTimeout(timeout);
      const duration = Date.now() - startTime;
      
      if (code === 0) {
        console.log(`\n✅ ${config.name} passed (${duration}ms)`);
        results.passed.push({
          name: config.name,
          type: testType,
          duration,
        });
        resolve(true);
      } else {
        console.log(`\n❌ ${config.name} failed with code ${code} (${duration}ms)`);
        results.failed.push({
          name: config.name,
          type: testType,
          error: `Exit code: ${code}`,
          duration,
          stdout: stdout.slice(-1000),
          stderr: stderr.slice(-1000),
        });
        resolve(false);
      }
    });
    
    child.on('error', (error) => {
      clearTimeout(timeout);
      console.log(`\n💥 ${config.name} error:`, error.message);
      
      results.failed.push({
        name: config.name,
        type: testType,
        error: error.message,
        duration: Date.now() - startTime,
        stdout,
        stderr,
      });
      
      resolve(false);
    });
  });
}

async function generateReport() {
  results.endTime = Date.now();
  const totalDuration = results.endTime - results.startTime;
  
  const report = {
    timestamp: new Date().toISOString(),
    duration: totalDuration,
    summary: {
      total: results.passed.length + results.failed.length + results.skipped.length,
      passed: results.passed.length,
      failed: results.failed.length,
      skipped: results.skipped.length,
      passRate: results.passed.length / (results.passed.length + results.failed.length) * 100,
    },
    results: {
      passed: results.passed,
      failed: results.failed,
      skipped: results.skipped,
    },
  };
  
  // Ensure test-results directory exists
  const reportsDir = path.join(process.cwd(), 'test-results');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }
  
  // Save detailed report
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportPath = path.join(reportsDir, `test-report-${timestamp}.json`);
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  // Save latest report
  const latestPath = path.join(reportsDir, 'latest-test-report.json');
  fs.writeFileSync(latestPath, JSON.stringify(report, null, 2));
  
  return { report, reportPath };
}

function printSummary(report) {
  console.log('\n' + '='.repeat(60));
  console.log('🎯 TEST EXECUTION SUMMARY');
  console.log('='.repeat(60));
  
  console.log(`📅 Timestamp: ${report.timestamp}`);
  console.log(`⏱️  Total Duration: ${(report.duration / 1000).toFixed(2)}s`);
  console.log(`📊 Tests Run: ${report.summary.total}`);
  console.log(`✅ Passed: ${report.summary.passed}`);
  console.log(`❌ Failed: ${report.summary.failed}`);
  console.log(`⏭️  Skipped: ${report.summary.skipped}`);
  console.log(`📈 Pass Rate: ${report.summary.passRate.toFixed(1)}%`);
  
  if (report.results.passed.length > 0) {
    console.log('\n✅ PASSED TESTS:');
    report.results.passed.forEach(test => {
      console.log(`  • ${test.name} (${test.duration}ms)`);
    });
  }
  
  if (report.results.failed.length > 0) {
    console.log('\n❌ FAILED TESTS:');
    report.results.failed.forEach(test => {
      console.log(`  • ${test.name} (${test.duration}ms)`);
      console.log(`    Error: ${test.error}`);
      if (test.stderr) {
        console.log(`    Stderr: ${test.stderr.slice(0, 200)}...`);
      }
    });
  }
  
  if (report.results.skipped.length > 0) {
    console.log('\n⏭️ SKIPPED TESTS:');
    report.results.skipped.forEach(test => {
      console.log(`  • ${test.name}`);
    });
  }
  
  console.log('\n' + '='.repeat(60));
  
  if (report.summary.failed === 0) {
    console.log('🎉 ALL TESTS PASSED! 🎉');
  } else {
    console.log(`💥 ${report.summary.failed} TEST(S) FAILED`);
  }
  
  console.log('='.repeat(60));
}

async function main() {
  console.log('🚀 Starting Comprehensive Test Suite...\n');
  
  const testTypes = process.argv.slice(2);
  const runAllTests = testTypes.length === 0 || testTypes.includes('--all');
  
  // Determine which tests to run
  const testsToRun = runAllTests 
    ? Object.keys(TEST_CONFIG)
    : testTypes.filter(type => TEST_CONFIG[type]);
  
  if (testsToRun.length === 0) {
    console.log('❌ No valid test types specified');
    console.log('Available test types:', Object.keys(TEST_CONFIG).join(', '));
    process.exit(1);
  }
  
  console.log(`📋 Running tests: ${testsToRun.join(', ')}`);
  
  // Run tests sequentially
  for (const testType of testsToRun) {
    const config = TEST_CONFIG[testType];
    
    try {
      await runTest(testType, config);
    } catch (error) {
      console.error(`💥 Unexpected error running ${config.name}:`, error);
      results.failed.push({
        name: config.name,
        type: testType,
        error: error.message,
        duration: 0,
      });
    }
  }
  
  // Generate and display report
  const { report, reportPath } = await generateReport();
  
  console.log(`\n📊 Detailed report saved: ${reportPath}`);
  printSummary(report);
  
  // Exit with appropriate code
  const exitCode = report.summary.failed > 0 ? 1 : 0;
  process.exit(exitCode);
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Test execution interrupted');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Test execution terminated');
  process.exit(1);
});

// Run if called directly
if (require.main === module) {
  main().catch((error) => {
    console.error('💥 Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = {
  runTest,
  generateReport,
  TEST_CONFIG,
};

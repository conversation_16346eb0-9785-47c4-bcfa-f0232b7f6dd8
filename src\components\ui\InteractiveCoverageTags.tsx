/**
 * Interactive Coverage Tags Component
 * Bali Property Scout Website
 * 
 * Converts location lists into clickable chips that trigger chatbot
 * with prefilled queries for enhanced user engagement and lead conversion.
 */

'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { ChatbotCTA } from '@/components/ui/ChatbotCTA';

export interface CoverageTag {
  id: string;
  label: string;
  type: 'location' | 'property-type' | 'service';
  context?: string;
  description?: string;
  priority?: 'high' | 'medium' | 'low';
  icon?: string;
}

export interface InteractiveCoverageTagsProps {
  tags: CoverageTag[];
  title?: string;
  description?: string;
  variant?: 'grid' | 'inline' | 'compact';
  maxVisible?: number;
  className?: string;
  showPriority?: boolean;
  animated?: boolean;
}

export function InteractiveCoverageTags({
  tags,
  title,
  description,
  variant = 'inline',
  maxVisible,
  className,
  showPriority = false,
  animated = true,
}: InteractiveCoverageTagsProps) {
  const [showAll, setShowAll] = useState(false);
  const [hoveredTag, setHoveredTag] = useState<string | null>(null);

  // Defensive check for tags array
  if (!tags || !Array.isArray(tags) || tags.length === 0) {
    return null;
  }

  // Determine which tags to show
  const visibleTags = maxVisible && !showAll ? tags.slice(0, maxVisible) : tags;
  const hasMoreTags = maxVisible && tags.length > maxVisible;

  // Simplified priority styling - uniform tags with brand accent on hover
  const getPriorityStyle = (priority?: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-50 text-red-700 border border-red-200 hover:bg-red-100';
      case 'medium':
        return 'bg-orange-50 text-orange-700 border border-orange-200 hover:bg-orange-100';
      default:
        return 'bg-neutral-100 text-neutral-700 border border-neutral-200 hover:bg-brand-primary-bg hover:text-brand-primary hover:border-brand-primary-light';
    }
  };

  // Generate chatbot query based on tag type
  const generateQuery = (tag: CoverageTag) => {
    if (!tag || !tag.type) {
      return { type: 'general' as const };
    }

    switch (tag.type) {
      case 'location':
        return { type: 'location' as const, location: tag.label || 'Unknown Location' };
      case 'property-type':
        return { type: 'property-type' as const, propertyType: tag.label || 'Unknown Type', location: tag.context };
      case 'service':
        return { type: 'service' as const, service: tag.label || 'Unknown Service' };
      default:
        return { type: 'general' as const };
    }
  };

  // Layout classes based on variant
  const getLayoutClasses = () => {
    switch (variant) {
      case 'grid':
        return 'grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4';
      case 'compact':
        return 'flex flex-wrap gap-2';
      default:
        return 'flex flex-wrap gap-3';
    }
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Clean Section Header */}
      {(title || description) && (
        <div className="text-center">
          {title && (
            <h3 className="heading-3 text-xl md:text-2xl font-bold mb-4" style={{ color: 'var(--neutral-900)' }}>
              {title}
            </h3>
          )}
          {description && (
            <p className="body-text text-base max-w-3xl mx-auto leading-relaxed" style={{ color: 'var(--neutral-600)' }}>
              {description}
            </p>
          )}
        </div>
      )}

      {/* Interactive Tags */}
      <div className={getLayoutClasses()}>
        {visibleTags.map((tag, index) => {
          try {
            return (
              <ChatbotCTA
                key={tag.id || `tag-${index}`}
                query={generateQuery(tag)}
            className={cn(
              'group relative inline-flex items-center gap-2 px-4 py-3 rounded-lg font-medium text-sm transition-all duration-300 shadow-sm hover:shadow-md',
              showPriority ? getPriorityStyle(tag.priority) : 'bg-neutral-100 text-neutral-700 border border-neutral-200 hover:bg-brand-primary-bg hover:text-brand-primary hover:border-brand-primary-light',
              animated && 'hover:scale-105',
              variant === 'grid' && 'justify-center text-center',
              variant === 'compact' && 'px-3 py-2 text-xs'
            )}
            style={!showPriority ? {
              backgroundColor: 'var(--neutral-100)',
              color: 'var(--neutral-700)',
              borderColor: 'var(--neutral-200)'
            } : undefined}
            onMouseEnter={() => setHoveredTag(tag.id)}
            onMouseLeave={() => setHoveredTag(null)}
          >
            {/* Label - No Icons, No HOT Labels */}
            <span className="font-semibold">{tag.label}</span>
            
            {/* Hover Arrow */}
            <svg
              className={cn(
                'w-4 h-4 transition-transform duration-200',
                hoveredTag === tag.id ? 'translate-x-1' : 'translate-x-0'
              )}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </ChatbotCTA>
            );
          } catch (error) {
            console.warn('Error rendering tag:', tag, error);
            return null;
          }
        })}
      </div>

      {/* Show More/Less Button */}
      {hasMoreTags && (
        <div className="text-center">
          <button
            onClick={() => setShowAll(!showAll)}
            className="btn-secondary inline-flex items-center gap-2 px-6 py-3 font-medium transition-all duration-300 hover:scale-105"
            style={{
              borderColor: 'var(--brand-primary)',
              color: 'var(--brand-primary)'
            }}
          >
            <span>
              {showAll ? 'Show Less' : `View All ${tags.length} Options`}
            </span>
            <svg
              className={cn(
                'w-4 h-4 transition-transform duration-200',
                showAll && 'rotate-180'
              )}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>
        </div>
      )}

      {/* Clean Call-to-Action Footer */}
      <div className="text-center pt-4">
        <p className="text-sm" style={{ color: 'var(--neutral-500)' }}>
          Click any option to start a personalized search with our AI assistant
        </p>
      </div>
    </div>
  );
}

export default InteractiveCoverageTags;

/**
 * Pricing Table Component
 * Bali Property Scout Website
 * 
 * Interactive pricing table with property types, price ranges,
 * and market indicators for location pages.
 */

'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import ChatbotCTA from '@/components/ui/ChatbotCTA';

export interface PricingData {
  propertyType: string;
  icon: string;
  rentalRange: {
    min: number;
    max: number;
    currency: string;
  };
  saleRange: {
    min: number;
    max: number;
    currency: string;
  };
  growthTrend: number; // Percentage growth
  popularityScore: number; // 1-10 scale
  features: string[];
  description: string;
}

export interface PricingTableProps {
  /** Location name for context */
  locationName: string;
  /** Pricing data for different property types */
  pricingData: PricingData[];
  /** Additional CSS classes */
  className?: string;
}

export const PricingTable: React.FC<PricingTableProps> = ({
  locationName,
  pricingData,
  className,
}) => {
  const [activeView, setActiveView] = useState<'rental' | 'sale'>('rental');
  const [expandedRow, setExpandedRow] = useState<string | null>(null);

  // Format price range
  const formatPriceRange = (min: number, max: number, currency: string = 'USD') => {
    const formatNumber = (num: number) => {
      if (num >= 1000000) {
        return `${(num / 1000000).toFixed(1)}M`;
      } else if (num >= 1000) {
        return `${(num / 1000).toFixed(0)}K`;
      }
      return num.toLocaleString();
    };

    return `$${formatNumber(min)} - $${formatNumber(max)}`;
  };

  // Get growth trend indicator
  const getGrowthIndicator = (growth: number) => {
    if (growth > 5) return { icon: '📈', color: 'text-green-600', label: 'Strong Growth' };
    if (growth > 0) return { icon: '📊', color: 'text-emerald-600', label: 'Moderate Growth' };
    if (growth > -5) return { icon: '📉', color: 'text-yellow-600', label: 'Stable' };
    return { icon: '⬇️', color: 'text-red-600', label: 'Declining' };
  };

  // Get popularity indicator
  const getPopularityStars = (score: number) => {
    const stars = Math.round(score / 2); // Convert 1-10 to 1-5 stars
    return '⭐'.repeat(stars) + '☆'.repeat(5 - stars);
  };

  return (
    <div className={cn('bg-white rounded-2xl shadow-lg overflow-hidden', className)}>
      {/* Table Header */}
      <div className="bg-gradient-to-r from-emerald-600 to-teal-600 p-6">
        <h3 className="text-2xl font-bold text-white mb-2">
          {locationName} Property Pricing Guide
        </h3>
        <p className="text-emerald-100">
          Comprehensive pricing data for different property types and market segments
        </p>
        
        {/* View Toggle */}
        <div className="flex gap-2 mt-4">
          <button
            onClick={() => setActiveView('rental')}
            className={cn(
              'px-4 py-2 rounded-lg font-medium text-sm transition-all duration-300',
              activeView === 'rental'
                ? 'bg-white text-emerald-600 shadow-lg'
                : 'bg-emerald-700 text-white hover:bg-emerald-800'
            )}
          >
            🏠 Rental Prices
          </button>
          <button
            onClick={() => setActiveView('sale')}
            className={cn(
              'px-4 py-2 rounded-lg font-medium text-sm transition-all duration-300',
              activeView === 'sale'
                ? 'bg-white text-emerald-600 shadow-lg'
                : 'bg-emerald-700 text-white hover:bg-emerald-800'
            )}
          >
            💰 Sale Prices
          </button>
        </div>
      </div>

      {/* Table Content */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                Property Type
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                {activeView === 'rental' ? 'Monthly Rent' : 'Sale Price'}
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                Market Trend
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                Popularity
              </th>
              <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                Action
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {pricingData.map((property, index) => {
              const isExpanded = expandedRow === property.propertyType;
              const growthIndicator = getGrowthIndicator(property.growthTrend);
              const priceRange = activeView === 'rental' 
                ? formatPriceRange(property.rentalRange.min, property.rentalRange.max)
                : formatPriceRange(property.saleRange.min, property.saleRange.max);

              return (
                <React.Fragment key={property.propertyType}>
                  <tr 
                    className="hover:bg-gray-50 transition-colors duration-200 cursor-pointer"
                    onClick={() => setExpandedRow(isExpanded ? null : property.propertyType)}
                  >
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <span className="text-2xl mr-3">{property.icon}</span>
                        <div>
                          <div className="text-sm font-semibold text-gray-900">
                            {property.propertyType}
                          </div>
                          <div className="text-xs text-gray-500">
                            Click to expand details
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm font-bold text-gray-900">
                        {priceRange}
                      </div>
                      <div className="text-xs text-gray-500">
                        {activeView === 'rental' ? 'per month' : 'purchase price'}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <span className="text-lg mr-2">{growthIndicator.icon}</span>
                        <div>
                          <div className={cn('text-sm font-medium', growthIndicator.color)}>
                            {property.growthTrend > 0 ? '+' : ''}{property.growthTrend}%
                          </div>
                          <div className="text-xs text-gray-500">
                            {growthIndicator.label}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm">
                        {getPopularityStars(property.popularityScore)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {property.popularityScore}/10 demand
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <ChatbotCTA
                        query={{ 
                          type: 'property-type', 
                          propertyType: property.propertyType.toLowerCase(),
                          location: locationName,
                          context: activeView 
                        }}
                        className="bg-emerald-600 hover:bg-emerald-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-300 hover:shadow-lg"
                      >
                        🔍 Find {property.propertyType}s
                      </ChatbotCTA>
                    </td>
                  </tr>
                  
                  {/* Expanded Row Details */}
                  {isExpanded && (
                    <tr className="bg-gray-50">
                      <td colSpan={5} className="px-6 py-6">
                        <div className="grid md:grid-cols-2 gap-6">
                          {/* Description */}
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-2">
                              About {property.propertyType}s in {locationName}
                            </h4>
                            <p className="text-sm text-gray-600 leading-relaxed mb-4">
                              {property.description}
                            </p>
                            
                            {/* Price Comparison */}
                            <div className="bg-white rounded-lg p-4 shadow-sm">
                              <h5 className="font-medium text-gray-900 mb-3">Price Comparison</h5>
                              <div className="space-y-2">
                                <div className="flex justify-between text-sm">
                                  <span className="text-gray-600">Monthly Rental:</span>
                                  <span className="font-medium">
                                    {formatPriceRange(property.rentalRange.min, property.rentalRange.max)}
                                  </span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span className="text-gray-600">Purchase Price:</span>
                                  <span className="font-medium">
                                    {formatPriceRange(property.saleRange.min, property.saleRange.max)}
                                  </span>
                                </div>
                                <div className="flex justify-between text-sm">
                                  <span className="text-gray-600">Annual Growth:</span>
                                  <span className={cn('font-medium', growthIndicator.color)}>
                                    {property.growthTrend > 0 ? '+' : ''}{property.growthTrend}%
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          {/* Features */}
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-2">
                              Key Features & Amenities
                            </h4>
                            <div className="grid grid-cols-1 gap-2 mb-4">
                              {property.features.map((feature, idx) => (
                                <div key={idx} className="flex items-center text-sm text-gray-600">
                                  <span className="w-2 h-2 bg-emerald-500 rounded-full mr-3 flex-shrink-0"></span>
                                  {feature}
                                </div>
                              ))}
                            </div>
                            
                            {/* CTA */}
                            <div className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-lg p-4">
                              <p className="text-sm text-gray-600 mb-3">
                                Ready to explore {property.propertyType.toLowerCase()}s in {locationName}?
                              </p>
                              <ChatbotCTA
                                query={{ 
                                  type: 'property-search', 
                                  propertyType: property.propertyType.toLowerCase(),
                                  location: locationName,
                                  context: 'detailed-search'
                                }}
                                className="w-full bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white py-2 px-4 rounded-lg text-sm font-medium transition-all duration-300 hover:shadow-lg"
                              >
                                🏠 Start Property Search
                              </ChatbotCTA>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* Table Footer */}
      <div className="bg-gray-50 px-6 py-4">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <p>
            Prices updated monthly • Data from local market analysis
          </p>
          <p>
            💡 Click any row for detailed information
          </p>
        </div>
      </div>
    </div>
  );
};

export default PricingTable;

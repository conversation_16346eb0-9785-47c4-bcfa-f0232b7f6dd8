/**
 * Lazy Component Exports
 * 
 * This file exports lazy-loaded versions of heavy components
 * to improve initial bundle size and loading performance
 */

// Lazy content components
export { LazyGallery as Gallery } from '../content/LazyGallery';

// Lazy UI components  
export { LazyModal as Modal } from '../ui/LazyModal';

// Dynamic imports for other heavy components
export const LazyTestimonial = () => import('../content/Testimonial').then(m => m.Testimonial);
export const LazyFAQ = () => import('../content/FAQ').then(m => m.FAQ);
export const LazyRichText = () => import('../content/RichText').then(m => m.RichText);

// Form components (typically heavy due to validation)
export const LazyContactForm = () => import('../forms/ContactForm').then(m => m.ContactForm);
export const LazyPropertyInterestForm = () => import('../forms/PropertyInterestForm').then(m => m.PropertyInterestForm);

// Admin components (should definitely be lazy)
export const LazyAdminPanel = () => import('../admin/AdminPanel').then(m => m.AdminPanel);

// Map components (usually very heavy)
export const LazyMap = () => import('../ui/Map').then(m => m.Map);

// Chart/Analytics components
export const LazyChart = () => import('../ui/Chart').then(m => m.Chart);
export const LazyAnalytics = () => import('../analytics/Analytics').then(m => m.Analytics);

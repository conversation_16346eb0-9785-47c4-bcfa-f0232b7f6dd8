/**
 * Contact FAQ Accordions Component
 * Bali Property Scout Website
 * 
 * Collapsible FAQ section with common questions and answers about Bali real estate.
 * Features smooth animations, keyboard navigation, and accessibility support.
 */

'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import ChatbotCTA from '@/components/ui/ChatbotCTA';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: 'legal' | 'process' | 'costs' | 'general';
}

export interface ContactFAQAccordionsProps {
  className?: string;
}

const faqData: FAQItem[] = [
  {
    id: 'foreign-ownership',
    question: 'Can foreigners buy property in Bali?',
    answer: 'Foreigners can purchase property through leasehold agreements (25-30 years, renewable) or by establishing a PMA company for freehold ownership. We guide you through the legal requirements and help you choose the best option for your situation.',
    category: 'legal'
  },
  {
    id: 'property-costs',
    question: 'What are the typical costs involved in buying property in Bali?',
    answer: 'Costs include the property price, notary fees (1-2%), land transfer tax (5%), legal fees, and due diligence costs. For rentals, expect first month, last month, and security deposit. Our AI calculator can provide accurate estimates based on your specific situation.',
    category: 'costs'
  },
  {
    id: 'rental-process',
    question: 'How does the rental process work in Bali?',
    answer: 'The rental process typically involves property viewing, lease negotiation, contract signing, and payment of deposits. We handle the entire process, from AI-powered property matching to contract finalization, ensuring a smooth experience.',
    category: 'process'
  },
  {
    id: 'best-areas',
    question: 'Which areas are best for different lifestyles?',
    answer: 'Canggu is perfect for surfers and digital nomads, Seminyak for luxury and nightlife, Ubud for nature and wellness, Sanur for families, and Uluwatu for cliff-top living. Our AI system matches your lifestyle preferences with the ideal location.',
    category: 'general'
  },
  {
    id: 'visa-requirements',
    question: 'What visa do I need to live in Bali long-term?',
    answer: 'Options include B211 Visit Visa (60 days, extendable), B213 Social/Cultural Visa (6 months), Kitas (1 year, renewable), and investment visas. Requirements vary based on your situation - we can connect you with visa specialists.',
    category: 'legal'
  },
  {
    id: 'property-management',
    question: 'Do you offer property management services?',
    answer: 'We focus on property search, purchase assistance, and investment consultation. For ongoing management, we can recommend trusted local partners who specialize in property maintenance, rental management, and guest services.',
    category: 'general'
  },
  {
    id: 'investment-returns',
    question: 'What kind of rental returns can I expect?',
    answer: 'Rental yields typically range from 8-15% annually, depending on location, property type, and management quality. Prime locations like Canggu and Seminyak often achieve higher returns. Our investment analysis provides detailed projections.',
    category: 'costs'
  },
  {
    id: 'due-diligence',
    question: 'What due diligence is required before purchasing?',
    answer: 'Essential checks include land certificate verification, building permits, tax clearance, neighborhood analysis, and legal structure review. Our comprehensive due diligence process ensures you make informed decisions with complete transparency.',
    category: 'process'
  }
];

const categories = {
  legal: { name: 'Legal & Visa', icon: '⚖️', color: 'text-blue-600' },
  process: { name: 'Process & Procedures', icon: '📋', color: 'text-emerald-600' },
  costs: { name: 'Costs & Investment', icon: '💰', color: 'text-purple-600' },
  general: { name: 'General Information', icon: 'ℹ️', color: 'text-orange-600' }
};

export const ContactFAQAccordions: React.FC<ContactFAQAccordionsProps> = ({ className }) => {
  const [openItems, setOpenItems] = useState<Set<string>>(new Set());
  const [activeCategory, setActiveCategory] = useState<string | null>(null);

  const toggleItem = (id: string) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(id)) {
      newOpenItems.delete(id);
    } else {
      newOpenItems.add(id);
    }
    setOpenItems(newOpenItems);
  };

  const toggleCategory = (category: string) => {
    const categoryItems = faqData.filter(item => item.category === category);
    const allCategoryOpen = categoryItems.every(item => openItems.has(item.id));
    
    const newOpenItems = new Set(openItems);
    
    if (allCategoryOpen) {
      // Close all items in category
      categoryItems.forEach(item => newOpenItems.delete(item.id));
    } else {
      // Open all items in category
      categoryItems.forEach(item => newOpenItems.add(item.id));
    }
    
    setOpenItems(newOpenItems);
  };

  const filteredFAQs = activeCategory 
    ? faqData.filter(item => item.category === activeCategory)
    : faqData;

  return (
    <section className={cn('py-16 bg-white', className)}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Frequently Asked Questions
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Get quick answers to common questions about Bali real estate, legal requirements, and our services.
          </p>
        </div>

        {/* Category Filter */}
        <div className="mb-8">
          <div className="flex flex-wrap justify-center gap-2 mb-6">
            <button
              onClick={() => setActiveCategory(null)}
              className={cn(
                'px-4 py-2 rounded-full text-sm font-medium transition-all duration-200',
                !activeCategory
                  ? 'bg-gray-900 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              )}
            >
              All Questions
            </button>
            {Object.entries(categories).map(([key, category]) => (
              <button
                key={key}
                onClick={() => setActiveCategory(activeCategory === key ? null : key)}
                className={cn(
                  'px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 flex items-center gap-2',
                  activeCategory === key
                    ? 'bg-gray-900 text-white'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                )}
              >
                <span>{category.icon}</span>
                {category.name}
              </button>
            ))}
          </div>

          {/* Category Actions */}
          {activeCategory && (
            <div className="text-center">
              <button
                onClick={() => toggleCategory(activeCategory)}
                className="text-sm text-emerald-600 hover:text-emerald-700 font-medium"
              >
                {faqData.filter(item => item.category === activeCategory).every(item => openItems.has(item.id))
                  ? 'Collapse All'
                  : 'Expand All'
                }
              </button>
            </div>
          )}
        </div>

        {/* FAQ Accordions */}
        <div className="space-y-4">
          {filteredFAQs.map((faq, index) => {
            const isOpen = openItems.has(faq.id);
            const category = categories[faq.category];
            
            return (
              <div
                key={faq.id}
                className="bg-gray-50 rounded-xl border border-gray-200 overflow-hidden transition-all duration-200 hover:shadow-md"
              >
                <button
                  onClick={() => toggleItem(faq.id)}
                  className="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-inset"
                  aria-expanded={isOpen}
                  aria-controls={`faq-content-${faq.id}`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-start gap-3 flex-1">
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-lg">{category.icon}</span>
                        <span className={cn('text-xs font-medium', category.color)}>
                          {category.name}
                        </span>
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 leading-relaxed">
                        {faq.question}
                      </h3>
                    </div>
                    <div className="ml-4 flex-shrink-0">
                      <svg
                        className={cn(
                          'w-5 h-5 text-gray-500 transition-transform duration-200',
                          isOpen ? 'rotate-180' : 'rotate-0'
                        )}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </div>
                  </div>
                </button>

                <div
                  id={`faq-content-${faq.id}`}
                  className={cn(
                    'overflow-hidden transition-all duration-300 ease-in-out',
                    isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                  )}
                >
                  <div className="px-6 pb-4">
                    <div className="pl-8">
                      <p className="text-gray-600 leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* CTA Section */}
        <div className="mt-12 text-center">
          <div className="bg-gradient-to-br from-emerald-50 to-blue-50 rounded-2xl p-8 border border-emerald-100">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Still Have Questions?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Our AI assistant is available 24/7 to provide personalized answers to your specific questions about Bali real estate.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <ChatbotCTA
                query={{ 
                  type: 'faq-followup', 
                  context: 'contact-page-faq-section'
                }}
                variant="primary"
                size="lg"
                className="btn-primary text-lg font-semibold px-8 py-4 bg-gradient-to-r from-emerald-600 to-blue-600 hover:from-emerald-700 hover:to-blue-700 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
              >
                Ask Our AI Assistant
              </ChatbotCTA>
              
              <button
                onClick={() => {
                  const formsSection = document.querySelector('[data-section="contact-forms"]');
                  if (formsSection) {
                    formsSection.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
                className="btn-secondary text-base font-medium px-6 py-3 border-2 border-gray-300 text-gray-700 hover:border-gray-400 hover:text-gray-900 transition-all duration-300"
              >
                Schedule Consultation
              </button>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div className="p-4">
            <div className="text-2xl font-bold text-emerald-600">500+</div>
            <div className="text-sm text-gray-600">Questions Answered</div>
          </div>
          <div className="p-4">
            <div className="text-2xl font-bold text-blue-600">24/7</div>
            <div className="text-sm text-gray-600">AI Support</div>
          </div>
          <div className="p-4">
            <div className="text-2xl font-bold text-purple-600">5+</div>
            <div className="text-sm text-gray-600">Years Experience</div>
          </div>
          <div className="p-4">
            <div className="text-2xl font-bold text-orange-600">95%</div>
            <div className="text-sm text-gray-600">Client Satisfaction</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactFAQAccordions;

#!/usr/bin/env node

/**
 * Accessibility Audit Script
 * 
 * Runs automated accessibility tests using axe-playwright
 */

const { chromium } = require('playwright');
const { injectAxe, checkA11y, getViolations } = require('axe-playwright');
const fs = require('fs');
const path = require('path');

// WCAG 2.1 AA compliance configuration
const ACCESSIBILITY_CONFIG = {
  rules: {
    // Level A rules (required)
    'area-alt': { enabled: true },
    'aria-allowed-attr': { enabled: true },
    'aria-hidden-body': { enabled: true },
    'aria-hidden-focus': { enabled: true },
    'aria-labelledby': { enabled: true },
    'aria-required-attr': { enabled: true },
    'aria-required-children': { enabled: true },
    'aria-required-parent': { enabled: true },
    'aria-roles': { enabled: true },
    'aria-valid-attr': { enabled: true },
    'aria-valid-attr-value': { enabled: true },
    'button-name': { enabled: true },
    'bypass': { enabled: true },
    'color-contrast': { enabled: true },
    'document-title': { enabled: true },
    'duplicate-id': { enabled: true },
    'form-field-multiple-labels': { enabled: true },
    'frame-title': { enabled: true },
    'html-has-lang': { enabled: true },
    'html-lang-valid': { enabled: true },
    'image-alt': { enabled: true },
    'input-button-name': { enabled: true },
    'input-image-alt': { enabled: true },
    'label': { enabled: true },
    'link-name': { enabled: true },
    'list': { enabled: true },
    'listitem': { enabled: true },
    'meta-refresh': { enabled: true },
    'meta-viewport': { enabled: true },
    'object-alt': { enabled: true },
    'role-img-alt': { enabled: true },
    'scrollable-region-focusable': { enabled: true },
    'server-side-image-map': { enabled: true },
    'svg-img-alt': { enabled: true },
    'td-headers-attr': { enabled: true },
    'th-has-data-cells': { enabled: true },
    'valid-lang': { enabled: true },
    'video-caption': { enabled: true },

    // Level AA rules (required for WCAG 2.1 AA)
    'color-contrast-enhanced': { enabled: false }, // This is AAA, not AA
    'focus-order-semantics': { enabled: true },
    'hidden-content': { enabled: true },
    'landmark-banner-is-top-level': { enabled: true },
    'landmark-complementary-is-top-level': { enabled: true },
    'landmark-contentinfo-is-top-level': { enabled: true },
    'landmark-main-is-top-level': { enabled: true },
    'landmark-no-duplicate-banner': { enabled: true },
    'landmark-no-duplicate-contentinfo': { enabled: true },
    'landmark-one-main': { enabled: true },
    'landmark-unique': { enabled: true },
    'page-has-heading-one': { enabled: true },
    'region': { enabled: true },
    'skip-link': { enabled: true },
    'tabindex': { enabled: true },
  },
  tags: ['wcag2a', 'wcag2aa', 'wcag21aa'],
};

// URLs to test
const TEST_URLS = [
  'http://localhost:3001',
  'http://localhost:3001/locations/canggu',
  'http://localhost:3001/property-types/villa',
  'http://localhost:3001/contact',
];

// Severity levels for violations
const SEVERITY_LEVELS = {
  minor: 1,
  moderate: 2,
  serious: 3,
  critical: 4,
};

async function runAccessibilityAudit(url) {
  console.log(`🔍 Testing accessibility: ${url}`);
  
  const browser = await chromium.launch({ headless: true });
  const page = await browser.newPage();

  try {
    // Navigate to the page
    await page.goto(url, { waitUntil: 'networkidle' });
    
    // Wait for page to be fully loaded
    await page.waitForTimeout(2000);
    
    // Inject axe-core
    await injectAxe(page);
    
    // Run accessibility checks
    const violations = await getViolations(page, null, ACCESSIBILITY_CONFIG);
    
    await browser.close();
    
    return {
      url,
      timestamp: new Date().toISOString(),
      violations,
      summary: {
        total: violations.length,
        critical: violations.filter(v => v.impact === 'critical').length,
        serious: violations.filter(v => v.impact === 'serious').length,
        moderate: violations.filter(v => v.impact === 'moderate').length,
        minor: violations.filter(v => v.impact === 'minor').length,
      },
    };
  } catch (error) {
    await browser.close();
    throw error;
  }
}

function analyzeResults(results) {
  const analysis = {
    timestamp: new Date().toISOString(),
    summary: {
      totalUrls: results.length,
      totalViolations: 0,
      criticalViolations: 0,
      seriousViolations: 0,
      moderateViolations: 0,
      minorViolations: 0,
      wcagCompliant: true,
    },
    results,
    recommendations: [],
  };

  // Aggregate violations
  results.forEach(result => {
    analysis.summary.totalViolations += result.summary.total;
    analysis.summary.criticalViolations += result.summary.critical;
    analysis.summary.seriousViolations += result.summary.serious;
    analysis.summary.moderateViolations += result.summary.moderate;
    analysis.summary.minorViolations += result.summary.minor;
  });

  // Determine WCAG compliance
  analysis.summary.wcagCompliant = 
    analysis.summary.criticalViolations === 0 && 
    analysis.summary.seriousViolations === 0;

  // Generate recommendations
  const allViolations = results.flatMap(r => r.violations);
  const violationCounts = {};
  
  allViolations.forEach(violation => {
    violationCounts[violation.id] = (violationCounts[violation.id] || 0) + 1;
  });

  // Top 5 most common violations
  const topViolations = Object.entries(violationCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5);

  topViolations.forEach(([violationId, count]) => {
    const violation = allViolations.find(v => v.id === violationId);
    if (violation) {
      analysis.recommendations.push({
        id: violationId,
        description: violation.description,
        help: violation.help,
        helpUrl: violation.helpUrl,
        impact: violation.impact,
        occurrences: count,
        priority: SEVERITY_LEVELS[violation.impact] || 1,
      });
    }
  });

  return analysis;
}

function generateReport(analysis) {
  const reportsDir = path.join(process.cwd(), 'accessibility-reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `accessibility-audit-${timestamp}.json`;
  const filepath = path.join(reportsDir, filename);

  fs.writeFileSync(filepath, JSON.stringify(analysis, null, 2));

  // Also save as latest
  const latestPath = path.join(reportsDir, 'latest.json');
  fs.writeFileSync(latestPath, JSON.stringify(analysis, null, 2));

  return filepath;
}

function printSummary(analysis) {
  console.log('\n♿ ACCESSIBILITY AUDIT SUMMARY');
  console.log('================================');
  console.log(`📅 Timestamp: ${analysis.timestamp}`);
  console.log(`🔗 URLs Tested: ${analysis.summary.totalUrls}`);
  console.log(`⚠️  Total Violations: ${analysis.summary.totalViolations}`);
  
  const compliance = analysis.summary.wcagCompliant ? '✅ COMPLIANT' : '❌ NON-COMPLIANT';
  console.log(`📋 WCAG 2.1 AA: ${compliance}`);

  console.log('\n📊 Violation Breakdown:');
  console.log(`  🚨 Critical: ${analysis.summary.criticalViolations}`);
  console.log(`  ⚠️  Serious: ${analysis.summary.seriousViolations}`);
  console.log(`  ⚡ Moderate: ${analysis.summary.moderateViolations}`);
  console.log(`  ℹ️  Minor: ${analysis.summary.minorViolations}`);

  if (analysis.summary.totalViolations > 0) {
    console.log('\n🔧 TOP ISSUES TO FIX:');
    analysis.recommendations.slice(0, 5).forEach((rec, index) => {
      console.log(`  ${index + 1}. ${rec.id} (${rec.occurrences} occurrences)`);
      console.log(`     Impact: ${rec.impact.toUpperCase()}`);
      console.log(`     ${rec.description}`);
      console.log(`     Help: ${rec.helpUrl}\n`);
    });
  }

  // Per-page results
  console.log('\n📄 PER-PAGE RESULTS:');
  analysis.results.forEach(result => {
    const status = result.summary.total === 0 ? '✅' : '❌';
    console.log(`  ${status} ${result.url}: ${result.summary.total} violations`);
    if (result.summary.total > 0) {
      console.log(`     Critical: ${result.summary.critical}, Serious: ${result.summary.serious}, Moderate: ${result.summary.moderate}, Minor: ${result.summary.minor}`);
    }
  });
}

async function main() {
  console.log('♿ Starting Accessibility Audit...\n');

  try {
    const results = [];

    for (const url of TEST_URLS) {
      const result = await runAccessibilityAudit(url);
      results.push(result);
      
      const status = result.summary.total === 0 ? '✅' : '❌';
      console.log(`${status} ${url}: ${result.summary.total} violations`);
    }

    const analysis = analyzeResults(results);
    const reportPath = generateReport(analysis);
    
    console.log(`\n📊 Report saved: ${reportPath}`);
    printSummary(analysis);

    // Exit with error code if not WCAG compliant
    if (!analysis.summary.wcagCompliant) {
      console.log('\n❌ Accessibility audit failed - WCAG 2.1 AA compliance not met');
      process.exit(1);
    } else {
      console.log('\n✅ Accessibility audit passed - WCAG 2.1 AA compliant!');
      process.exit(0);
    }

  } catch (error) {
    console.error('❌ Accessibility audit failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  runAccessibilityAudit,
  analyzeResults,
  ACCESSIBILITY_CONFIG,
};

import { buildConfig } from 'payload'
import { postgresAdapter } from '@payloadcms/db-postgres'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import { seoPlugin } from '@payloadcms/plugin-seo'
import { formBuilderPlugin } from '@payloadcms/plugin-form-builder'
import path from 'path'
import dotenv from 'dotenv'
import sharp from 'sharp'

// Import collections
import { Users } from './src/collections/Users'
import { Media } from './src/collections/Media'
import { Leads } from './src/collections/Leads'
import { Locations } from './src/collections/Locations'
import { PropertyTypes } from './src/collections/PropertyTypes'
// import { AIConversations } from './src/collections/AIConversations'
// import { PropertyMatches } from './src/collections/PropertyMatches'

dotenv.config({ path: '.env.local' })

export default buildConfig({
  admin: {
    user: 'users',
    bundler: 'webpack',
  },
  sharp,
  serverURL: process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000',
  collections: [Users, Media, Leads, Locations, PropertyTypes], // AIConversations, PropertyMatches - disabled for Story 1.2 rollback
  editor: lexicalEditor({}),
  plugins: [
    seoPlugin({
      collections: ['locations', 'property-types'],
      uploadsCollection: 'media',
    }),
    formBuilderPlugin({
      fields: {
        payment: false,
      },
    }),
  ],
  secret: process.env.PAYLOAD_SECRET || '7cc8b36cf86d42ad284f6fbb6b1eb85b473cbe8e22eb8d6df3c23f56f0e6c7e0',
  typescript: {
    outputFile: './src/payload-types.ts',
  },
  db: postgresAdapter({
    pool: {
      connectionString: process.env.SUPABASE_DATABASE_URL || process.env.DATABASE_URL || 'postgresql://localhost:5432/fallback',
      // Add connection pool optimization for better performance
      max: 10, // Maximum number of connections in pool
      min: 2,  // Minimum number of connections in pool
      idleTimeoutMillis: 30000, // Close idle connections after 30 seconds
      connectionTimeoutMillis: 10000, // Connection timeout 10 seconds
      acquireTimeoutMillis: 10000, // Acquire connection timeout 10 seconds
    },
    schemaName: 'payload_cms',
    // Add migration settings for better schema handling
    migrationDir: './src/migrations',
    push: false, // Disable automatic schema push in development
  }),
})

/**
 * Database Health Check Utility
 * Bali Property Scout Website
 * 
 * Utility for monitoring database connection health and providing
 * fallback mechanisms when database is unavailable.
 */

'use client';

import { getPayloadClient } from './payload';

export interface DatabaseHealthStatus {
  isHealthy: boolean;
  responseTime: number;
  error?: string;
  lastChecked: Date;
}

let healthStatus: DatabaseHealthStatus | null = null;
let healthCheckInProgress = false;

/**
 * Check database connection health
 */
export async function checkDatabaseHealth(): Promise<DatabaseHealthStatus> {
  // Return cached status if check is already in progress
  if (healthCheckInProgress && healthStatus) {
    return healthStatus;
  }

  healthCheckInProgress = true;
  const startTime = Date.now();

  try {
    console.log('🔍 Checking database health...');
    
    // Try to get payload client with timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Database health check timeout')), 5000);
    });

    const healthCheckPromise = getPayloadClient().then(async (payload) => {
      // Simple query to test connection
      await payload.find({
        collection: 'users',
        limit: 1,
      });
      return true;
    });

    await Promise.race([healthCheckPromise, timeoutPromise]);

    const responseTime = Date.now() - startTime;
    healthStatus = {
      isHealthy: true,
      responseTime,
      lastChecked: new Date(),
    };

    console.log(`✅ Database healthy - Response time: ${responseTime}ms`);
    
  } catch (error) {
    const responseTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    healthStatus = {
      isHealthy: false,
      responseTime,
      error: errorMessage,
      lastChecked: new Date(),
    };

    console.warn(`⚠️ Database unhealthy - ${errorMessage} (${responseTime}ms)`);
  } finally {
    healthCheckInProgress = false;
  }

  return healthStatus;
}

/**
 * Get cached health status without performing new check
 */
export function getCachedHealthStatus(): DatabaseHealthStatus | null {
  return healthStatus;
}

/**
 * Check if database should be used based on health status
 */
export function shouldUseDatabaseFallback(): boolean {
  if (!healthStatus) {
    return false; // No health check performed yet
  }

  // Use fallback if database is unhealthy or response time > 3 seconds
  return !healthStatus.isHealthy || healthStatus.responseTime > 3000;
}

/**
 * Development mode check - disable database in certain conditions
 */
export function isDatabaseDisabledInDev(): boolean {
  if (process.env.NODE_ENV !== 'development') {
    return false;
  }

  // Disable database if explicitly set
  if (process.env.DISABLE_DATABASE === 'true') {
    return true;
  }

  // Disable database if no connection string provided
  if (!process.env.DATABASE_URL && !process.env.SUPABASE_DATABASE_URL) {
    return true;
  }

  return false;
}

/**
 * Enhanced database client with health checking
 */
export async function getHealthyPayloadClient() {
  // Check if database should be disabled in development
  if (isDatabaseDisabledInDev()) {
    throw new Error('Database disabled in development mode');
  }

  // Check health status
  const health = await checkDatabaseHealth();
  
  if (!health.isHealthy) {
    throw new Error(`Database unhealthy: ${health.error}`);
  }

  return getPayloadClient();
}

/**
 * Periodic health check (for background monitoring)
 */
export function startHealthMonitoring(intervalMs: number = 30000) {
  if (typeof window === 'undefined') {
    return; // Only run in browser
  }

  const interval = setInterval(async () => {
    try {
      await checkDatabaseHealth();
    } catch (error) {
      console.warn('Background health check failed:', error);
    }
  }, intervalMs);

  // Cleanup function
  return () => clearInterval(interval);
}

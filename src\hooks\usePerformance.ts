/**
 * Performance Monitoring Hook
 * 
 * React hook for monitoring component performance and Web Vitals
 */

'use client';

import { useEffect, useRef, useCallback } from 'react';
import { measureComponentRender, type PerformanceMetric } from '@/lib/performance';

export interface UsePerformanceOptions {
  /** Component name for logging */
  componentName?: string;
  /** Whether to measure render time */
  measureRender?: boolean;
  /** Whether to log slow renders */
  logSlowRenders?: boolean;
  /** Threshold for slow render warning (ms) */
  slowRenderThreshold?: number;
  /** Whether to measure effect execution time */
  measureEffects?: boolean;
}

export interface PerformanceData {
  /** Average render time */
  averageRenderTime: number;
  /** Maximum render time */
  maxRenderTime: number;
  /** Total number of renders */
  renderCount: number;
  /** Slow renders count */
  slowRenderCount: number;
  /** Last render time */
  lastRenderTime: number;
}

/**
 * Hook for monitoring component performance
 */
export function usePerformance(options: UsePerformanceOptions = {}): PerformanceData {
  const {
    componentName = 'Unknown Component',
    measureRender = true,
    logSlowRenders = true,
    slowRenderThreshold = 16, // 60fps = 16.67ms per frame
    measureEffects = false,
  } = options;

  const renderTimes = useRef<number[]>([]);
  const renderStartTime = useRef<number>(0);
  const effectTimes = useRef<number[]>([]);

  // Start measuring render time
  if (measureRender) {
    renderStartTime.current = performance.now();
  }

  // Measure render completion
  useEffect(() => {
    if (measureRender) {
      const renderTime = performance.now() - renderStartTime.current;
      renderTimes.current.push(renderTime);

      // Keep only last 100 measurements to prevent memory leaks
      if (renderTimes.current.length > 100) {
        renderTimes.current = renderTimes.current.slice(-100);
      }

      // Log slow renders
      if (logSlowRenders && renderTime > slowRenderThreshold) {
        console.warn(
          `[Performance] ${componentName} slow render: ${renderTime.toFixed(2)}ms (threshold: ${slowRenderThreshold}ms)`
        );
      }
    }
  });

  // Measure effect execution time
  const measureEffect = useCallback(
    (effectName: string, effectFn: () => void | (() => void)) => {
      if (!measureEffects) {
        return effectFn();
      }

      const startTime = performance.now();
      const cleanup = effectFn();
      const endTime = performance.now();
      const effectTime = endTime - startTime;

      effectTimes.current.push(effectTime);

      if (effectTime > 5) { // Effects taking more than 5ms
        console.warn(
          `[Performance] ${componentName} slow effect (${effectName}): ${effectTime.toFixed(2)}ms`
        );
      }

      return cleanup;
    },
    [componentName, measureEffects]
  );

  // Calculate performance data
  const performanceData: PerformanceData = {
    averageRenderTime: renderTimes.current.length > 0 
      ? renderTimes.current.reduce((sum, time) => sum + time, 0) / renderTimes.current.length 
      : 0,
    maxRenderTime: renderTimes.current.length > 0 
      ? Math.max(...renderTimes.current) 
      : 0,
    renderCount: renderTimes.current.length,
    slowRenderCount: renderTimes.current.filter(time => time > slowRenderThreshold).length,
    lastRenderTime: renderTimes.current[renderTimes.current.length - 1] || 0,
  };

  return performanceData;
}

/**
 * Hook for measuring async operations
 */
export function useAsyncPerformance() {
  const measureAsync = useCallback(async <T>(
    operationName: string,
    asyncOperation: () => Promise<T>
  ): Promise<T> => {
    const startTime = performance.now();
    
    try {
      const result = await asyncOperation();
      const endTime = performance.now();
      const duration = endTime - startTime;

      console.log(`[Performance] ${operationName}: ${duration.toFixed(2)}ms`);

      if (duration > 1000) { // Operations taking more than 1 second
        console.warn(`[Performance] Slow async operation: ${operationName} took ${duration.toFixed(2)}ms`);
      }

      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      console.error(`[Performance] Failed operation: ${operationName} failed after ${duration.toFixed(2)}ms`, error);
      throw error;
    }
  }, []);

  return { measureAsync };
}

/**
 * Hook for monitoring intersection observer performance
 */
export function useIntersectionPerformance(
  callback: IntersectionObserverCallback,
  options?: IntersectionObserverInit
) {
  const observerRef = useRef<IntersectionObserver | null>(null);

  useEffect(() => {
    if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
      return;
    }

    const performanceCallback: IntersectionObserverCallback = (entries, observer) => {
      const startTime = performance.now();
      callback(entries, observer);
      const endTime = performance.now();
      const duration = endTime - startTime;

      if (duration > 5) { // Intersection callbacks taking more than 5ms
        console.warn(`[Performance] Slow intersection callback: ${duration.toFixed(2)}ms`);
      }
    };

    observerRef.current = new IntersectionObserver(performanceCallback, options);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [callback, options]);

  return observerRef.current;
}

/**
 * Hook for monitoring scroll performance
 */
export function useScrollPerformance(
  callback: (event: Event) => void,
  throttleMs: number = 16 // 60fps
) {
  const lastCallTime = useRef<number>(0);
  const frameId = useRef<number>(0);

  useEffect(() => {
    const throttledCallback = (event: Event) => {
      const now = performance.now();
      
      if (now - lastCallTime.current >= throttleMs) {
        lastCallTime.current = now;
        
        // Cancel previous frame if still pending
        if (frameId.current) {
          cancelAnimationFrame(frameId.current);
        }

        // Schedule callback for next frame
        frameId.current = requestAnimationFrame(() => {
          const startTime = performance.now();
          callback(event);
          const endTime = performance.now();
          const duration = endTime - startTime;

          if (duration > 8) { // Scroll handlers taking more than 8ms
            console.warn(`[Performance] Slow scroll handler: ${duration.toFixed(2)}ms`);
          }
        });
      }
    };

    window.addEventListener('scroll', throttledCallback, { passive: true });

    return () => {
      window.removeEventListener('scroll', throttledCallback);
      if (frameId.current) {
        cancelAnimationFrame(frameId.current);
      }
    };
  }, [callback, throttleMs]);
}

/**
 * Hook for monitoring resize performance
 */
export function useResizePerformance(
  callback: (event: Event) => void,
  debounceMs: number = 250
) {
  const timeoutId = useRef<NodeJS.Timeout>();

  useEffect(() => {
    const debouncedCallback = (event: Event) => {
      if (timeoutId.current) {
        clearTimeout(timeoutId.current);
      }

      timeoutId.current = setTimeout(() => {
        const startTime = performance.now();
        callback(event);
        const endTime = performance.now();
        const duration = endTime - startTime;

        if (duration > 10) { // Resize handlers taking more than 10ms
          console.warn(`[Performance] Slow resize handler: ${duration.toFixed(2)}ms`);
        }
      }, debounceMs);
    };

    window.addEventListener('resize', debouncedCallback);

    return () => {
      window.removeEventListener('resize', debouncedCallback);
      if (timeoutId.current) {
        clearTimeout(timeoutId.current);
      }
    };
  }, [callback, debounceMs]);
}

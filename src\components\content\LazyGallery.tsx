/**
 * Lazy-loaded Gallery Component
 * 
 * This component dynamically imports the Gallery component to reduce initial bundle size
 */

'use client';

import { Suspense, lazy } from 'react';
import { GalleryProps } from './Gallery';

// Lazy load the Gallery component
const Gallery = lazy(() => import('./Gallery').then(module => ({ default: module.Gallery })));

// Loading component for Gallery
function GalleryLoading() {
  return (
    <div className="w-full h-64 bg-gray-100 animate-pulse rounded-lg flex items-center justify-center">
      <div className="text-gray-500">Loading gallery...</div>
    </div>
  );
}

// Lazy Gallery wrapper component
export function LazyGallery(props: GalleryProps) {
  return (
    <Suspense fallback={<GalleryLoading />}>
      <Gallery {...props} />
    </Suspense>
  );
}

export default LazyGallery;

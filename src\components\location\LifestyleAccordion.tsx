/**
 * Lifestyle Accordion Component
 * Bali Property Scout Website
 * 
 * Collapsible accordion for lifestyle and amenities information
 * with icons, animations, and detailed content sections.
 */

'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import ChatbotCTA from '@/components/ui/ChatbotCTA';

export interface LifestyleItem {
  id: string;
  title: string;
  icon: string;
  summary: string;
  details: {
    description: string;
    features: string[];
    highlights: string[];
  };
  ctaText?: string;
  ctaQuery?: {
    type: string;
    context: string;
    location?: string;
  };
}

export interface LifestyleAccordionProps {
  /** Location name for context */
  locationName: string;
  /** Lifestyle items to display */
  items: LifestyleItem[];
  /** Additional CSS classes */
  className?: string;
}

export const LifestyleAccordion: React.FC<LifestyleAccordionProps> = ({
  locationName,
  items,
  className,
}) => {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const toggleItem = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  return (
    <div className={cn('space-y-4', className)}>
      {items.map((item) => {
        const isExpanded = expandedItems.has(item.id);
        
        return (
          <div
            key={item.id}
            className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
          >
            {/* Accordion Header */}
            <button
              onClick={() => toggleItem(item.id)}
              className="w-full p-6 text-left hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-inset"
              aria-expanded={isExpanded}
              aria-controls={`accordion-content-${item.id}`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-emerald-100 rounded-xl flex items-center justify-center flex-shrink-0">
                    <span className="text-2xl">{item.icon}</span>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-gray-900 mb-1">
                      {item.title}
                    </h3>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {item.summary}
                    </p>
                  </div>
                </div>
                <div className="flex-shrink-0 ml-4">
                  <div
                    className={cn(
                      'w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center transition-transform duration-300',
                      isExpanded && 'rotate-180'
                    )}
                  >
                    <svg
                      className="w-4 h-4 text-emerald-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </button>

            {/* Accordion Content */}
            <div
              id={`accordion-content-${item.id}`}
              className={cn(
                'overflow-hidden transition-all duration-300 ease-in-out',
                isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
              )}
            >
              <div className="px-6 pb-6">
                <div className="border-t border-gray-100 pt-6">
                  {/* Description */}
                  <div className="mb-6">
                    <p className="text-gray-700 leading-relaxed">
                      {item.details.description}
                    </p>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    {/* Features */}
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3">
                        Available Features
                      </h4>
                      <ul className="space-y-2">
                        {item.details.features.map((feature, index) => (
                          <li key={index} className="flex items-start">
                            <div className="w-2 h-2 bg-emerald-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            <span className="text-sm text-gray-600">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Highlights */}
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3">
                        Why Choose {locationName}
                      </h4>
                      <ul className="space-y-2">
                        {item.details.highlights.map((highlight, index) => (
                          <li key={index} className="flex items-start">
                            <div className="w-2 h-2 bg-teal-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            <span className="text-sm text-gray-600">{highlight}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {/* CTA Button */}
                  {item.ctaText && item.ctaQuery && (
                    <div className="mt-6 pt-4 border-t border-gray-100">
                      <ChatbotCTA
                        query={{
                          ...item.ctaQuery,
                          location: locationName,
                        }}
                        className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-6 py-3 rounded-xl font-medium text-sm transition-all duration-300 hover:shadow-lg hover:scale-105"
                      >
                        {item.ctaText}
                      </ChatbotCTA>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default LifestyleAccordion;

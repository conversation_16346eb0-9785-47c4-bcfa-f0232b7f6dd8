# 🚀 Deployment Guide - Bali Real Estate Website

## 📋 **Pre-Deployment Checklist**

### **1. Environment Variables Setup**

**Required Environment Variables for Vercel:**

```bash
# Database (Optional for static build)
DATABASE_URI=****************************************/database

# Payload CMS
PAYLOAD_SECRET=your-super-secret-payload-key

# Site Configuration
NEXT_PUBLIC_SITE_URL=https://your-domain.com
NEXT_PUBLIC_SITE_NAME="Bali Real Estate"

# Analytics
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# Build Configuration
SKIP_ENV_VALIDATION=true
NODE_ENV=production
```

### **2. Database Setup (Optional)**

The website can deploy **without a database** for static content. If you want dynamic CMS content:

1. **Set up PostgreSQL database** (Supabase, Railway, or Vercel Postgres)
2. **Add DATABASE_URI** to environment variables
3. **Run migrations** after first deployment

### **3. Vercel Deployment Steps**

#### **Option A: GitHub Integration (Recommended)**

1. **Push to GitHub:**
   ```bash
   git add .
   git commit -m "Ready for deployment"
   git push origin main
   ```

2. **Connect to Vercel:**
   - Go to [vercel.com](https://vercel.com)
   - Import your GitHub repository
   - Configure environment variables
   - Deploy!

#### **Option B: Vercel CLI**

1. **Install Vercel CLI:**
   ```bash
   npm i -g vercel
   ```

2. **Login and Deploy:**
   ```bash
   vercel login
   vercel --prod
   ```

### **4. Environment Variables in Vercel Dashboard**

Add these in your Vercel project settings:

| Variable | Value | Required |
|----------|-------|----------|
| `PAYLOAD_SECRET` | Random 32+ character string | ✅ Yes |
| `NEXT_PUBLIC_SITE_URL` | Your domain (e.g., https://bali-real-estate.com) | ✅ Yes |
| `NEXT_PUBLIC_GA_ID` | Google Analytics ID | ⚠️ Recommended |
| `DATABASE_URI` | PostgreSQL connection string | ❌ Optional |
| `SKIP_ENV_VALIDATION` | `true` | ✅ Yes |

---

## 🔧 **Build Configuration**

### **Static Build (No Database)**
The website is configured to build statically without a database connection. It will use predefined location and property type data.

### **Dynamic Build (With Database)**
If you provide a `DATABASE_URI`, the website will fetch real data from Payload CMS during build time.

---

## 🚨 **Troubleshooting Common Issues**

### **Issue 1: Database Connection Error**
```
ERROR: cannot connect to Postgres. Details: connect ECONNREFUSED
```

**Solution:**
- This is expected if no database is configured
- The website will use static fallback data
- To fix: Add valid `DATABASE_URI` environment variable

### **Issue 2: Build Timeout**
```
Build exceeded maximum duration
```

**Solution:**
- Reduce build complexity
- Use static generation instead of server-side rendering
- Optimize image sizes

### **Issue 3: Environment Variable Issues**
```
Missing required environment variables
```

**Solution:**
- Add `SKIP_ENV_VALIDATION=true` to environment variables
- Ensure all required variables are set in Vercel dashboard

---

## 📊 **Post-Deployment Checklist**

### **1. Verify Core Functionality**
- [ ] Homepage loads correctly
- [ ] Location pages work
- [ ] Contact form submits
- [ ] SEO meta tags are present
- [ ] Analytics tracking works

### **2. Performance Check**
- [ ] Lighthouse score > 90
- [ ] Core Web Vitals are green
- [ ] Images load optimized formats (WebP/AVIF)
- [ ] Service worker is active

### **3. SEO Verification**
- [ ] Sitemap.xml is accessible
- [ ] Robots.txt is correct
- [ ] Google Search Console setup
- [ ] Social media meta tags work

### **4. Analytics Setup**
- [ ] Google Analytics 4 tracking
- [ ] Google Tag Manager (if used)
- [ ] Conversion tracking setup
- [ ] Performance monitoring active

---

## 🔄 **Continuous Deployment**

### **Automatic Deployments**
- **Main branch** → Production deployment
- **Development branch** → Preview deployment
- **Pull requests** → Preview deployments

### **Manual Deployments**
```bash
# Deploy to production
vercel --prod

# Deploy preview
vercel
```

---

## 🛡️ **Security Configuration**

### **Headers (Configured in vercel.json)**
- ✅ X-Content-Type-Options: nosniff
- ✅ X-Frame-Options: DENY
- ✅ X-XSS-Protection: 1; mode=block
- ✅ Referrer-Policy: origin-when-cross-origin

### **HTTPS**
- ✅ Automatic HTTPS via Vercel
- ✅ HTTP to HTTPS redirects
- ✅ HSTS headers

---

## 📈 **Monitoring & Analytics**

### **Built-in Monitoring**
- **Vercel Analytics** - Performance monitoring
- **Google Analytics 4** - User behavior tracking
- **Core Web Vitals** - Performance metrics
- **Error Tracking** - Runtime error monitoring

### **Custom Monitoring**
- Service worker performance
- Cache hit rates
- Form submission tracking
- Lead generation metrics

---

## 🎯 **Domain Configuration**

### **Custom Domain Setup**
1. **Add domain in Vercel dashboard**
2. **Configure DNS records:**
   ```
   Type: CNAME
   Name: www
   Value: cname.vercel-dns.com
   
   Type: A
   Name: @
   Value: 76.76.19.61
   ```
3. **Verify SSL certificate**

### **Subdomain Setup**
- `www.bali-real-estate.com` → Main site
- `admin.bali-real-estate.com` → CMS admin (if enabled)
- `api.bali-real-estate.com` → API endpoints (if needed)

---

## 🚀 **Production Optimization**

### **Performance Optimizations**
- ✅ Image optimization with Next.js Image
- ✅ Automatic code splitting
- ✅ Service worker caching
- ✅ Static generation where possible
- ✅ CDN distribution via Vercel Edge Network

### **SEO Optimizations**
- ✅ Server-side rendering for critical pages
- ✅ Automatic sitemap generation
- ✅ Structured data markup
- ✅ Open Graph and Twitter Card meta tags
- ✅ Canonical URLs

---

## 📞 **Support & Maintenance**

### **Regular Maintenance Tasks**
- **Weekly:** Check analytics and performance
- **Monthly:** Update dependencies
- **Quarterly:** Review and optimize content
- **Annually:** Security audit and updates

### **Emergency Procedures**
- **Rollback:** Use Vercel dashboard to rollback to previous deployment
- **Hotfix:** Push critical fixes to main branch for immediate deployment
- **Monitoring:** Set up alerts for downtime and performance issues

---

## ✅ **Deployment Success!**

Your Bali Real Estate website is now live and optimized for:
- 🚀 **Performance** - Lightning-fast loading
- 🔍 **SEO** - Search engine optimized
- 📱 **Mobile** - Responsive design
- 🛡️ **Security** - Production-ready security headers
- 📊 **Analytics** - Comprehensive tracking
- 🎯 **Conversions** - Lead generation optimized

**Live URL:** https://your-domain.com  
**Admin Panel:** https://your-domain.com/admin (if database configured)  
**Analytics:** Google Analytics Dashboard  

🎉 **Congratulations! Your website is production-ready!** 🎉

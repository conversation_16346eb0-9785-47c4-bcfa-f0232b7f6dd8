/**
 * Accessibility Testing Component
 * 
 * Uses axe-core to test accessibility compliance in development
 */

'use client';

import { useEffect } from 'react';

interface AccessibilityViolation {
  id: string;
  impact: 'minor' | 'moderate' | 'serious' | 'critical';
  description: string;
  help: string;
  helpUrl: string;
  nodes: Array<{
    target: string[];
    html: string;
    failureSummary: string;
  }>;
}

interface AccessibilityResults {
  violations: AccessibilityViolation[];
  passes: Array<{ id: string; description: string }>;
  incomplete: Array<{ id: string; description: string }>;
  inapplicable: Array<{ id: string; description: string }>;
}

export function AccessibilityTester() {
  useEffect(() => {
    // Only run in development
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    // Dynamic import to avoid including axe-core in production
    Promise.all([
      import('@axe-core/react'),
      import('react'),
      import('react-dom')
    ]).then(([axe, React, ReactDOM]) => {
      axe.default(React.default, ReactDOM.default, 1000, {
        // WCAG 2.1 AA compliance rules
        rules: {
          // Level A rules
          'area-alt': { enabled: true },
          'aria-allowed-attr': { enabled: true },
          'aria-hidden-body': { enabled: true },
          'aria-hidden-focus': { enabled: true },
          'aria-labelledby': { enabled: true },
          'aria-required-attr': { enabled: true },
          'aria-required-children': { enabled: true },
          'aria-required-parent': { enabled: true },
          'aria-roles': { enabled: true },
          'aria-valid-attr': { enabled: true },
          'aria-valid-attr-value': { enabled: true },
          'button-name': { enabled: true },
          'bypass': { enabled: true },
          'color-contrast': { enabled: true },
          'document-title': { enabled: true },
          'duplicate-id': { enabled: true },
          'form-field-multiple-labels': { enabled: true },
          'frame-title': { enabled: true },
          'html-has-lang': { enabled: true },
          'html-lang-valid': { enabled: true },
          'image-alt': { enabled: true },
          'input-button-name': { enabled: true },
          'input-image-alt': { enabled: true },
          'label': { enabled: true },
          'link-name': { enabled: true },
          'list': { enabled: true },
          'listitem': { enabled: true },
          'meta-refresh': { enabled: true },
          'meta-viewport': { enabled: true },
          'object-alt': { enabled: true },
          'role-img-alt': { enabled: true },
          'scrollable-region-focusable': { enabled: true },
          'server-side-image-map': { enabled: true },
          'svg-img-alt': { enabled: true },
          'td-headers-attr': { enabled: true },
          'th-has-data-cells': { enabled: true },
          'valid-lang': { enabled: true },
          'video-caption': { enabled: true },

          // Level AA rules
          'color-contrast-enhanced': { enabled: false }, // AAA rule, not AA
          'focus-order-semantics': { enabled: true },
          'hidden-content': { enabled: true },
          'landmark-banner-is-top-level': { enabled: true },
          'landmark-complementary-is-top-level': { enabled: true },
          'landmark-contentinfo-is-top-level': { enabled: true },
          'landmark-main-is-top-level': { enabled: true },
          'landmark-no-duplicate-banner': { enabled: true },
          'landmark-no-duplicate-contentinfo': { enabled: true },
          'landmark-one-main': { enabled: true },
          'landmark-unique': { enabled: true },
          'page-has-heading-one': { enabled: true },
          'region': { enabled: true },
          'skip-link': { enabled: true },
          'tabindex': { enabled: true },
        },
        tags: ['wcag2a', 'wcag2aa', 'wcag21aa'],
      });
    }).catch((error) => {
      console.error('Failed to load axe-core:', error);
    });
  }, []);

  return null; // This component doesn't render anything
}

// Manual accessibility testing function
export async function runAccessibilityAudit(): Promise<AccessibilityResults | null> {
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    const axe = await import('axe-core');
    
    const results = await axe.default.run(document, {
      rules: {
        // WCAG 2.1 AA rules
        'color-contrast': { enabled: true },
        'keyboard-navigation': { enabled: true },
        'focus-management': { enabled: true },
        'aria-compliance': { enabled: true },
        'semantic-markup': { enabled: true },
      },
      tags: ['wcag2a', 'wcag2aa', 'wcag21aa'],
    });

    // Log results in development
    if (process.env.NODE_ENV === 'development') {
      console.group('🔍 Accessibility Audit Results');
      
      if (results.violations.length > 0) {
        console.error(`❌ ${results.violations.length} accessibility violations found:`);
        results.violations.forEach((violation) => {
          console.group(`${violation.impact?.toUpperCase()}: ${violation.id}`);
          console.log('Description:', violation.description);
          console.log('Help:', violation.help);
          console.log('Help URL:', violation.helpUrl);
          console.log('Affected elements:', violation.nodes.length);
          violation.nodes.forEach((node, index) => {
            console.log(`Element ${index + 1}:`, node.target.join(' > '));
            console.log('HTML:', node.html);
            if (node.failureSummary) {
              console.log('Issue:', node.failureSummary);
            }
          });
          console.groupEnd();
        });
      } else {
        console.log('✅ No accessibility violations found!');
      }

      console.log(`✅ ${results.passes.length} accessibility checks passed`);
      console.log(`⚠️ ${results.incomplete.length} checks incomplete`);
      console.log(`ℹ️ ${results.inapplicable.length} checks not applicable`);
      console.groupEnd();
    }

    return results;
  } catch (error) {
    console.error('Accessibility audit failed:', error);
    return null;
  }
}

// Accessibility testing hook
export function useAccessibilityTesting() {
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // Run audit after component mount
      const timer = setTimeout(() => {
        runAccessibilityAudit();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, []);
}

// Color contrast checker
export function checkColorContrast(foreground: string, background: string): {
  ratio: number;
  wcagAA: boolean;
  wcagAAA: boolean;
} {
  // Convert hex to RGB
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  };

  // Calculate relative luminance
  const getLuminance = (r: number, g: number, b: number) => {
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  };

  const fg = hexToRgb(foreground);
  const bg = hexToRgb(background);

  if (!fg || !bg) {
    return { ratio: 0, wcagAA: false, wcagAAA: false };
  }

  const fgLuminance = getLuminance(fg.r, fg.g, fg.b);
  const bgLuminance = getLuminance(bg.r, bg.g, bg.b);

  const ratio = (Math.max(fgLuminance, bgLuminance) + 0.05) / 
                (Math.min(fgLuminance, bgLuminance) + 0.05);

  return {
    ratio: Math.round(ratio * 100) / 100,
    wcagAA: ratio >= 4.5,
    wcagAAA: ratio >= 7,
  };
}

export default AccessibilityTester;

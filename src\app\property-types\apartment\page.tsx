/**
 * Apartments Property Type Page
 * PRD Requirement: Create apartments page marked as "to be built"
 */

import { Metadata } from 'next';
import Link from 'next/link';
import { Breadcrumbs } from '@/components/common/Breadcrumbs';

export const metadata: Metadata = {
  title: 'Apartments in Bali - Modern Urban Living | Bali Real Estate',
  description: 'Modern apartments in urban hubs with shared facilities such as pools and gyms. Perfect for young professionals and digital nomads. Rental: $500-2,000/month, Purchase: $80,000-400,000.',
  keywords: 'bali apartment rental, modern apartment bali, serviced apartment bali, rent apartment bali, buy apartment bali, bali apartment living, modern apartments bali, urban living bali, digital nomad bali housing, expat living in bali',
};

export default function ApartmentPage() {
  return (
    <main className="min-h-screen bg-gray-50">
      {/* Breadcrumbs */}
      <div className="bg-white py-4">
        <div className="max-w-6xl mx-auto px-4">
          <Breadcrumbs />
        </div>
      </div>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-20">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                Apartments in Bali
              </h1>
              <p className="text-xl mb-8 text-blue-100 leading-relaxed">
                Modern apartments in urban hubs with shared facilities such as pools and gyms.
              </p>
              
              {/* Key Stats */}
              <div className="grid grid-cols-2 gap-4 mb-8">
                <div className="bg-white/10 rounded-lg p-4">
                  <div className="text-2xl font-bold text-white">$500-2,000</div>
                  <div className="text-blue-200 text-sm">Monthly Rental</div>
                </div>
                <div className="bg-white/10 rounded-lg p-4">
                  <div className="text-2xl font-bold text-white">$80K-400K</div>
                  <div className="text-blue-200 text-sm">Purchase Price</div>
                </div>
              </div>

              {/* Perfect For */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold mb-3 text-blue-100">Perfect for:</h3>
                <div className="flex flex-wrap gap-2">
                  <span className="bg-white/20 px-3 py-1 rounded-full text-sm">Young Professionals</span>
                  <span className="bg-white/20 px-3 py-1 rounded-full text-sm">Digital Nomads</span>
                  <span className="bg-white/20 px-3 py-1 rounded-full text-sm">Urban Lifestyle</span>
                </div>
              </div>

              {/* PRD Compliant CTAs */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="https://app.balipropertyscout.com?query=rent-apartment-in-bali"
                  className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl text-center"
                >
                  Find Apartments
                </Link>
                <Link
                  href="https://app.balipropertyscout.com?query=buy-apartment-in-bali"
                  className="bg-white/10 hover:bg-white/20 text-white border-2 border-white/30 hover:border-white/50 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 backdrop-blur-sm text-center"
                >
                  Buy an Apartment
                </Link>
              </div>
            </div>

            <div className="relative">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <h3 className="text-xl font-bold mb-4">Coming Soon</h3>
                <p className="text-blue-100 mb-6">
                  We're currently developing our apartment listings and detailed information. 
                  Our chatbot can help you with immediate apartment inquiries.
                </p>
                <div className="space-y-3">
                  <div className="flex items-center text-blue-100">
                    <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Modern amenities
                  </div>
                  <div className="flex items-center text-blue-100">
                    <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Shared facilities
                  </div>
                  <div className="flex items-center text-blue-100">
                    <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Urban locations
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Under Construction Notice */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <div className="bg-white rounded-lg shadow-lg p-12">
            <div className="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-8">
              <svg className="w-12 h-12 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 8h1m-1-4h1m4 4h1m-1-4h1" />
              </svg>
            </div>
            
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Apartment Listings Coming Soon
            </h2>
            
            <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
              We're working hard to bring you comprehensive apartment listings across Bali. 
              In the meantime, our AI assistant can help you with apartment inquiries and connect you with available options.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Link
                href="https://app.balipropertyscout.com?query=rent-apartment-in-bali"
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
              >
                Find Apartments Now
              </Link>
              <Link
                href="/contact"
                className="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-3 rounded-lg font-semibold transition-colors"
              >
                Contact Our Team
              </Link>
            </div>

            <div className="text-gray-500 text-sm">
              <p>Available property types: <Link href="/property-types/villa" className="text-blue-600 hover:underline">Villas</Link> • <Link href="/property-types/guesthouse" className="text-blue-600 hover:underline">Guesthouses</Link></p>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}

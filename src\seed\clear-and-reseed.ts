import { getPayload } from 'payload'
import config from '../../payload.config'

const clearAndReseed = async (): Promise<void> => {
  try {
    const payload = await getPayload({ config })

    console.log('🗑️ Clearing existing locations...')
    
    // Get all existing locations
    const existingLocations = await payload.find({
      collection: 'locations',
      limit: 100,
    })

    // Delete all existing locations
    for (const location of existingLocations.docs) {
      await payload.delete({
        collection: 'locations',
        id: location.id,
      })
      console.log(`❌ Deleted location: ${location.name}`)
    }

    console.log('🏝️ Creating new locations with correct structure...')

    // Seed Locations with correct structure
    const locations = [
      {
        name: 'Canggu',
        slug: 'canggu',
        description: {
          root: {
            type: 'root',
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    type: 'text',
                    text: 'Surf town paradise with digital nomad community and rice field views'
                  }
                ]
              }
            ]
          }
        },
        lifestyle: {
          root: {
            type: 'root',
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    type: 'text',
                    text: 'The lifestyle in Canggu perfectly embodies the modern Bali experience, where traditional Indonesian culture meets international sophistication. This vibrant coastal community has become synonymous with the digital nomad lifestyle, offering an unparalleled blend of work, wellness, and adventure that attracts professionals from around the globe.'
                  }
                ]
              }
            ]
          }
        },
        priority: 'zeer-hoog',
        priceRange: {
          rentalMin: 800,
          rentalMax: 3000,
          saleMin: 150000,
          saleMax: 800000,
        },
        amenities: [
          { amenity: 'World-class surfing beaches' },
          { amenity: 'High-speed internet (100+ Mbps)' },
          { amenity: '20+ coworking spaces' },
          { amenity: 'International dining scene' },
          { amenity: 'Beach clubs and nightlife' },
          { amenity: 'Rice paddy views' },
        ],
      },
      {
        name: 'Ubud',
        slug: 'ubud',
        description: 'Cultural heart of Bali with wellness focus and jungle setting',
        lifestyle: 'Ubud\'s lifestyle revolves around wellness, spirituality, and cultural immersion, offering a completely different pace of life compared to Bali\'s beach destinations. This mountain town has become a global center for yoga, meditation, and holistic healing.',
        priority: 'zeer-hoog',
        priceRange: {
          rentalMin: 600,
          rentalMax: 2500,
          saleMin: 120000,
          saleMax: 600000,
        },
        amenities: [
          { amenity: 'Yoga and wellness centers' },
          { amenity: 'Traditional Balinese culture' },
          { amenity: 'Organic restaurants' },
          { amenity: 'Art galleries and museums' },
          { amenity: 'Rice terrace views' },
          { amenity: 'Spiritual retreats' },
        ],
      },
      {
        name: 'Seminyak',
        slug: 'seminyak',
        description: 'Upscale beach destination with luxury amenities and sophisticated dining',
        lifestyle: 'Seminyak epitomizes luxury living in Bali, offering a sophisticated lifestyle that rivals any international resort destination. This upscale area attracts affluent residents and visitors who appreciate fine dining, luxury shopping, premium spas, and exclusive beach clubs.',
        priority: 'hoog',
        priceRange: {
          rentalMin: 1000,
          rentalMax: 4000,
          saleMin: 200000,
          saleMax: 1000000,
        },
        amenities: [
          { amenity: 'Luxury beach clubs' },
          { amenity: 'Fine dining restaurants' },
          { amenity: 'High-end shopping' },
          { amenity: 'Premium spas' },
          { amenity: 'Sunset beaches' },
          { amenity: 'Sophisticated nightlife' },
        ],
      },
      {
        name: 'Sanur',
        slug: 'sanur',
        description: 'Family-friendly beach town with traditional Balinese culture and international schools',
        lifestyle: 'Sanur offers the perfect family-oriented lifestyle in Bali, combining traditional Balinese culture with modern amenities and international standards. This peaceful coastal town is renowned for its excellent schools, safe environment, and strong expat family community.',
        priority: 'hoog',
        priceRange: {
          rentalMin: 800,
          rentalMax: 2500,
          saleMin: 150000,
          saleMax: 600000,
        },
        amenities: [
          { amenity: 'International schools' },
          { amenity: 'Family-friendly beaches' },
          { amenity: 'Traditional Balinese culture' },
          { amenity: 'Quality healthcare' },
          { amenity: 'Safe neighborhoods' },
          { amenity: 'Expat family community' },
        ],
      },
      {
        name: 'Jimbaran',
        slug: 'jimbaran',
        description: 'Luxury beachfront destination famous for seafood dining and airport proximity',
        lifestyle: 'Jimbaran represents sophisticated luxury living in Bali, offering upscale beachfront properties, world-class dining, and premium amenities. This prestigious area attracts affluent expat families and investors who appreciate quality, convenience, and exclusivity.',
        priority: 'hoog',
        priceRange: {
          rentalMin: 1200,
          rentalMax: 5000,
          saleMin: 250000,
          saleMax: 1500000,
        },
        amenities: [
          { amenity: 'Famous seafood restaurants' },
          { amenity: 'Luxury beachfront properties' },
          { amenity: 'Airport proximity' },
          { amenity: 'Premium golf courses' },
          { amenity: 'Luxury resorts and spas' },
          { amenity: 'Upscale expat community' },
        ],
      },
    ]

    for (const location of locations) {
      try {
        const created = await payload.create({
          collection: 'locations',
          data: location,
        })
        console.log(`✅ Created location: ${location.name} (ID: ${created.id})`)
      } catch (error) {
        console.error(`❌ Error creating location ${location.name}:`, error)
      }
    }

    console.log('🎉 Clear and reseed completed successfully!')
    process.exit(0)
  } catch (error) {
    console.error('❌ Error during clear and reseed:', error)
    process.exit(1)
  }
}

clearAndReseed()

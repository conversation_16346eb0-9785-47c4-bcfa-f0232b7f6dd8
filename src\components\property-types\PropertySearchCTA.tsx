/**
 * Property Search CTA Component
 * Bali Property Scout Website
 * 
 * Final call-to-action section for property type pages with single button
 * that opens chatbot for property search with relevant query strings.
 * Optimized for conversion with compelling messaging and trust indicators.
 */

'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import ChatbotCTA from '@/components/ui/ChatbotCTA';

export interface PropertySearchCTAProps {
  /** Property type (villa, guesthouse, apartment) */
  propertyType: string;
  /** Custom headline */
  headline?: string;
  /** Custom description */
  description?: string;
  /** Custom CTA text */
  ctaText?: string;
  /** Background style variant */
  variant?: 'primary' | 'gradient' | 'image';
  /** Background image URL (for image variant) */
  backgroundImage?: string;
  /** Additional CSS classes */
  className?: string;
}

export const PropertySearchCTA: React.FC<PropertySearchCTAProps> = ({
  propertyType,
  headline,
  description,
  ctaText,
  variant = 'primary',
  backgroundImage,
  className,
}) => {
  // Default content based on property type
  const defaultContent = {
    villa: {
      headline: 'Ready to Find Your Perfect Villa?',
      description: 'Our AI-powered property matching connects you with luxury villas that match your lifestyle, budget, and investment goals. Start your personalized villa search today.',
      ctaText: 'Start Villa Search'
    },
    guesthouse: {
      headline: 'Ready for Authentic Bali Living?',
      description: 'Discover traditional guesthouses and co-living spaces that offer genuine cultural immersion and community experiences. Find your perfect home away from home.',
      ctaText: 'Find Your Guesthouse'
    },
    apartment: {
      headline: 'Find Your Modern Apartment?',
      description: 'Explore contemporary apartments with modern amenities and convenient locations. Perfect for urban living and professional lifestyles in Bali.',
      ctaText: 'Search Apartments'
    }
  };

  const content = defaultContent[propertyType as keyof typeof defaultContent] || defaultContent.villa;
  const finalHeadline = headline || content.headline;
  const finalDescription = description || content.description;
  const finalCtaText = ctaText || content.ctaText;

  // Background styling based on variant
  const getBackgroundStyle = () => {
    switch (variant) {
      case 'gradient':
        return {
          background: 'linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-primary-dark) 100%)'
        };
      case 'image':
        return {
          backgroundImage: backgroundImage ? `linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url(${backgroundImage})` : undefined,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundColor: 'var(--brand-primary)'
        };
      default:
        return {
          backgroundColor: 'var(--brand-primary)'
        };
    }
  };

  return (
    <section 
      className={cn('relative section-spacing text-white overflow-hidden', className)}
      style={getBackgroundStyle()}
    >
      {/* Decorative Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-24 -right-24 w-96 h-96 bg-white/5 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-24 -left-24 w-96 h-96 bg-white/8 rounded-full blur-3xl"></div>
        
        {/* Floating Icons */}
        <div className="absolute top-20 left-20 opacity-10">
          <svg className="w-16 h-16" fill="currentColor" viewBox="0 0 24 24">
            <path d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
        </div>
        <div className="absolute bottom-20 right-20 opacity-10">
          <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 24 24">
            <path d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        </div>
      </div>

      <div className="relative z-10 max-w-4xl mx-auto container-padding text-center">
        {/* Trust Indicators */}
        <div className="flex justify-center items-center gap-8 mb-8 text-white/80">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-white rounded-full"></div>
            <span className="text-sm font-medium">AI-Powered Matching</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-white rounded-full"></div>
            <span className="text-sm font-medium">500+ Properties</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-white rounded-full"></div>
            <span className="text-sm font-medium">Local Expertise</span>
          </div>
        </div>

        {/* Main Content */}
        <h2 className="heading-2 text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight">
          {finalHeadline}
        </h2>
        
        <p className="body-text-large text-lg md:text-xl mb-10 text-white/90 max-w-3xl mx-auto leading-relaxed">
          {finalDescription}
        </p>

        {/* Primary CTA */}
        <div className="mb-10">
          <ChatbotCTA
            query={{ 
              type: 'property-search', 
              propertyType: propertyType,
              context: 'final-cta',
              intent: 'consultation'
            }}
            variant="primary"
            size="xl"
            className="btn-primary text-xl font-bold px-12 py-6 min-h-[72px] shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300"
            style={{
              backgroundColor: 'var(--neutral-white)',
              borderColor: 'var(--neutral-white)',
              color: 'var(--brand-primary)'
            }}
          >
            {finalCtaText}
          </ChatbotCTA>
        </div>

        {/* Secondary Information */}
        <div className="space-y-4 text-white/80">
          <p className="text-sm">
            ✨ Get personalized recommendations in under 2 minutes
          </p>
          <div className="flex flex-wrap justify-center items-center gap-6 text-xs">
            <span>🔒 Secure & Private</span>
            <span>💬 Instant AI Response</span>
            <span>🎯 Tailored Results</span>
            <span>📞 Human Support Available</span>
          </div>
        </div>

        {/* Stats Section */}
        <div className="mt-16 pt-12 border-t border-white/20">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-2xl md:text-3xl font-bold mb-2">500+</div>
              <div className="text-sm text-white/80">Properties Available</div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl font-bold mb-2">95%</div>
              <div className="text-sm text-white/80">Match Accuracy</div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl font-bold mb-2">2min</div>
              <div className="text-sm text-white/80">Average Response</div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl font-bold mb-2">24/7</div>
              <div className="text-sm text-white/80">AI Support</div>
            </div>
          </div>
        </div>

        {/* Final Trust Message */}
        <div className="mt-12 p-6 bg-white/10 backdrop-blur-md rounded-2xl border border-white/20">
          <p className="text-sm text-white/90 leading-relaxed">
            <strong>Trusted by 500+ clients</strong> • Our AI-powered platform combines cutting-edge technology 
            with local market expertise to find your perfect {propertyType} in Bali. 
            Start your personalized search today and discover why we're Bali's #1 property matching service.
          </p>
        </div>
      </div>
    </section>
  );
};

export default PropertySearchCTA;

/**
 * Hydration-Safe Utilities
 * Bali Property Scout Website
 * 
 * Utilities to prevent hydration mismatches between server and client rendering.
 * These functions ensure consistent behavior across SSR and client-side rendering.
 */

'use client';

import React, { useState, useEffect } from 'react';

/**
 * Hook to safely detect if we're on the client side
 * Prevents hydration mismatches by returning false on server, true on client after mount
 */
export function useIsClient(): boolean {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

/**
 * Hook to safely use window object
 * Returns null on server, window object on client after mount
 */
export function useWindow(): Window | null {
  const [windowObj, setWindowObj] = useState<Window | null>(null);

  useEffect(() => {
    setWindowObj(window);
  }, []);

  return windowObj;
}

/**
 * Generate hydration-safe unique ID
 * Uses a counter on server, crypto.randomUUID on client
 */
let serverCounter = 0;

export function generateHydrationSafeId(prefix: string = 'id'): string {
  if (typeof window === 'undefined') {
    // Server-side: use counter to ensure consistency
    return `${prefix}_server_${++serverCounter}`;
  }
  
  // Client-side: use crypto API if available, fallback to timestamp + random
  if (crypto && crypto.randomUUID) {
    return `${prefix}_${crypto.randomUUID()}`;
  }
  
  // Fallback for older browsers
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return `${prefix}_${timestamp}_${random}`;
}

/**
 * Hydration-safe date formatting
 * Returns consistent format between server and client
 */
export function formatDateSafe(date: Date, options?: Intl.DateTimeFormatOptions): string {
  if (typeof window === 'undefined') {
    // Server-side: use simple ISO format to avoid timezone issues
    return date.toISOString().split('T')[0];
  }
  
  // Client-side: use full Intl formatting
  try {
    return new Intl.DateTimeFormat('en-US', options).format(date);
  } catch (error) {
    console.warn('Date formatting error:', error);
    return date.toISOString().split('T')[0];
  }
}

/**
 * Hydration-safe media query hook
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    
    if (!window.matchMedia) {
      return;
    }

    const mediaQuery = window.matchMedia(query);
    setMatches(mediaQuery.matches);

    const handler = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    mediaQuery.addEventListener('change', handler);
    return () => mediaQuery.removeEventListener('change', handler);
  }, [query]);

  return isClient ? matches : false;
}

/**
 * Hydration-safe component wrapper
 * Only renders children on client-side to prevent hydration mismatches
 */
export function ClientOnly({ children, fallback = null }: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

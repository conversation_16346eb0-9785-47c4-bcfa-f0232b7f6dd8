/**
 * Service Worker Provider
 * 
 * Manages service worker registration and provides caching utilities
 */

'use client';

import { useEffect, useState, createContext, useContext } from 'react';

interface ServiceWorkerContextType {
  isSupported: boolean;
  isRegistered: boolean;
  isOnline: boolean;
  registration: ServiceWorkerRegistration | null;
  updateAvailable: boolean;
  installUpdate: () => void;
  clearCache: () => Promise<void>;
  getCacheStats: () => Promise<Record<string, number>>;
}

const ServiceWorkerContext = createContext<ServiceWorkerContextType | null>(null);

export function useServiceWorker() {
  const context = useContext(ServiceWorkerContext);
  if (!context) {
    throw new Error('useServiceWorker must be used within ServiceWorkerProvider');
  }
  return context;
}

interface ServiceWorkerProviderProps {
  children: React.ReactNode;
}

export function ServiceWorkerProvider({ children }: ServiceWorkerProviderProps) {
  const [isSupported, setIsSupported] = useState(false);
  const [isRegistered, setIsRegistered] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null);
  const [updateAvailable, setUpdateAvailable] = useState(false);

  useEffect(() => {
    // Check if service workers are supported
    if ('serviceWorker' in navigator) {
      setIsSupported(true);
      registerServiceWorker();
    }

    // Set up online/offline detection
    setIsOnline(navigator.onLine);
    
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const registerServiceWorker = async () => {
    try {
      const reg = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
      });

      setRegistration(reg);
      setIsRegistered(true);

      console.log('Service Worker registered successfully:', reg);

      // Check for updates
      reg.addEventListener('updatefound', () => {
        const newWorker = reg.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // New update available
              setUpdateAvailable(true);
              console.log('New service worker update available');
            }
          });
        }
      });

      // Handle controller change (when new SW takes control)
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        console.log('Service Worker controller changed');
        window.location.reload();
      });

      // Check for existing update
      if (reg.waiting) {
        setUpdateAvailable(true);
      }

    } catch (error) {
      console.error('Service Worker registration failed:', error);
    }
  };

  const installUpdate = () => {
    if (registration?.waiting) {
      // Tell the waiting service worker to skip waiting and become active
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });
    }
  };

  const clearCache = async (): Promise<void> => {
    if (!registration) return;

    return new Promise((resolve) => {
      const messageChannel = new MessageChannel();
      
      messageChannel.port1.onmessage = (event) => {
        if (event.data.success) {
          console.log('Cache cleared successfully');
        }
        resolve();
      };

      registration.active?.postMessage(
        { type: 'CLEAR_CACHE' },
        [messageChannel.port2]
      );
    });
  };

  const getCacheStats = async (): Promise<Record<string, number>> => {
    if (!registration) return {};

    return new Promise((resolve) => {
      const messageChannel = new MessageChannel();
      
      messageChannel.port1.onmessage = (event) => {
        resolve(event.data || {});
      };

      registration.active?.postMessage(
        { type: 'GET_CACHE_STATS' },
        [messageChannel.port2]
      );
    });
  };

  const contextValue: ServiceWorkerContextType = {
    isSupported,
    isRegistered,
    isOnline,
    registration,
    updateAvailable,
    installUpdate,
    clearCache,
    getCacheStats,
  };

  return (
    <ServiceWorkerContext.Provider value={contextValue}>
      {children}
      {updateAvailable && <UpdateNotification onUpdate={installUpdate} />}
      {!isOnline && <OfflineNotification />}
    </ServiceWorkerContext.Provider>
  );
}

// Update notification component
function UpdateNotification({ onUpdate }: { onUpdate: () => void }) {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 max-w-sm">
      <div className="flex items-center justify-between">
        <div>
          <h4 className="font-semibold">Update Available</h4>
          <p className="text-sm opacity-90">A new version of the app is ready.</p>
        </div>
        <button
          onClick={() => setIsVisible(false)}
          className="ml-4 text-white/70 hover:text-white"
        >
          ×
        </button>
      </div>
      <div className="mt-3 flex gap-2">
        <button
          onClick={onUpdate}
          className="bg-white text-blue-600 px-3 py-1 rounded text-sm font-medium hover:bg-gray-100"
        >
          Update Now
        </button>
        <button
          onClick={() => setIsVisible(false)}
          className="text-white/70 px-3 py-1 rounded text-sm hover:text-white"
        >
          Later
        </button>
      </div>
    </div>
  );
}

// Offline notification component
function OfflineNotification() {
  return (
    <div className="fixed top-0 left-0 right-0 bg-yellow-500 text-black p-2 text-center z-50">
      <div className="flex items-center justify-center gap-2">
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path
            fillRule="evenodd"
            d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
            clipRule="evenodd"
          />
        </svg>
        <span className="text-sm font-medium">
          You're offline. Some features may be limited.
        </span>
      </div>
    </div>
  );
}

// Cache management hook
export function useCacheManager() {
  const { clearCache, getCacheStats } = useServiceWorker();
  const [stats, setStats] = useState<Record<string, number>>({});
  const [isLoading, setIsLoading] = useState(false);

  const refreshStats = async () => {
    setIsLoading(true);
    try {
      const newStats = await getCacheStats();
      setStats(newStats);
    } catch (error) {
      console.error('Failed to get cache stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearCache = async () => {
    setIsLoading(true);
    try {
      await clearCache();
      await refreshStats();
    } catch (error) {
      console.error('Failed to clear cache:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    refreshStats();
  }, []);

  return {
    stats,
    isLoading,
    refreshStats,
    clearCache: handleClearCache,
  };
}

// Performance monitoring hook
export function usePerformanceMonitoring() {
  const { isOnline } = useServiceWorker();
  const [metrics, setMetrics] = useState<{
    loadTime: number;
    cacheHitRate: number;
    offlineTime: number;
  }>({
    loadTime: 0,
    cacheHitRate: 0,
    offlineTime: 0,
  });

  useEffect(() => {
    // Monitor page load performance
    if (typeof window !== 'undefined' && 'performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        setMetrics(prev => ({
          ...prev,
          loadTime: navigation.loadEventEnd - navigation.navigationStart,
        }));
      }
    }

    // Track offline time
    let offlineStart: number | null = null;
    
    const handleOffline = () => {
      offlineStart = Date.now();
    };
    
    const handleOnline = () => {
      if (offlineStart) {
        const offlineTime = Date.now() - offlineStart;
        setMetrics(prev => ({
          ...prev,
          offlineTime: prev.offlineTime + offlineTime,
        }));
        offlineStart = null;
      }
    };

    window.addEventListener('offline', handleOffline);
    window.addEventListener('online', handleOnline);

    return () => {
      window.removeEventListener('offline', handleOffline);
      window.removeEventListener('online', handleOnline);
    };
  }, []);

  return {
    metrics,
    isOnline,
  };
}

export default ServiceWorkerProvider;

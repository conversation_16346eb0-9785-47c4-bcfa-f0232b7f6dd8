Product Requirements Document – v2
Project Overview
The existing site is an initial implementation of the Bali Real Estate platform described in the first PRD. The current build includes attractive, content‑rich pages for a few high priority location/property combinations (such as /seminyak/villa/long‑term‑rental and /ubud/villa/long‑term‑rental) but several critical behaviours have been misinterpreted or left unfinished. Important calls‑to‑action do nothing, navigation links lead to 404 pages and the AI search experience promised in the original specification is completely absent. This document summarises the gaps between the original requirements and the current website, and defines the scope for the next development phase.
Purpose of this revision
•	Align the implementation with the original vision of an AI‑assisted property search experience. The new requirement is that whenever a visitor wants to search for properties (via buttons such as View Available Properties, Get Personalized Recommendations or Start Your Bali Journey) a chatbot should launch in a dedicated /chatbot route with the appropriate query parameter. For example, clicking View Available Properties on the Seminyak villa rental page should navigate to /chatbot?query=rent‑villa‑in‑seminyak.
•	Preserve the existing contact/consultation page (currently at /contact) and its forms. These forms should continue to capture lead information and submit to the Leads collection defined in the CMS. They should not be removed or replaced by the chatbot.
•	Clarify that other navigation links (Properties, About, Services) are placeholders until their pages are built. Until then they should not navigate to non‑existent routes or cause 404 errors.
This revision also incorporates some of the AI‑integration specifications from the AI PRD. Contextual triggers and AI agent CTAs were meant to be embedded throughout the pages, but they are missing in the current build. The next phase must add these triggers and hook them to the new chatbot route.
Evaluation of the current site
Area	Findings	Deviation from original PRD
Home page	The landing page features a hero heading and lists “High Priority Locations & Property Types.” However, the Start Your Bali Journey form is incomplete; inputs such as “Monthly budget” and “Timeline” exist but the submission button does nothing. There is no AI search widget.	The original PRD required strategic contact forms and CTAs on every page, and the AI PRD specified an AI‑agent CTA in the hero.
Location/property pages	Pages like /seminyak/villa/long‑term‑rental and /canggu/villa/long‑term‑rental have comprehensive content sections (neighbourhood overview, price ranges, lifestyle, expat community, transportation, FAQ, etc.). However, the buttons View Available Properties, Schedule Viewing and Get Personalized Recommendations are not interactive – they simply scroll or do nothing.	The PRD required CTAs to contact experts and integrate a chatbot to start searches. The missing behaviour breaks the lead generation flow.
Navigation	The header contains dropdowns for Locations and Properties and links for Services, About and Contact. Many of these lead to 404 pages (e.g., /properties/villa/long‑term‑rental), causing a poor user experience.	The original PRD defined a page architecture with pages/index.js, dynamic location pages, property‑type pages and combination pages. Unimplemented links should not break navigation.
Contact page	A dedicated contact/consultation page exists at /contact. It contains a hero section, a Free Consultation Request form that collects name, email, phone, timeline and message, and displays contact details. This page functions as expected and should be retained.	The PRD specified several types of lead generation forms. This form satisfies the “General consultation request” type and should remain unchanged.
AI agent	No chatbot or AI search interface is present. There is no /chatbot route.	The AI PRD defined contextual triggers for the AI agent (e.g., “Find [property type] in [location]” and “Start your [location] [property type] search”). These triggers are absent, and users cannot search for properties via the bot.
Internal linking & SEO	Some internal links exist (for example, location names in the footer), but the site lacks the hub‑and‑spoke architecture described in the AI PRD.	The absence of location and property‑type index pages means visitors cannot browse all options.
Performance & accessibility	Core Web Vitals have not been measured yet. Images are not always optimized and there is no lazy loading. Many sections rely solely on long paragraphs; headings and ARIA labels are missing in places.	The PRD emphasises technical SEO, image optimization and accessibility.
Updated objectives
1.	Activate AI search – integrate a chatbot accessible via a new route (/chatbot) that accepts a query parameter. All property search CTAs must call this route. The chatbot should interpret the query (e.g., rent‑villa‑in‑canggu) and start a conversation that captures budget, timeline, location and property preferences.
2.	Stabilise navigation – ensure that links in the header either point to existing pages or remain disabled. Provide placeholder pages for Properties, Services and About with a short message such as “Content coming soon” until full content is developed.
3.	Preserve and enhance lead forms – keep the /contact page in its current form, but ensure that submissions save to the Leads collection with fields for name, email, phone, budget, timeline, property type and location preferences. Forms on other pages should send users to the chatbot instead of posting data directly.
4.	Complete index pages – implement /locations/index.tsx and /property‑types/index.tsx pages that list all available locations and property categories. This satisfies the hub‑and‑spoke architecture and prevents 404 errors when users select links in the navigation.
5.	Ensure accessibility and SEO – add alt text to every image, use semantic HTML (h1, h2, lists) and implement lazy loading. Generate dynamic meta titles and descriptions as described in the PRD.
Functional requirements
1. AI chatbot integration
•	Route definition: Create a new file pages/chatbot/index.tsx that renders the chat interface. It should read the query parameter from router.query and display the query to the user. When a page sends the user to /chatbot?query=rent‑villa‑in‑canggu, the chatbot should use this string to pre‑fill its initial message.
•	Trigger points: Add event handlers to all search‑related CTAs. For example:
// Example button inside a property combination page component
import { useRouter } from 'next/router';

function ViewPropertiesButton({ location, propertyType }: { location: string; propertyType: string }) {
  const router = useRouter();
  const handleClick = () => {
    // build a query string like "rent‑villa‑in‑canggu"
    const slug = `rent-${propertyType}-in-${location}`;
    router.push(`/chatbot?query=${slug}`);
  };
  return (
    <button onClick={handleClick} className="btn btn-primary">
      View Available Properties
    </button>
  );
}
•	Contextual queries: For location pages, the query should reflect the page context. Example: the Canggu location page might have a CTA “Find villas in Canggu” that calls /chatbot?query=rent‑villa‑in‑canggu. On a property‑type page such as Villas for Sale, the query could be buy‑villa‑in‑bali. The triggers correspond to the contextual prompts defined in the AI PRD.
•	Chatbot conversation flow: The chat interface should follow the engagement and profiling flow defined in the AI specification – start by collecting budget, duration, property type and location preferences. Once the user provides enough details, the bot should retrieve matching properties from the CMS or external APIs and present them. This behaviour will be implemented in a later phase when the property database is available.
2. Navigation and page structure
•	Header links: Modify the header component so that each menu item maps to an existing page. Until property‑type pages and the blog are implemented, the Properties, Services and About links should point to placeholder pages under /coming‑soon. These pages can display a message and a link back to the homepage. Do not link to missing routes to avoid 404 pages.
•	Locations index: Implement pages/locations/index.tsx that lists all locations stored in the CMS. Each item should link to its individual page (e.g., /locations/canggu).
•	Property‑types index: Implement pages/property‑types/index.tsx that lists all property categories (Villas, Guesthouses, Apartments, Land) as defined in the PRD. Each category card should link to its dedicated page when available or to a chatbot query when not.
•	Contact page: Leave pages/contact/index.tsx unchanged. Ensure form validation and success messages work. Persist data to the CMS by posting to an API route (/api/lead) that creates a new entry in the Leads collection.
3. Content and SEO improvements
•	Content length and sections: Maintain the detailed sections on each location/combination page (overview, neighborhoods, price ranges, lifestyle, expat community, transportation, FAQ). Ensure each section meets the word counts specified in the AI PRD (2 000–2 500 words for service area pages and 1 500–2 000 words for combination pages).
•	Internal linking: Use the hub‑and‑spoke model described in the AI PRD. Each location page should link to its related property types, to relevant educational articles and to nearby locations. Each property‑type page should link back to all locations where that type is available.
•	Meta data: Generate dynamic titles and descriptions. For example: title = \${'${propertyType}'} in ${'${location}'}, Bali | 2025`;`. The description should summarise the offer and include a call to action.
•	Structured data: Include RealEstateListing and LocalBusiness schema on pages with property information.
•	Accessibility: Use semantic elements (section, article, nav, header, footer), ensure buttons have accessible names and labels, and add alt text to every image. Implement lazy loading and the Next.js <Image> component to optimise images.
4. Lead management and forms
•	Leads collection: Ensure that all forms submit to the Leads collection with fields: name, email, phone, budget, timeline, propertyType, location and message. Capture the source (e.g., contact-form or chatbot) for analytics.
•	Chatbot hand‑off: After the chatbot provides property matches, it should ask whether the user wants to be contacted. If the user agrees, the chatbot should create a lead in the Leads collection via an API route. The lead score can be calculated based on budget alignment, timeline urgency, location specificity and property type clarity.
•	Email notifications: Send an automated confirmation email to the user and notify the sales team when a new lead is created. Use a transactional email service (SendGrid, Resend) configured via environment variables.
Non‑functional requirements
Category	Requirement
Performance	Achieve Core Web Vitals targets (LCP < 1.5 s, FID < 100 ms, CLS < 0.1). Use incremental static regeneration and caching to improve load times.
Security	Validate and sanitise all form inputs, implement rate limiting on API routes and protect sensitive environment variables.
Scalability	Design the chatbot to support additional locations and property types without code changes. Use a generic slug system for query parameters.
Internationalisation	Provide at least English and Dutch translations for UI text. Structure content in the CMS to allow multi‑language fields.
Analytics	Integrate Google Analytics and track events for chatbot interactions, form submissions and page views. Monitor AI interaction rate as defined in the AI PRD’s KPIs.
Acceptance criteria
1.	CTA navigates to chatbot: When a user clicks any View Available Properties, Get Personalized Recommendations, Start your [location] search or similar CTA, the site navigates to /chatbot?query=<context> using the current location and property type. The URL contains the correct slug with hyphens (no spaces). The chatbot page loads without a full page refresh.
2.	Chatbot page loads: Visiting /chatbot?query=rent‑villa‑in‑ubud displays the chatbot interface, shows the query string and begins a guided conversation to collect preferences. There is a button to return to the previous page.
3.	Navigation does not break: The header dropdowns display all available locations, and selecting an item leads to its page. The Properties, Services and About links lead to placeholder pages until full implementations are delivered. No menu item triggers a 404 error.
4.	Contact forms work: Submitting the form on /contact successfully stores a new record in the Leads collection with all required fields and sends an email confirmation. Validation errors are shown inline.
5.	Content completeness: Each service area and combination page includes all required sections with the minimum word counts and internal links. Pages have dynamic meta titles and descriptions, alt text for images and structured data.
6.	Accessibility and performance: The site scores above 90 on Lighthouse for performance and accessibility, with Core Web Vitals in the green zone. Images load lazily and the layout is responsive.
Next steps
1.	Implement /chatbot route and develop a simple chat UI. For the next sprint, the chatbot can return canned responses based on the query; property matching can be integrated later using CMS data or external APIs.
2.	Refactor CTAs across all pages so they call the chatbot route. Remove or hide any buttons that do not perform an action.
3.	Create placeholder pages for missing sections and adjust navigation. This prevents 404 errors while development continues.
4.	Update CMS and API routes to capture leads and to support multi‑language content fields.
5.	Perform an SEO and performance audit after the changes and address any issues before launch.
This PRD revision ensures that the next development phase focuses on core user flows (search via chatbot, lead capture via contact page), stabilises navigation and prepares the groundwork for full AI‑powered property matching. By following this specification the team can deliver an interactive and conversion‑optimised experience that aligns with the original business objectives.
________________________________________

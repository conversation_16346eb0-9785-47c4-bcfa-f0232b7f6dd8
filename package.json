{"name": "bali-property-scout-website", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env PAYLOAD_CONFIG_PATH=payload.config.ts next dev", "build": "node vercel-build.js", "build:local": "cross-env PAYLOAD_CONFIG_PATH=payload.config.ts next build", "start": "cross-env PAYLOAD_CONFIG_PATH=payload.config.ts next start", "lint": "eslint", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "analyze": "cross-env ANALYZE=true npm run build", "analyze:deps": "node scripts/analyze-dependencies.js", "lighthouse": "lighthouse http://localhost:3001 --output=json --output=html --output-path=./lighthouse-report", "perf:audit": "node scripts/performance-audit.js", "perf:monitor": "npm run analyze:deps && npm run analyze", "a11y:audit": "node scripts/accessibility-audit.js", "a11y:lint": "eslint src --ext .tsx,.ts --config eslint.config.mjs", "test:a11y": "npm run a11y:lint && npm run a11y:audit", "seo:audit": "node scripts/seo-audit.js", "seo:sitemap": "npm run build && curl http://localhost:3001/sitemap.xml", "seo:robots": "npm run build && curl http://localhost:3001/robots.txt", "test:seo": "npm run seo:audit", "perf:lighthouse": "lighthouse http://localhost:3001 --output=json --output-path=./performance-reports/lighthouse-report.json", "perf:bundle": "npm run analyze", "test:perf": "npm run perf:audit", "test:ci": "jest --ci --coverage --watchAll=false", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:all": "npm run test:ci && npm run test:e2e", "test:unit": "jest --testPathPattern=src/", "test:integration": "jest --testPathPattern=tests/integration/", "analyze:server": "cross-env BUNDLE_ANALYZE=server npm run build", "analyze:browser": "cross-env BUNDLE_ANALYZE=browser npm run build", "payload": "cross-env PAYLOAD_CONFIG_PATH=payload.config.ts payload", "generate:types": "cross-env PAYLOAD_CONFIG_PATH=payload.config.ts payload generate:types", "seed": "cross-env PAYLOAD_CONFIG_PATH=payload.config.ts tsx src/seed/index.ts", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@next/third-parties": "^15.5.0", "@payloadcms/db-mongodb": "^3.52.0", "@payloadcms/db-postgres": "^3.52.0", "@payloadcms/plugin-form-builder": "^3.52.0", "@payloadcms/plugin-seo": "^3.52.0", "@payloadcms/richtext-lexical": "^3.52.0", "@tailwindcss/postcss": "^4", "@types/uuid": "^10.0.0", "chart.js": "^4.5.0", "clsx": "^2.1.1", "critters": "^0.0.23", "cross-env": "^10.0.0", "dotenv": "^17.2.1", "gtag": "^1.0.1", "markdown-to-jsx": "^7.7.13", "mongoose": "^8.17.2", "motion": "^12.23.12", "next": "15.5.0", "next-seo": "^6.8.0", "payload": "^3.52.0", "react": "19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "19.1.0", "sharp": "^0.34.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4", "uuid": "^11.1.0", "web-vitals": "^5.1.0"}, "devDependencies": {"@axe-core/react": "^4.10.2", "@chromatic-com/storybook": "^4.1.1", "@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.5.0", "@playwright/test": "^1.55.0", "@storybook/addon-a11y": "^9.1.2", "@storybook/addon-docs": "^9.1.2", "@storybook/addon-onboarding": "^9.1.2", "@storybook/addon-vitest": "^9.1.2", "@storybook/nextjs-vite": "^9.1.2", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^5.0.1", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "axe-playwright": "^2.1.0", "chrome-launcher": "^1.2.0", "eslint": "^9", "eslint-config-next": "15.5.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-storybook": "^9.1.2", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "jsdom": "^26.1.0", "lighthouse": "^12.8.1", "playwright": "^1.55.0", "storybook": "^9.1.2", "tsx": "^4.20.4", "typescript": "^5", "vitest": "^3.2.4", "webpack-bundle-analyzer": "^4.10.2"}}
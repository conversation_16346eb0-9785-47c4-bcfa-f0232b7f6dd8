import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { PerformanceMonitor } from "@/components/performance/PerformanceMonitor";
import { WebVitals } from "@/components/analytics/WebVitals";
import { GoogleAnalytics } from '@/components/analytics/GoogleAnalytics';
import { OrganizationStructuredData, WebsiteStructuredData } from '@/components/seo/StructuredData';
import { ServiceWorkerProvider } from '@/components/performance/ServiceWorkerProvider';
import { CookieConsent } from '@/components/common/CookieConsent';
import { ConversionTracking } from '@/components/analytics/ConversionTracking';
import { DEFAULT_SEO } from '@/lib/seo';
import { DefaultSeo } from 'next-seo';
import { Suspense } from 'react';
// AI components temporarily disabled for stability

const inter = Inter({
  subsets: ["latin"],
  display: 'swap',
  preload: true,
});

// Global metadata - individual pages can override
export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://bali-real-estate.com'),
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" dir="ltr">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="theme-color" content="#059669" />
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
      </head>
      <body className={`${inter.className} antialiased`}>
        <ServiceWorkerProvider>
          {/* <DefaultSeo {...DEFAULT_SEO} /> */}
          <OrganizationStructuredData />
          <WebsiteStructuredData />
          <PerformanceMonitor enabled={true} sampleRate={1.0} />
          {/* Enhanced Web Vitals tracking for production */}
          <GoogleAnalytics />
          <Header />
          {children}
          <Footer />
          {/* AI Chat Widget - Temporarily disabled for stability */}
          {/* Cookie Consent Banner - PRD Requirement */}
          <CookieConsent />
          {/* Advanced Conversion Tracking - STORY-007 */}
          <Suspense fallback={null}>
            <ConversionTracking />
          </Suspense>
        </ServiceWorkerProvider>
      </body>
    </html>
  );
}

/**
 * RichText Component
 * 
 * Flexible rich text component that supports markdown, HTML, and custom styling.
 * Includes typography variants and responsive design.
 */

import React from 'react';
import { cn } from '@/lib/utils';

export interface RichTextProps {
  /** Content to render - can be HTML string, markdown, or React nodes */
  content?: string | React.ReactNode;
  /** Typography variant */
  variant?: 'body' | 'lead' | 'small' | 'caption';
  /** Text size */
  size?: 'sm' | 'md' | 'lg' | 'xl';
  /** Text color */
  color?: 'default' | 'muted' | 'primary' | 'secondary';
  /** Whether to enable markdown parsing */
  markdown?: boolean;
  /** Whether to sanitize HTML content */
  sanitize?: boolean;
  /** Custom className */
  className?: string;
  /** Maximum number of lines to show (truncation) */
  maxLines?: number;
  /** Whether to show read more/less toggle */
  expandable?: boolean;
  /** Custom styling for specific elements */
  elementStyles?: {
    h1?: string;
    h2?: string;
    h3?: string;
    h4?: string;
    h5?: string;
    h6?: string;
    p?: string;
    ul?: string;
    ol?: string;
    li?: string;
    blockquote?: string;
    code?: string;
    pre?: string;
    a?: string;
    strong?: string;
    em?: string;
  };
}

// Typography variant styles
const variantStyles = {
  body: 'text-base leading-relaxed',
  lead: 'text-lg leading-relaxed font-medium',
  small: 'text-sm leading-normal',
  caption: 'text-xs leading-tight',
};

// Size styles
const sizeStyles = {
  sm: 'text-sm',
  md: 'text-base',
  lg: 'text-lg',
  xl: 'text-xl',
};

// Color styles
const colorStyles = {
  default: 'text-neutral-900',
  muted: 'text-neutral-600',
  primary: 'text-primary',
  secondary: 'text-secondary',
};

// Default element styles
const defaultElementStyles = {
  h1: 'text-4xl font-bold mb-6 text-neutral-900',
  h2: 'text-3xl font-bold mb-5 text-neutral-900',
  h3: 'text-2xl font-semibold mb-4 text-neutral-900',
  h4: 'text-xl font-semibold mb-3 text-neutral-900',
  h5: 'text-lg font-semibold mb-3 text-neutral-900',
  h6: 'text-base font-semibold mb-2 text-neutral-900',
  p: 'mb-4 last:mb-0',
  ul: 'list-disc list-inside mb-4 space-y-1',
  ol: 'list-decimal list-inside mb-4 space-y-1',
  li: 'text-neutral-700',
  blockquote: 'border-l-4 border-primary-light pl-4 py-2 mb-4 italic text-neutral-700 bg-neutral-50',
  code: 'bg-neutral-100 text-neutral-800 px-1.5 py-0.5 rounded text-sm font-mono',
  pre: 'bg-neutral-900 text-neutral-100 p-4 rounded-lg mb-4 overflow-x-auto',
  a: 'text-primary hover:text-primary-dark underline transition-colors',
  strong: 'font-semibold text-neutral-900',
  em: 'italic',
};

// Simple markdown parser (basic implementation)
const parseMarkdown = (content: string): string => {
  return content
    // Headers
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    // Bold
    .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
    .replace(/__(.*?)__/gim, '<strong>$1</strong>')
    // Italic
    .replace(/\*(.*)\*/gim, '<em>$1</em>')
    .replace(/_(.*?)_/gim, '<em>$1</em>')
    // Links
    .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2">$1</a>')
    // Code
    .replace(/`([^`]+)`/gim, '<code>$1</code>')
    // Line breaks
    .replace(/\n/gim, '<br>');
};

// HTML sanitizer (basic implementation)
const sanitizeHtml = (html: string): string => {
  // Remove script tags and event handlers
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/on\w+="[^"]*"/gi, '')
    .replace(/on\w+='[^']*'/gi, '')
    .replace(/javascript:/gi, '');
};

export const RichText: React.FC<RichTextProps> = React.memo(({
  content,
  variant = 'body',
  size,
  color = 'default',
  markdown = false,
  sanitize = true,
  className,
  maxLines,
  expandable = false,
  elementStyles = {},
}) => {
  const [isExpanded, setIsExpanded] = React.useState(false);

  // Merge custom element styles with defaults
  const mergedElementStyles = { ...defaultElementStyles, ...elementStyles };

  // Process content
  const processedContent = React.useMemo(() => {
    if (!content) return '';
    
    if (typeof content !== 'string') {
      return content;
    }

    let processed = content;

    // Parse markdown if enabled
    if (markdown) {
      processed = parseMarkdown(processed);
    }

    // Sanitize HTML if enabled
    if (sanitize && typeof processed === 'string') {
      processed = sanitizeHtml(processed);
    }

    return processed;
  }, [content, markdown, sanitize]);

  // Apply element styles to HTML content
  const styledContent = React.useMemo(() => {
    if (typeof processedContent !== 'string') {
      return processedContent;
    }

    let styled = processedContent;

    // Apply styles to each element type
    Object.entries(mergedElementStyles).forEach(([tag, styles]) => {
      const regex = new RegExp(`<${tag}([^>]*)>`, 'gi');
      styled = styled.replace(regex, (match, attributes) => {
        const existingClass = attributes.match(/class="([^"]*)"/);
        const newClass = existingClass 
          ? `class="${existingClass[1]} ${styles}"`
          : `class="${styles}"`;
        
        if (existingClass) {
          return match.replace(existingClass[0], newClass);
        } else {
          return `<${tag} ${newClass}${attributes}>`;
        }
      });
    });

    return styled;
  }, [processedContent, mergedElementStyles]);

  // Handle truncation
  const shouldTruncate = maxLines && !isExpanded;
  const truncationClass = shouldTruncate 
    ? `line-clamp-${maxLines}` 
    : '';

  // Base classes
  const baseClasses = cn(
    // Variant styles (only if no size is specified)
    !size && variantStyles[variant],
    // Size styles (override variant if specified)
    size && sizeStyles[size],
    // Color styles
    colorStyles[color],
    // Truncation
    truncationClass,
    // Custom className
    className
  );

  // Render content
  const renderContent = () => {
    if (typeof styledContent === 'string') {
      return (
        <div
          className={baseClasses}
          dangerouslySetInnerHTML={{ __html: styledContent }}
        />
      );
    }

    return (
      <div className={baseClasses}>
        {styledContent}
      </div>
    );
  };

  return (
    <div className="rich-text">
      {renderContent()}
      
      {/* Expand/Collapse toggle */}
      {expandable && maxLines && (
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="mt-2 text-primary hover:text-primary-dark text-sm font-medium transition-colors"
        >
          {isExpanded ? 'Show less' : 'Read more'}
        </button>
      )}
    </div>
  );
});

RichText.displayName = 'RichText';

// Specialized RichText variants
export const RichTextLead: React.FC<Omit<RichTextProps, 'variant'>> = (props) => (
  <RichText {...props} variant="lead" />
);

export const RichTextSmall: React.FC<Omit<RichTextProps, 'variant'>> = (props) => (
  <RichText {...props} variant="small" />
);

export const RichTextCaption: React.FC<Omit<RichTextProps, 'variant'>> = (props) => (
  <RichText {...props} variant="caption" />
);

// Markdown-specific component
export const Markdown: React.FC<Omit<RichTextProps, 'markdown'>> = (props) => (
  <RichText {...props} markdown={true} />
);

// HTML content component
export const HtmlContent: React.FC<Omit<RichTextProps, 'markdown' | 'sanitize'>> = (props) => (
  <RichText {...props} markdown={false} sanitize={true} />
);

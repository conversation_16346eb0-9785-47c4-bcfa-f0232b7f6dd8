# Bali Real Estate Website - Technical Architecture

**Version:** 1.0  
**Date:** Augustus 2025  
**Tech Stack:** Next.js 15 + Payload CMS + Vercel

---

## System Overview

The Bali Real Estate website is built as a modern, SEO-optimized platform using a headless CMS architecture with Next.js frontend and Payload CMS backend, deployed on Vercel for optimal performance and scalability.

### Architecture Principles
- **Headless CMS:** Separation of content management and presentation
- **JAMstack:** JavaScript, APIs, and Markup for performance
- **SEO-First:** Built-in optimization for search engines
- **Mobile-First:** Responsive design with mobile optimization
- **Performance:** Core Web Vitals compliance

---

## Tech Stack Components

### Frontend Layer
- **Framework:** Next.js 15 with App Router
- **Language:** TypeScript for type safety
- **Styling:** Tailwind CSS for utility-first styling
- **Components:** React functional components with hooks
- **State Management:** React Context and hooks for local state

### Backend/CMS Layer
- **CMS:** Payload CMS 3.x for content management
- **Database:** MongoDB for data persistence
- **Rich Text:** Lexical editor for content creation
- **File Storage:** Local file system with Vercel static hosting
- **API:** RESTful APIs auto-generated by Payload

### Deployment & Infrastructure
- **Hosting:** Vercel for frontend and API routes
- **CDN:** Vercel Edge Network for global distribution
- **Database:** MongoDB Atlas for production
- **Domain:** Custom domain with SSL
- **Analytics:** Google Analytics 4 and Search Console

---

## Data Architecture

### Content Collections

#### Locations Collection
```typescript
interface Location {
  id: string
  name: string
  slug: string
  priority: 'zeer-hoog' | 'hoog' | 'middel' | 'laag'
  description: RichText
  lifestyle: RichText
  expatCommunity: RichText
  transportation: RichText
  practicalInfo: RichText
  priceRange: {
    rentalMin: number
    rentalMax: number
    saleMin: number
    saleMax: number
  }
  amenities: string[]
  neighborhoods: Array<{
    name: string
    description: string
    bestFor: string
  }>
  coordinates: Point
  featuredImage: Media
  gallery: Array<{
    image: Media
    caption: string
  }>
  faq: Array<{
    question: string
    answer: RichText
  }>
  seo: SEOFields
}
```

#### PropertyTypes Collection
```typescript
interface PropertyType {
  id: string
  name: string
  slug: string
  category: 'rental' | 'sale' | 'both'
  description: RichText
  typicalFeatures: string[]
  sizeRanges: {
    bedroomsMin: number
    bedroomsMax: number
    areaMin: number
    areaMax: number
  }
  priceRange: PriceRange
  targetAudience: Array<'digital-nomads' | 'expat-families' | 'property-investors' | 'retirees' | 'surfers' | 'budget-travelers'>
  rentalTerms: RichText
  purchaseProcess: RichText
  availableLocations: Location[]
  featuredImage: Media
  gallery: MediaGallery[]
  faq: FAQ[]
  seo: SEOFields
}
```

#### Content Collection
```typescript
interface Content {
  id: string
  title: string
  slug: string
  excerpt: string
  category: 'regulatory' | 'location-comparison' | 'process-financial' | 'lifestyle-practical' | 'market-analysis' | 'legal-advice'
  priority: Priority
  content: RichText
  tableOfContents: Array<{
    heading: string
    anchor: string
  }>
  author: string
  publishDate: Date
  lastUpdated: Date
  status: 'draft' | 'published' | 'archived'
  readingTime: number
  relatedLocations: Location[]
  relatedPropertyTypes: PropertyType[]
  relatedContent: Content[]
  featuredImage: Media
  gallery: MediaGallery[]
  keyTakeaways: string[]
  faq: FAQ[]
  targetKeywords: Array<{
    keyword: string
    priority: 'primary' | 'secondary' | 'long-tail'
  }>
  seo: SEOFields
}
```

#### Leads Collection
```typescript
interface Lead {
  id: string
  name: string
  email: string
  phone?: string
  leadType: 'general' | 'property-interest' | 'consultation' | 'newsletter'
  status: 'new' | 'contacted' | 'qualified' | 'converted' | 'closed'
  leadScore: number // 0-50
  source: string
  budget: {
    budgetRange: string
    budgetFlexible: boolean
  }
  timeline: string
  propertyPreferences: {
    propertyType: PropertyType[]
    preferredLocations: Location[]
    transactionType: 'rental' | 'purchase' | 'both' | 'undecided'
  }
  personalInfo: {
    currentLocation: string
    occupation: string
    familySize: number
    previousBaliExperience: boolean
  }
  message: string
  notes: Array<{
    note: string
    author: string
    date: Date
  }>
  emailOptIn: boolean
  gdprConsent: boolean
  lastContactDate?: Date
  nextFollowUpDate?: Date
  createdAt: Date
  updatedAt: Date
}
```

---

## API Architecture

### Payload CMS REST API
- **Base URL:** `/api`
- **Collections:** Auto-generated CRUD endpoints
- **Authentication:** JWT-based for admin access
- **Rate Limiting:** Built-in protection
- **Caching:** Response caching for performance

### Next.js API Routes
- **Contact Forms:** `/api/contact` for lead submission
- **Email Sending:** `/api/send-email` for notifications
- **Analytics:** `/api/analytics` for tracking
- **Sitemap:** `/api/sitemap.xml` for SEO

---

## Frontend Architecture

### Page Structure
```
src/app/
├── page.tsx                    # Homepage
├── layout.tsx                  # Root layout
├── globals.css                 # Global styles
├── locations/
│   ├── page.tsx               # Locations overview
│   └── [slug]/
│       └── page.tsx           # Individual location pages
├── property-types/
│   ├── page.tsx               # Property types overview
│   └── [slug]/
│       └── page.tsx           # Individual property type pages
├── [location]/
│   └── [property-type]/
│       └── [transaction]/
│           └── page.tsx       # Combination pages
├── guides/
│   ├── page.tsx               # Content hub
│   └── [slug]/
│       └── page.tsx           # Individual guides
├── contact/
│   └── page.tsx               # Contact page
└── api/
    └── [[...slug]]/
        └── route.ts           # Payload CMS API
```

### Component Architecture
```
src/components/
├── ui/                        # Basic UI components
│   ├── Button.tsx
│   ├── Card.tsx
│   ├── Form.tsx
│   └── Modal.tsx
├── layout/                    # Layout components
│   ├── Header.tsx
│   ├── Footer.tsx
│   ├── Navigation.tsx
│   └── Breadcrumbs.tsx
├── content/                   # Content components
│   ├── RichText.tsx
│   ├── Gallery.tsx
│   ├── FAQ.tsx
│   └── RelatedContent.tsx
├── forms/                     # Form components
│   ├── ContactForm.tsx
│   ├── PropertyInterestForm.tsx
│   └── ConsultationForm.tsx
└── seo/                       # SEO components
    ├── MetaTags.tsx
    ├── StructuredData.tsx
    └── OpenGraph.tsx
```

---

## SEO Architecture

### Technical SEO Implementation
- **Dynamic Meta Tags:** Generated from CMS content
- **Structured Data:** JSON-LD for RealEstateListing, LocalBusiness
- **XML Sitemap:** Auto-generated from CMS collections
- **Robots.txt:** Optimized for search engine crawling
- **Canonical URLs:** Prevent duplicate content issues

### Performance Optimization
- **Image Optimization:** Next.js Image component with WebP
- **Code Splitting:** Dynamic imports for large components
- **Lazy Loading:** Below-fold content loading
- **Caching:** ISR for static content, SWR for dynamic data
- **CDN:** Vercel Edge Network for global distribution

---

## Security Architecture

### Data Protection
- **GDPR Compliance:** Consent management and data protection
- **Input Validation:** Server-side validation for all forms
- **Rate Limiting:** API protection against abuse
- **HTTPS:** SSL encryption for all communications
- **Environment Variables:** Secure configuration management

### Authentication & Authorization
- **Admin Access:** JWT-based authentication for CMS
- **Role-based Access:** Admin, Editor, User roles
- **Session Management:** Secure session handling
- **Password Security:** Bcrypt hashing for passwords

---

## Deployment Architecture

### Vercel Configuration
- **Build Command:** `npm run build`
- **Output Directory:** `.next`
- **Node.js Version:** 18.x
- **Environment Variables:** Secure configuration
- **Preview Deployments:** Branch-based previews

### CI/CD Pipeline
- **Git Integration:** Automatic deployments from main branch
- **Build Process:** TypeScript compilation and optimization
- **Testing:** Automated testing before deployment
- **Rollback:** Easy rollback to previous versions

This architecture provides a solid foundation for a scalable, performant, and SEO-optimized real estate website that can grow with business needs while maintaining excellent user experience and search engine visibility.
